// constants.js
const BOOKING_TYPES = {
  CLUB_COURT: 1,
  CLINIC: 2,
  COACH: 3,
  };

  const RESERVATION_TYPES = {
    CLUB_COURT: 1,
    CLINIC: 2,
    LESSONS: 3,
    COACH: 4,
    };
  
  const BOOKING_STATUSES = {
    SUCCESS: 1,
    PENDING: 0,
    FAIL: 2,
    CANCELLED: 3
  };
  
  const ERROR_MESSAGES = {
    USER_NOT_FOUND: "User is missing.",
    PLAYER_IDS_NOT_FOUND: "Player ids is missing.",
    BUDDY_ID_NOT_FOUND: "Buddy id is missing.",
    NUM_PLAYERS_EXCEEDED: "Number of players exceeded.",
    STATUS_NOT_FOUND: "Status is missing.",
    SUBSCRIPTION_NOT_FOUND: "Subscription is missing.",
    CLUB_NOT_FOUND: "Club not found",
    REQUEST_NOT_FOUND: "Request is missing.",
    SLOTS_NOT_FOUND: "Slots is missing.",
    PLAYER_ALREADY_JOINED: "Player already requested.",
    SPORT_ID_NOT_FOUND: "Sport id is missing.",
    WEEKLY_RESERVATION_LIMIT_REACHED: "Weekly reservation limit reached.",
    MISSING_NAME: "Missing name parameter",
    GROUP_NOT_FOUND: "Group not found",
    BUDDY_NOT_FOUND: "Buddy is missing.",
    REQUEST_NOT_FOUND: "Request is missing.",
    USER_ALREADY_JOINED: "User already joined",
    RESERVATION_NOT_FOUND: "Reservation is missing.",
    BOOKING_NOT_FOUND: "Booking is missing.",
    BOOKING_ID_NOT_FOUND: "Booking id is missing.",
    PAYMENT_ERROR: "Error while creating payment intent.",
    BOOKING_ERROR: "Error while creating booking.",
    PAYMENT_VERIFICATION_ERROR: "Error while verifying payment.",
    BOOKING_VERIFICATION_ERROR: "Error while verifying booking.",
    REQUEST_ID_NOT_FOUND: "Request id is missing.",
    COACH_NOT_FOUND: "Coach is missing.",
    COURT_NOT_FOUND: "Court is missing.",
    CLINIC_NOT_FOUND: "Clinic is missing.",
    CLINIC_REGISTRATION_ERROR: "Error while registering for clinic.",
    COACH_REGISTRATION_ERROR: "Error while registering for coach.",
    CLINIC_REGISTRATION_VERIFICATION_ERROR: "Error while verifying clinic registration.",
    COACH_REGISTRATION_VERIFICATION_ERROR: "Error while verifying coach registration.",
    NTRP_NOT_FOUND: "NTRP is missing.",
    NUM_PLAYERS_NOT_FOUND: "Number of players is missing.",
    NUM_NEEDED_NOT_FOUND: "Number needed is missing.",
    TYPE_NOT_FOUND: "Type is missing.",
    START_TIME_NOT_FOUND: "Start time is missing.",
    END_TIME_NOT_FOUND: "End time is missing.",
    DATE_NOT_FOUND: "Date is missing.",
  };
  
  module.exports = { BOOKING_TYPES, BOOKING_STATUSES, ERROR_MESSAGES, RESERVATION_TYPES };
  