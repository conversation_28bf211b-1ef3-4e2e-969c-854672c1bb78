const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const { filterEmptyFieldsAllowEmptyString } = require("../utils/util");
const PasswordService = require("../../../services/PasswordService");
const { validate_availability, validate_account } = require("../utils/util");
const MailService = require("../../../services/MailService");
const MkdEventService = require("../../../services/MkdEventService");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({
    role: "club",
  }),
];
const adminMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({
    role: "admin|admin_staff",
  }),
];


module.exports = function (app) {
  app.post("/v3/api/custom/courtmatchup/club/update-password", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result
        });
      }
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }
      const hash = result[0].password;
      const passwordValid = await PasswordService.compareHash(req.body.current_password, hash);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Password"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated Password"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/courtmatchup/club/profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("clubs");
      const club = (await sdk.get({
        user_id: req.user_id,
      }))[0];


      if(!club) {
        throw new Error("Club not found");
      }

      // parse availability and account_details
      const availability = JSON.parse(club.availability || "[]");
      club.availability = availability;
      const account_details = club.account_details ? JSON.parse(club.account_details) : null;
      club.account_details = account_details;
      club.advance_booking_days = club?.advance_booking_days ? JSON.parse(club?.advance_booking_days) : null;
      // sdk.setTable('sports');
      // const sports = await sdk.get({
      //   club_id: club.id
      // });
      let sql = `
      SELECT courtmatchup_sports.id, courtmatchup_sports.status,courtmatchup_sports.name as name, courtmatchup_sports.cancel_hours_before, courtmatchup_sports.allow_cancel_reservation, courtmatchup_sports.club_id, 
      GROUP_CONCAT(cst.name, ';', cst.subtype , ';', cst.id SEPARATOR '==') AS sport_types FROM courtmatchup_sports
        LEFT JOIN courtmatchup_club_sport_type cst ON cst.sport_id = courtmatchup_sports.id
          where courtmatchup_sports.club_id=${club.id}
          group by courtmatchup_sports.id
       `;
      const sports = await sdk.rawQuery(sql);

      for (let i = 0; i < sports.length; i++) {
        if (!sports[i].sport_types) {
          sports[i].sport_types = [{
            type: '',
            subtype: [],
            club_sport_type_id: ''
          }];
          continue;
        }
        const sport_types = sports[i].sport_types.split('==');
        const data  = []
        sport_types.forEach((sport_type, index) => {
          const [type, subtype, club_sport_type_id] = sport_type.split(';');
          data.push({
            type: type,
            subtype: subtype ? JSON.parse(subtype || '[]') : [],
            club_sport_type_id: club_sport_type_id
          });
        })
        sports[i].sport_types = data;
      }
      
      sdk.setTable('club_court');
      const courts = await sdk.get({
        club_id: club.id
      });
      for (let i = 0; i < courts.length; i++) {
        courts[i].court_settings = JSON.parse(courts[i].court_settings || '{}');
      }

      sdk.setTable('club_pricing');
      const pricing = await sdk.get({
        club_id: club.id
      });


      const data = {
        user: result[0],
        club: club,
        courts: courts,
        sports: sports,
        pricing: pricing.length > 0 ? pricing : null
      };


      return res.status(200).json({
        error: false,
        model: data,
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  app.get("/v3/api/custom/courtmatchup/admin/profile/:id", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      req.user_id = req.params.id
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("clubs");
      const club = (await sdk.get({
        user_id: req.user_id,
      }))[0];


      if(!club) {
        throw new Error("Club not found");
      }

      // sdk.setTable('sports');
      // const sports = await sdk.get({
      //   club_id: club.id
      // });
      let sql = `
      SELECT courtmatchup_sports.id, courtmatchup_sports.status,courtmatchup_sports.name as name, courtmatchup_sports.club_id, 
      GROUP_CONCAT(cst.name, ';', cst.subtype , ';', cst.id SEPARATOR '==') AS sport_types FROM courtmatchup_sports
        LEFT JOIN courtmatchup_club_sport_type cst ON cst.sport_id = courtmatchup_sports.id
          where courtmatchup_sports.club_id=${club.id}
          group by courtmatchup_sports.id
       `;
      const sports = await sdk.rawQuery(sql);

      for (let i = 0; i < sports.length; i++) {
        if (!sports[i].sport_types) {
          sports[i].sport_types = [{
            type: '',
            subtype: [],
            club_sport_type_id: ''
          }];
          continue;
        }
        const sport_types = sports[i].sport_types.split('==');
        const data  = []
        sport_types.forEach((sport_type, index) => {
          const [type, subtype, club_sport_type_id] = sport_type.split(';');
          data.push({
            type: type,
            subtype: subtype ? JSON.parse(subtype || '[]') : [],
            club_sport_type_id: club_sport_type_id
          });
        })
        sports[i].sport_types = data;
      }
      sdk.setTable('club_court');
      const courts = await sdk.get({
        club_id: club.id
      });

      sdk.setTable('club_pricing');
      const pricing = await sdk.get({
        club_id: club.id
      });
      // account_sid: "**********************************",
      // auth_token: "f6a7888c510e71dfed17d8f2e9c5108e",

      const data = {
        user: result[0],
        club: club,
        courts: courts,
        sports: sports,
        pricing: pricing
      };


      return res.status(200).json({
        error: false,
        model: data,
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  
  // update clinic data
  app.post("/v3/api/custom/courtmatchup/club/update-clinic-data/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const clinic_id = req.params.id;

      const { mapping=0, name, description, recurring, start_time, type, sub_type, surface_id, date, age_group, end_time, cost_per_head, max_participants, sport_id, level, create_at, update_at, end_date, cancellation_policy_days } = req.body;

      sdk.setTable("clinics");
      const clinic = (await sdk.get({
        id: clinic_id,
      }))[0];

      let new_clinic_id = null;

      if(!clinic) {
        throw new Error("Clinic not found");
      }
      if (mapping === 4){
        // clone the clinic
        const new_clinic_id = await sdk.rawQuery(`
          INSERT INTO courtmatchup_clinics (club_id, name, description, recurring, start_time, type, sub_type, surface_id, date, age_group, end_time, cost_per_head, max_participants, sport_id, level, create_at, update_at, end_date, cancellation_policy_days)
          SELECT club_id, name, description, recurring, start_time, type, sub_type, surface_id, date, age_group, end_time, cost_per_head, max_participants, sport_id, level, create_at, update_at, end_date, cancellation_policy_days
          FROM courtmatchup_clinics WHERE id = ${clinic_id}
        `);
        try{
          await sdk.rawQuery(`
            INSERT INTO courtmatchup_clinic_coaches (coach_id, clinic_id, status, data, create_at, update_at)
            SELECT coach_id, ${new_clinic_id[0]?.insertId}, status, data, create_at, update_at
            FROM courtmatchup_clinic_coaches WHERE clinic_id = ${clinic_id}
          `);
        }catch(err){
          console.log(err);
        }

        try{
          // now update this new clinic with passed data
          await sdk.update(filterEmptyFields({
            name, description, recurring, start_time, type, sub_type, surface_id, date, age_group, end_time, cost_per_head, max_participants, sport_id, level, create_at, update_at, end_date, cancellation_policy_days
          }), new_clinic_id[0]?.insertId);
        }catch(err){
          console.log(err);
        }

        new_clinic_id = new_clinic_id[0].insertId;

      }else{
        await sdk.update(filterEmptyFields({
          name, description, recurring, start_time, type, sub_type, surface_id, date, age_group, end_time, cost_per_head, max_participants, sport_id, level, create_at, update_at, end_date, cancellation_policy_days
        }), clinic_id);
        if(mapping !== 0) {
          if (mapping === 1){
            // cancel all future clinic events and bookings
            // delete them
            sdk.setTable("clinics");
            await sdk.rawQuery(`
              UPDATE courtmatchup_clinics SET status = 3 WHERE id = ${clinic_id}
              AND date >= CURDATE()
            `);
            // update to cancelled status
            await sdk.rawQuery(`
              UPDATE courtmatchup_booking SET status = 3 WHERE clinic_id = ${clinic_id}
              AND date >= CURDATE()
            `);
          }else if (mapping === 2){
            // update the data for them in booking table (sport_id, type, start_time, end_time, date) only if data is passed do not update to null
            await sdk.rawQuery(`
              UPDATE courtmatchup_booking SET ${sport_id ? `sport_id = ${sport_id},` : ''} ${type ? `type = ${type},` : ''} ${start_time ? `start_time = ${start_time},` : ''} ${end_time ? `end_time = ${end_time},` : ''} ${date ? `date = ${date}` : ''} WHERE clinic_id = ${clinic_id}
              AND date >= CURDATE()
            `);
          }else if (mapping === 3){
            // update the data for them in booking table (sport_id, type, start_time, end_time, date) only if data is passed do not update to null
            await sdk.rawQuery(`
              UPDATE courtmatchup_booking SET ${sport_id ? `sport_id = ${sport_id},` : ''} ${type ? `type = ${type},` : ''} ${start_time ? `start_time = ${start_time},` : ''} ${end_time ? `end_time = ${end_time},` : ''} ${date ? `date = ${date}` : ''} WHERE clinic_id = ${clinic_id}
            `);
          }
        }
      }

      return res.status(200).json({
        error: false,
        message: "Clinic data updated",
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  })
  app.get("/v3/api/custom/courtmatchup/club/format-time", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("club_court");
      const result = await sdk.get({})
      

// INSERT INTO `courtmatchup_club_court` (`id`, `club_id`, `name`, `sport_id`, `type`, `surface_id`, `availability`, `create_at`, `update_at`) VALUES
// (1,	1,	'CS court',	11,	1,	1,	'[{\"day\":\"monday\",\"timeslots\":[\"09:15:34\",\"10:45:23\",\"11:03:58\",\"12:26:40\",\"13:11:22\",\"14:59:47\",\"15:10:05\",\"16:23:16\",\"17:41:33\",\"18:14:02\",\"19:52:29\",\"20:37:45\",\"21:08:51\"]},{\"day\":\"tuesday\",\"timeslots\":[\"09:26:17\",\"10:12:45\",\"11:44:05\",\"12:09:33\",\"13:29:14\",\"20:15:49\",\"21:30:57\"]},{\"day\":\"wednessday\",\"timeslots\":[\"17:30:22\",\"18:15:47\",\"19:22:36\",\"20:43:10\",\"21:08:01\"]},{\"day\":\"friday\",\"timeslots\":[\"02:40:12\",\"17:20:33\",\"21:05:54\"]}]',	'2024-11-04',	'2024-11-04 14:37:49');
    // format all timeslots to have 00 seconds
      for (let i = 0; i < result.length; i++) {
        // parse availability
        const av = JSON.parse(result[i].availability || "[]");
        for (let j = 0; j < av.length; j++) {
          // parse timeslots
          console.log(av[j].timeslots,'full before');
          const timeslots = av[j].timeslots;
          console.log(timeslots);
          for (let k = 0; k < timeslots.length; k++) {
            // replace last 2 digits with 00
            console.log(timeslots[k],'before');
            timeslots[k] = timeslots[k].slice(0, -3) + ":00";
            console.log(timeslots[k]);
          }
          av[j].timeslots = timeslots;
        }
        // update table

        try {
          await sdk.update({
            availability: JSON.stringify(av)
          }, result[i].id);
        }catch (err) {
          console.log(err);
        }

        console.log("finished", result[i].id, '--', i);

      }
      


      return res.status(200).json({
        error: false,
        message: "OK",
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

//   app.get("/v3/api/custom/courtmatchup/club/generate-timeslots", middlewares, async function (req, res) {
//   try {
//     let sdk = req.sdk;
//     sdk.getDatabase();
//     sdk.setProjectId(req.projectId);
//     sdk.setTable("club_court");

//     const result = await sdk.get({});

//     function generateRandomTimeslot() {
//       const hour = Math.floor(Math.random() * 24).toString().padStart(2, "0");
//       const minute = Math.floor(Math.random() * 60).toString().padStart(2, "0");
//       return `${hour}:${minute}:00`;
//     }

//     function generateRandomDayData() {
//       const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
//       const dayCount = Math.floor(Math.random() * days.length); // Random number of days
//       const selectedDays = days.slice(0, dayCount);

//       return selectedDays.map((day) => {
//         const timeslotCount = Math.floor(Math.random() * 10) + 1; // Random number of timeslots
//         const timeslots = Array.from({ length: timeslotCount }, generateRandomTimeslot);
//         return { day, timeslots };
//       });
//     }

//     for (let i = 0; i < result.length; i++) {
//       // Generate random availability data
//       const includeAvailability = Math.random() > 0.3; // 70% chance to include availability
//       const availability = includeAvailability ? generateRandomDayData() : [];

//       try {
//         // Update table with new random availability
//         await sdk.update({
//           availability: JSON.stringify(availability)
//         }, result[i].id);
//       } catch (err) {
//         console.error("Error updating row ID:", result[i].id, err);
//       }

//       console.log("Finished updating row ID:", result[i].id);
//     }

//     return res.status(200).json({
//       error: false,
//       message: "Random timeslots generated and updated successfully!",
//     });
//   } catch (err) {
//     console.error(err);
//     res.status(500).json({
//       error: true,
//       message: err.message,
//     });
//   }
// });
app.get("/v3/api/custom/courtmatchup/club/generate-timeslots", middlewares, async function (req, res) {
  try {
    let sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    sdk.setTable("club_court");

    const result = await sdk.get({});

    // Predefined list of proper event timeslots (including `:45`)
    const eventTimes = [
      "09:00:00", "09:15:00", "09:30:00", "09:45:00",
      "10:00:00", "10:15:00", "10:30:00", "10:45:00",
      "11:00:00", "11:15:00", "11:30:00", "11:45:00",
      "12:00:00", "12:15:00", "12:30:00", "12:45:00",
      "13:00:00", "13:15:00", "13:30:00", "13:45:00",
      "14:00:00", "14:15:00", "14:30:00", "14:45:00",
      "15:00:00", "15:15:00", "15:30:00", "15:45:00",
      "16:00:00", "16:15:00", "16:30:00", "16:45:00",
      "17:00:00", "17:15:00", "17:30:00", "17:45:00",
      "18:00:00", "18:15:00", "18:30:00", "18:45:00",
      "19:00:00", "19:15:00", "19:30:00", "19:45:00",
      "20:00:00", "20:15:00", "20:30:00", "20:45:00",
      "21:00:00", "21:15:00", "21:30:00", "21:45:00"
    ];

    function generateRandomTimeslot() {
      const count = Math.floor(Math.random() * 5) + 1; // Random number of timeslots (1 to 5)
      const shuffledTimes = eventTimes.sort(() => Math.random() - 0.5); // Shuffle times
      return shuffledTimes.slice(0, count); // Select `count` random times
    }

    function generateRandomDayData() {
      const days = ["monday", "tuesday", "wednesday", "thursday", "friday", ];
      const dayCount = Math.floor(Math.random() * days.length); // Random number of days
      const selectedDays = days.sort(() => Math.random() - 0.5).slice(0, dayCount); // Randomly pick days

      return selectedDays.map((day) => {
        return { day, timeslots: generateRandomTimeslot() };
      });
    }

    for (let i = 0; i < result.length; i++) {
      // Generate random availability data
      const includeAvailability = Math.random() > 0.3; // 70% chance to include availability
      const availability =  generateRandomDayData();

      try {
        // Update table with new random availability
        await sdk.update({
          availability: JSON.stringify(availability)
        }, result[i].id);
      } catch (err) {
        console.error("Error updating row ID:", result[i].id, err);
      }

      console.log("Finished updating row ID:", result[i].id);
    }

    return res.status(200).json({
      error: false,
      message: "Random timeslots generated and updated successfully!",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: true,
      message: err.message,
    });
  }
});
app.get("/v3/api/custom/courtmatchup/coach/generate-timeslots", middlewares, async function (req, res) {
  try {
    let sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    sdk.setTable("coach");

    const result = await sdk.get({});

    // Predefined list of proper event timeslots (including `:45`)
    const eventTimes = [
      "09:00:00", "09:15:00", "09:30:00", "09:45:00",
      "10:00:00", "10:15:00", "10:30:00", "10:45:00",
      "11:00:00", "11:15:00", "11:30:00", "11:45:00",
      "12:00:00", "12:15:00", "12:30:00", "12:45:00",
      "13:00:00", "13:15:00", "13:30:00", "13:45:00",
      "14:00:00", "14:15:00", "14:30:00", "14:45:00",
      "15:00:00", "15:15:00", "15:30:00", "15:45:00",
      "16:00:00", "16:15:00", "16:30:00", "16:45:00",
      "17:00:00", "17:15:00", "17:30:00", "17:45:00",
      "18:00:00", "18:15:00", "18:30:00", "18:45:00",
      "19:00:00", "19:15:00", "19:30:00", "19:45:00",
      "20:00:00", "20:15:00", "20:30:00", "20:45:00",
      "21:00:00", "21:15:00", "21:30:00", "21:45:00"
    ];

    function generateRandomTimeslot(all =false) {
      const count = Math.floor(Math.random() * 5) + 1; // Random number of timeslots (1 to 5)
      if (all) return eventTimes
      const shuffledTimes = eventTimes.sort(() => Math.random() - 0.5); // Shuffle times
      return shuffledTimes.slice(0, count); // Select `count` random times
    }

    function generateRandomDayData(all=false) {
      const days = ["monday", "tuesday", "wednesday", "thursday", "friday", ];
      const dayCount = Math.floor(Math.random() * days.length); // Random number of days
      const selectedDays = days.sort(() => Math.random() - 0.5).slice(0, dayCount); // Randomly pick days

      return all ? days.map((day) => {
        return { day, timeslots: generateRandomTimeslot(all) };
      }) : selectedDays.map((day) => {
        return { day, timeslots: generateRandomTimeslot() };
      });
    }

    let all = true

    for (let i = 0; i < result.length; i++) {
      // Generate random availability data
      const includeAvailability = Math.random() > 0.3; // 70% chance to include availability
      const availability =  generateRandomDayData(all);
      all = false

      try {
        // Update table with new random availability
        await sdk.update({
          availability: JSON.stringify(availability)
        }, result[i].id);
      } catch (err) {
        console.error("Error updating row ID:", result[i].id, err);
      }

      console.log("Finished updating row ID:", result[i].id);
    }

    return res.status(200).json({
      error: false,
      message: "Random timeslots generated and updated successfully!",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: true,
      message: err.message,
    });
  }
});

  app.post("/v3/api/custom/courtmatchup/club/profile-edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("user");

      const updateResult = await sdk.update(filterEmptyFields({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo,
        phone: req.body.phone,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.user_id);

      sdk.setTable("clubs");
      const club = (await sdk.get({
        user_id: req.user_id,
      }))[0];

      let coaches = []

      if(!club) {

        let {
          name,
          bio,
          opening_time,
          closing_time,
          times= [],
          title,
          fee_settings=[],
          description,
          club_logo,
          show_notification,
          home_image,
          days_off,
          daily_breaks,
          splash_screen,
          show_clinic,
          private_contact,
          show_buddy,
          show_coach,
          completed=0,
          show_groups,
          slug,
          advance_booking_days={},
          allow_user_court_selection=0,
          show_court,
          account_settings,
          buddy_description,
          court_description,
          exceptions,
          service_fee,
          coach_description,
          clinic_description,
          club_location,
          lesson_description,
          custom_request_threshold,
          max_players,
          membership_settings=[],
          account_details,
          pricing = [],
          sport_ids=[],
          sports=[],
          courts
        } = req.body;

        if (name) {
          name = name.trim();
          sdk.setTable('clubs');
          const clubs = await sdk.get({
            name
          });
          if (clubs.length > 0) {
            return res.status(403).json({
              error: true,
              message: "Name already exists"
            });
          }
        }
        
        if (account_details) validate_account(account_details);
        // if (availability) validate_availability(availability);
        sdk.setTable('clubs');
        const club_id = await sdk.insert(filterEmptyFields({
          user_id: result,
          name,
          bio,
          opening_time,
          club_logo,
          show_notification,
          home_image,
          private_contact,
          closing_time,
          splash_screen,
          slug,
          advance_booking_days: JSON.stringify(advance_booking_days || {}),
          allow_user_court_selection: allow_user_court_selection,
          show_clinic,
          show_buddy,
          show_coach,
          show_groups,
          show_court,
          service_fee,
          club_location,
          title,
          description,
          buddy_description,
          completed,
          court_description,
          coach_description,
          days_off: JSON.stringify( days_off || []),
          times: JSON.stringify( times || []),
          fee_settings: JSON.stringify( fee_settings || []),
          daily_breaks: JSON.stringify( daily_breaks || []),
          account_settings: JSON.stringify( account_settings || []),
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          exceptions: JSON.stringify( exceptions || []),
          account_details: JSON.stringify( account_details || []),
          membership_settings: JSON.stringify( membership_settings || []),
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }))

        // if(sport_ids && sport_ids.length > 0) {
        //   sdk.setTable('club_sports');
        //   for (const sport of sport_ids) {
        //     const { type, price, sport_id } = sport
        //     await sdk.insert(filterEmptyFields({
        //       club_id,
        //       sport_id,
        //       type,
        //       price
        //     }))
        //   }
        // }

        if(sport_ids && sport_ids.length > 0) {
          sports = [...sports, ...sport_ids]
        }

        if(sports && sports.length > 0) {
          for (const sport of sports) {
            const { name, types=[], allow_cancel_reservation=false, cancel_hours_before=24 } = sport
            sdk.setTable('sports');
            const sport_id = await sdk.insert(filterEmptyFields({
              club_id,
              name,
              allow_cancel_reservation: allow_cancel_reservation ? 1 : 0,
              cancel_hours_before,
            }))

            sdk.setTable("club_sport_type")
            for (const type of types) {
              const { name, sub_type:subtype=[] } = type
              await sdk.insert(filterEmptyFields({
                club_id,
                name,
                sport_id,
                subtype: JSON.stringify(subtype || [])
              }))
            }
          }
        }
        
        if(courts && courts.length){
          for (const sport of courts) {
            const { type, price, sport_id,name,days,start_time,end_time, start_date,end_date, surface_id, sub_type, court_settings } = sport
            sdk.setTable('club_court');
            const court_id = await sdk.insert(filterEmptyFields({
              club_id,
              sport_id,
              name,
              type: (type),
              surface_id,
              sub_type: (sub_type),
              court_settings: JSON.stringify(court_settings || {})
            }))
            sdk.setTable('court_pricing');
            const price_id = await sdk.insert(filterEmptyFields({
              court_id,
              club_id,
              price,
              days: JSON.stringify(days),
              start_time,
              end_time,
              start_date,
              end_date
            }))
          }
        }

        for (const price of pricing) {
          const { sport_id, type, sub_type, subtype, price_by_hours, is_general, general_rate=0, lesson_club_fixed_amount, lesson_club_percentage, lesson_pricing_type, is_lesson=false } = price;
          // check if exists
          
          sdk.setTable('club_pricing');
          
          const price_id = await sdk.insert(filterEmptyFields({
            sport_id,
            type,
            subtype: subtype ? subtype : sub_type ? sub_type : null,
            lesson_club_fixed_amount,
            lesson_club_percentage,
            lesson_pricing_type,
            is_lesson: is_lesson ? 1 : 0,
            club_id,
            price_by_hours: price_by_hours ? JSON.stringify(price_by_hours) : null,
            is_general,
            general_rate
          }));
        }
      }else{
        let {
          name,
          bio,
          opening_time,
          closing_time,
          times,
          title,
          description,
          days_off,
          daily_breaks,
          splash_screen,
          show_clinic,
          advance_booking_days,
          allow_user_court_selection,
          show_buddy,
          show_coach,
          completed,
          slug,
          email_template,
          private_contact,
          show_groups,
          show_court,
          club_logo,
          show_notification,
          home_image,
          buddy_description,
          court_description,
          coach_description,
          fee_settings,
          sports,
          clinic_description,
          lesson_description,
          service_fee,
          custom_request_threshold,
          max_players,
          account_details,
          club_location,
          account_settings,
          exceptions,
          sport_ids = [],
          pricing = [],
          membership_settings,
          courts
        } = req.body;

        if (name) {
          name = name.trim();
          sdk.setTable('clubs');
          const clubs = await sdk.get({
            name
          });
          if (clubs.length > 0) {
            return res.status(403).json({
              error: true,
              message: "Name already exists"
            });
          }
        }
        
        if (account_details) validate_account(account_details);
        // if (availability) validate_availability(availability);
        sdk.setTable('clubs');
        await sdk.update(filterEmptyFields({
          user_id: req.user_id,
          name,
          bio,
          opening_time,
          closing_time,
          service_fee,
          splash_screen,
          show_clinic,
          advance_booking_days: advance_booking_days ? JSON.stringify(advance_booking_days) : null,
          allow_user_court_selection: allow_user_court_selection,
          completed,
          show_buddy,
          private_contact,
          slug,
          times: times ? JSON.stringify( times ) : null,
          fee_settings: fee_settings ? JSON.stringify( fee_settings || []) : null,
          show_coach,
          title,
          description,
          days_off: days_off ? JSON.stringify( days_off || []) : null,
          daily_breaks: daily_breaks ? JSON.stringify( daily_breaks || []) : null,
          club_logo,
          show_notification,
          home_image,
          show_groups,
          show_court,
          buddy_description,
          court_description,
          club_location,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details: account_details ?  JSON.stringify( account_details) : null,
          account_settings:account_settings ?   JSON.stringify( account_settings) : null,
          membership_settings:membership_settings ?   JSON.stringify( membership_settings) : null,
          exceptions: exceptions ?  JSON.stringify( exceptions) : null,
          update_at: sqlDateTimeFormat(new Date())
        }),club.id);
        const club_id = club.id;

        if(sport_ids && sport_ids.length > 0) {
          sports = [...sports, ...sport_ids]
        }

        if (sports && sports.length > 0) {
          for (const sport of sports) {
            let { name, types = [], sport_id, allow_cancel_reservation, cancel_hours_before } = sport;
            sdk.setTable("sports");
        
            if (sport_id) {
              await sdk.update(
                filterEmptyFields({
                  name,
                  allow_cancel_reservation: allow_cancel_reservation ? 1 : 0,
                  cancel_hours_before,
                  update_at: sqlDateTimeFormat(new Date()),
                }),
                sport_id
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype, club_sport_type_id } = type;    
                  return club_sport_type_id ? sdk.update(
                    filterEmptyFieldsAllowEmptyString({
                      subtype: subtype ? JSON.stringify(subtype) : null,
                      name,
                      update_at: sqlDateTimeFormat(new Date()),
                    }),
                    club_sport_type_id
                  ) : sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: subtype ? JSON.stringify(subtype) : null,
                    })
                  );
                })
              );
            } else {
              if (!club_id) throw new Error("club_id is undefined");
        
              sport_id = await sdk.insert(
                filterEmptyFieldsAllowEmptyString({
                  club_id,
                  name,
                })
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype = [] } = type;
                  return sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: JSON.stringify(subtype),
                    })
                  );
                })
              );
            }
          }
        }
        


        sdk.setTable('club_pricing');
        if (pricing && pricing.length){
          for (const price of pricing) {
            const { sport_id, type, sub_type, subtype, price_by_hours, club_pricing_id, is_general, general_rate, lesson_club_fixed_amount, lesson_club_percentage, lesson_pricing_type, is_lesson=false } = price;
            if(club_pricing_id){
              await sdk.update(filterEmptyFieldsAllowEmptyString({
                sport_id,
                type,
                subtype: subtype ? subtype : sub_type ? sub_type : null,
                price_by_hours: price_by_hours ?  JSON.stringify(price_by_hours) : null,
                is_general,
                is_lesson: is_lesson ? 1 : 0,
                general_rate,
                lesson_club_fixed_amount,
                lesson_club_percentage,
                lesson_pricing_type
              }), club_pricing_id)
            }else{
              await sdk.insert(filterEmptyFieldsAllowEmptyString({
                sport_id,
                type,
                club_id,
                lesson_club_fixed_amount,
                lesson_club_percentage,
                lesson_pricing_type,
                is_lesson: is_lesson ? 1 : 0,
                subtype: subtype ? subtype : sub_type ? sub_type : null,
                price_by_hours: price_by_hours ? JSON.stringify(price_by_hours) : null,
                is_general,
                general_rate
              }))
            }
          }
        }

        
        if(courts && courts.length){
          for (const sport of courts) {
            const { availability, type, price, sport_id,name,days,start_time,end_time, start_date,end_date, court_id, court_price_id,surface_id, sub_type, sport_change_option, court_settings, delete_reservations=false } = sport
            if(court_id){
              sdk.setTable('club_court');
              await sdk.update(filterEmptyFieldsAllowEmptyString({
                sport_id,
                name,
                type,
                availability: availability ? JSON.stringify(availability) : null,
                surface_id,
                sub_type,
                court_settings: court_settings ? JSON.stringify(court_settings) : null
              }),court_id)

              if(sport_change_option){
                // Find all bookings for this court by court_id or court_ids JSON array
                const events = await sdk.rawQuery(`
                  SELECT * FROM courtmatchup_booking WHERE court_id = ${court_id} OR JSON_CONTAINS(court_ids, '${court_id}', '$')
                `);

                if (sport_change_option == 3) {
                  // Change the sport for these events
                  for (const event of events) {
                    const { court_ids: eventCourtIds, court_id: eventCourtId, id } = event;
                    let updatedCourtIds = JSON.parse(eventCourtIds || '[]');
                    
                    // If this court is in the court_ids array, update it to the new sport_id
                    if (updatedCourtIds.includes(parseInt(court_id))) {
                      updatedCourtIds = updatedCourtIds.map(id => id == court_id ? parseInt(sport_id) : id);
                    }
                    
                    // Update booking table
                    sdk.setTable('booking');
                    
                    // If this court is the primary court, update sport_id
                    if (eventCourtId == court_id) {
                      await sdk.update(filterEmptyFields({
                        sport_id
                      }), id);
                    }
                    
                    // Update court_ids array
                    await sdk.update(filterEmptyFields({
                      court_ids: JSON.stringify(updatedCourtIds)
                    }), id);
                  }
                } else if (sport_change_option == 2) {
                  // Delete all bookings for this court
                  sdk.setTable('booking');
                  await sdk.deleteWhere({
                    court_id: court_id
                  });
                  for (const event of events) {
                    // remove court_id from court_ids array
                    let updatedCourtIds = JSON.parse(event.court_ids || '[]');
                    if (updatedCourtIds.length > 0) {
                      updatedCourtIds = updatedCourtIds.filter(id => id != court_id);
                      await sdk.update(filterEmptyFields({
                        court_ids: JSON.stringify(updatedCourtIds)
                      }), event.id);
                    }
                  }
                }
              }


              if (delete_reservations) {
                // Get all affected reservations
                const affectedReservations = await sdk.rawQuery(`
                  SELECT r.id as reservation_id, b.id as booking_id
                  FROM courtmatchup_reservation r
                  JOIN courtmatchup_booking b ON r.booking_id = b.id
                  WHERE b.court_id = ${court_id} 
                  OR JSON_CONTAINS(b.court_ids, '${court_id}', '$')
                `);
  
                // Delete reservations and their associated bookings
                for (const reservation of affectedReservations) {
                  sdk.setTable('reservation');
                  await sdk.delete({}, reservation.reservation_id);
                  
                  sdk.setTable('booking');
                  await sdk.delete({}, reservation.booking_id);
                }
              }

              if (court_price_id) {
                sdk.setTable('court_pricing');
                const price_id = await sdk.update(filterEmptyFields({
                  price,
                  days: days ? JSON.stringify(days) : null,
                  start_time,
                  end_time,
                  start_date,
                  end_date
                }),court_price_id)
              }
             
            }else{
              sdk.setTable('club_court');
              const court_id = await sdk.insert(filterEmptyFieldsAllowEmptyString({
                club_id,
                sport_id,
                name,
                type,
                sub_type,
                availability: availability ? JSON.stringify(availability || []) : null,
                surface_id
              }))
              sdk.setTable('court_pricing');
              const price_id = await sdk.insert(filterEmptyFields({
                court_id,
                club_id,
                price,
                days: JSON.stringify(days || []),
                start_time,
                end_time,
                start_date,
                end_date
              }))
            }
          }
        }
        if(exceptions && exceptions.length > 0 || days_off && days_off.length > 0 || opening_time || closing_time) {
          // send email notification to all coaches
          coaches = await sdk.rawQuery(`
            SELECT email,role, first_name, last_name, courtmatchup_user.club_id FROM courtmatchup_user LEFT JOIN courtmatchup_coach c on c.user_id=courtmatchup_user.id WHERE c.club_id = ${club_id}
          `);
        }
      }
      
      

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }




      res.status(200).json({
        error: false,
        message: "Updated",
        // notification_errors: errors
      });
      console.log(coaches.length,'<< coaches');
      for (const coach of coaches) {
        const { email, first_name, last_name } = coach;
        const eventService = new MkdEventService(sdk, req.projectId, req.headers);
        try{
          const mailResult = await eventService.sendMail(
            {
              email: config.mail_user,
              to: email,
              from: config.from_mail,
              first_name,
              last_name,
              link: `https://courtmatchup.manaknightdigital.com/coach/login`
            },
            "affected_schedule"
          );
        }catch(err){
          console.log(err);
          // errors.push(JSON.stringify(err));
        }
      }
    } catch (err) {
      console.log(err)
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  app.get("/v3/api/custom/courtmatchup/club/data/:table_name/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const tables = ["club_pricing", "club_court", "court_pricing"];
      if(!tables.includes(req.params.table_name)){
        return res.status(403).json({
          error: true,
          message: "Invalid Table Name",
        });
      }
      
      sdk.setTable(req.params.table_name);
      const {id} = req.params;
      const result = await sdk.get({
        id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "Invalid ID",
        });
      }
      await sdk.delete({},id);
      

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      console.log(err)
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  app.post("/v3/api/custom/courtmatchup/admin/profile-edit/:id", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      req.user_id = req.params.id;
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("user");

      const updateResult = await sdk.update(filterEmptyFields({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo,
        phone: req.body.phone,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.user_id);

      sdk.setTable("clubs");
      const club = (await sdk.get({
        user_id: req.user_id,
      }))[0];

      if (!club) {
        return res.status(403).json({
          error: true,
          message: "Club not found",
        });
      }
        const {
          name,
          bio,
          opening_time,
          closing_time,
          title,
          description,
          days_off,
          daily_breaks,
          splash_screen,
          show_clinic,
          show_buddy,
          show_coach,
          show_groups,
          sports,
          show_court,
          service_fee,
          club_logo,
          show_notification,
          home_image,
          buddy_description,
          court_description,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details,
          account_settings,
          exceptions,
          sport_ids,
          fee_settings,
          pricing,
          membership_settings,
          courts
        } = req.body;
        
        if (account_details) validate_account(account_details);
        // if (availability) validate_availability(availability);
        sdk.setTable('clubs');
        await sdk.update(filterEmptyFields({
          name,
          bio,
          opening_time,
          closing_time,
          splash_screen,
          show_clinic,
          show_buddy,
          show_coach,
          title,
          description,
          days_off: days_off ? JSON.stringify( days_off || []) : null,
          fee_settings: fee_settings ? JSON.stringify( fee_settings || []) : null,
          daily_breaks: daily_breaks ? JSON.stringify( daily_breaks || []) : null,
          club_logo,
          show_notification,
          home_image,
          show_groups,
          show_court,
          buddy_description,
          court_description,
          service_fee,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details: account_details ?  JSON.stringify( account_details) : null,
          account_settings:account_settings ?   JSON.stringify( account_settings) : null,
          membership_settings:membership_settings ?   JSON.stringify( membership_settings) : null,
          exceptions: exceptions ?  JSON.stringify( exceptions) : null,
          update_at: sqlDateTimeFormat(new Date())
        }),club.id);
        const club_id = club.id;

        if(sport_ids && sport_ids.length > 0) {
          sdk.setTable('club_sports');
          for (const sport of sport_ids) {
            const { type, price, sport_id, club_sport_id } = sport
            if(club_sport_id){
              await sdk.update(filterEmptyFields({
                club_id,
                sport_id,
                type,
                price
              }), club_sport_id)
            }else{
              await sdk.insert(filterEmptyFields({
                club_id,
                sport_id,
                type,
                price
              }))
            }
          }
        }

        if (sports && sports.length > 0) {
          for (const sport of sports) {
            let { name, types = [], sport_id, allow_cancel_reservation, cancel_hours_before } = sport;
            sdk.setTable("sports");
        
            if (sport_id) {
              await sdk.update(
                filterEmptyFieldsAllowEmptyString({
                  name,
                  allow_cancel_reservation: allow_cancel_reservation ? 1 : 0,
                  cancel_hours_before,
                  update_at: sqlDateTimeFormat(new Date()),
                }),
                sport_id
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype, club_sport_type_id } = type;    
                  return club_sport_type_id ? sdk.update(
                    filterEmptyFieldsAllowEmptyString({
                      subtype: subtype ? JSON.stringify(subtype) : null,
                      name,
                      update_at: sqlDateTimeFormat(new Date()),
                    }),
                    club_sport_type_id
                  ) : sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: subtype ? JSON.stringify(subtype) : null,
                    })
                  );
                })
              );
            } else {
              if (!club_id) throw new Error("club_id is undefined");
        
              sport_id = await sdk.insert(
                filterEmptyFieldsAllowEmptyString({
                  club_id,
                  name,
                })
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype = [] } = type;
                  return sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: JSON.stringify(subtype),
                    })
                  );
                })
              );
            }
          }
        }
        if(pricing && pricing.length){
          sdk.setTable('club_pricing');
          for (const price of pricing) {
            const { sport_id, type, sub_type, subtype, price_by_hours, club_pricing_id, is_general, general_rate, lesson_club_fixed_amount, lesson_club_percentage, lesson_pricing_type, is_lesson=false } = price;
            if(club_pricing_id){
              await sdk.update(filterEmptyFieldsAllowEmptyString({
                sport_id,
                type,
                subtype: subtype ? subtype : sub_type ? sub_type : null,
                price_by_hours: price_by_hours ?  JSON.stringify(price_by_hours) : null,
                is_general,
                is_lesson: is_lesson ? 1 : 0,
                general_rate,
                lesson_club_fixed_amount,
                lesson_club_percentage,
                lesson_pricing_type
              }), club_pricing_id)
            }else{
              await sdk.insert(filterEmptyFieldsAllowEmptyString({
                sport_id,
                type,
                club_id,
                lesson_club_fixed_amount,
                lesson_club_percentage,
                lesson_pricing_type,
                is_lesson: is_lesson ? 1 : 0,
                subtype,
                price_by_hours: price_by_hours ? JSON.stringify(price_by_hours) : null,
                is_general,
                general_rate
              }))
            }
          }
        }

        
        if(courts && courts.length){
          for (const sport of courts) {
            const { availability, type, price, sport_id,name,days,start_time,end_time, start_date,end_date, court_id, court_price_id,surface_id } = sport
            if(court_id){
              sdk.setTable('club_court');
              await sdk.update(filterEmptyFieldsAllowEmptyString({
                sport_id,
                name,
                type,
                availability: availability ? JSON.stringify(availability) : null,
                surface_id
              }),court_id)

              if (court_price_id) {
                sdk.setTable('court_pricing');
                const price_id = await sdk.update(filterEmptyFields({
                  price,
                  days: days ? JSON.stringify(days) : null,
                  start_time,
                  end_time,
                  start_date,
                  end_date
                }),court_price_id)
              }
             
            }else{
              sdk.setTable('club_court');
              const court_id = await sdk.insert(filterEmptyFieldsAllowEmptyString({
                club_id,
                sport_id,
                name,
                type,
                sub_type,
                availability: availability ? JSON.stringify(availability || []) : null,
                surface_id
              }))
              sdk.setTable('court_pricing');
              const price_id = await sdk.insert(filterEmptyFields({
                court_id,
                club_id,
                price,
                days: JSON.stringify(days || []),
                start_time,
                end_time,
                start_date,
                end_date
              }))
            }
          }
        }
      
      

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }




      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      console.log(err)
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  //GET /v3/api/custom/courtmatchup/club/courts/affected-reservations query: { court_id }
  // Add new endpoint to check affected reservations
  app.get("/v3/api/custom/courtmatchup/club/courts/affected-reservations", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { 
        court_id,
      } = req.query;

      let query = '';
      let affectedReservations = [];

      // Case 1: Check court-specific changes
      if (court_id) {
        // Get all bookings that would be affected by court changes
        query = `
          SELECT 
            b.id as booking_id,
            b.date,
            b.start_time,
            b.end_time,
            b.sport_id,
            b.court_id,
            b.court_ids,
            b.user_id,
            u.first_name,
            u.last_name,
            u.email,
            s.name as sport_name,
            r.id as reservation_id
          FROM 
            courtmatchup_booking b
          LEFT JOIN 
            courtmatchup_user u ON b.user_id = u.id
          LEFT JOIN
            courtmatchup_sports s ON b.sport_id = s.id
          LEFT JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
          WHERE 
            b.court_id = ${court_id} 
            OR JSON_CONTAINS(b.court_ids, '${court_id}', '$')
          ORDER BY 
            b.date ASC, b.start_time ASC
        `;
        
        affectedReservations = await sdk.rawQuery(query);
      }

      return res.status(200).json({
        error: false,
        affected_reservations: affectedReservations,
        count: affectedReservations.length
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/courtmatchup/club/courts/affected-reservations", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { 
        court_id,
        times,
        days_off,
        exceptions
      } = req.body;

      let query = '';
      let affectedReservations = [];

      // Case 1: Check court-specific changes
      if (court_id) {
        // Get all bookings that would be affected by court changes
        query = `
          SELECT 
            b.id as booking_id,
            b.date,
            b.start_time,
            b.status,
            b.end_time,
            b.sport_id,
            b.court_id,
            b.court_ids,
            b.user_id,
            u.first_name,
            u.last_name,
            u.email,
            s.name as sport_name,
            r.id as reservation_id
          FROM 
            courtmatchup_booking b
          LEFT JOIN 
            courtmatchup_user u ON b.user_id = u.id
          LEFT JOIN
            courtmatchup_sports s ON b.sport_id = s.id
          LEFT JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
          WHERE 
            b.court_id = ${court_id} 
            OR JSON_CONTAINS(b.court_ids, '${court_id}', '$')
          ORDER BY 
            b.date ASC, b.start_time ASC
        `;
        
        affectedReservations = await sdk.rawQuery(query);
      } 
      // Case 2: Check club-wide changes (working hours, days off, exceptions)
      else {
        // First, get the club details to compare current vs new settings
        sdk.setTable("clubs");
        const clubData = await sdk.get({ user_id: req.user_id });

        const club_id = clubData[0].id;
        
        if (!clubData.length) {
          throw new Error("Club not found");
        }
        
        const club = clubData[0];
        
        // Parse the new settings if provided
        const newTimes = times ? times : null;
        const newDaysOff = days_off ? days_off : null;
        const newExceptions = exceptions ? exceptions : null;
        
        // Build a query to find all affected reservations
        let whereConditions = [];
        
        // Check days off changes
        if (newDaysOff && newDaysOff.length > 0) {
          const daysOffConditions = newDaysOff.map(day => {
            return `DAYNAME(b.date) = '${day}'`;
          });
          
          if (daysOffConditions.length > 0) {
            whereConditions.push(`(${daysOffConditions.join(" OR ")})`);
          }
        }
        
        // Check working hours changes
        if (newTimes && newTimes.length > 0) {
          // For simplicity, assume the first time range is the main working hours
          const mainHours = newTimes[0];
          
          if (mainHours.from && mainHours.until) {
            // Get the current club times to compare with new times
            const currentTimes = JSON.parse(club.times || '[]');
            
            if (currentTimes.length > 0) {
              const currentMainHours = currentTimes[0];
              
              // If new opening hour is later than current one
              if (mainHours.from > currentMainHours.from) {
                // Add events that start within the removed morning hours
                whereConditions.push(`(b.start_time >= '${currentMainHours.from}' AND b.start_time < '${mainHours.from}')`);
              }
              
              // If new closing hour is earlier than current one
              if (mainHours.until < currentMainHours.until) {
                // Add events that end within the removed evening hours
                whereConditions.push(`(b.end_time > '${mainHours.until}' AND b.end_time <= '${currentMainHours.until}')`);
              }
              
              // For events completely outside the new operating hours
              whereConditions.push(`(b.start_time < '${mainHours.from}' AND b.end_time <= '${mainHours.from}')`); // Events before opening
              whereConditions.push(`(b.start_time >= '${mainHours.until}' AND b.end_time > '${mainHours.until}')`); // Events after closing
            } else {
              // If there are no current times (new club setup), just find events outside the new hours
              whereConditions.push(`(b.start_time < '${mainHours.from}' OR b.end_time > '${mainHours.until}')`);
            }
          }
        }
        
        // Check exception hours
        if (newExceptions && newExceptions.length > 0) {
          const exceptionConditions = [];
          
          for (const exception of newExceptions) {
            if (exception.days && exception.days.length > 0) {
              for (const dayData of exception.days) {
                const day = dayData.day;
                const timeslots = dayData.timeslots || [];
                
                if (timeslots.length > 0) {
                  // Convert day name to DAYOFWEEK value (MySQL uses 1=Sunday, 2=Monday, etc.)
                  let dayNumber;
                  switch (day.toLowerCase()) {
                    case 'sunday': dayNumber = 1; break;
                    case 'monday': dayNumber = 2; break;
                    case 'tuesday': dayNumber = 3; break;
                    case 'wednesday': dayNumber = 4; break;
                    case 'thursday': dayNumber = 5; break;
                    case 'friday': dayNumber = 6; break;
                    case 'saturday': dayNumber = 7; break;
                    default: continue;
                  }
                  
                  for (const timeslot of timeslots) {
                    // Find bookings that overlap with this exception timeslot
                    exceptionConditions.push(`(
                      DAYOFWEEK(b.date) = ${dayNumber} AND 
                      (
                        (b.start_time <= '${timeslot}' AND b.end_time > '${timeslot}') OR
                        ('${timeslot}' BETWEEN b.start_time AND b.end_time)
                      )
                    )`);
                  }
                }
              }
            }
          }
          
          if (exceptionConditions.length > 0) {
            whereConditions.push(`(${exceptionConditions.join(" OR ")})`);
          }
        }
        
        // Only perform the query if we have conditions to check
        if (whereConditions.length > 0) {
          query = `
            SELECT 
              b.id as booking_id,
              b.date,
              b.start_time,
              b.end_time,
              b.sport_id,
              b.court_id,
              b.court_ids,
              b.status,
              b.user_id,
              u.first_name,
              u.last_name,
              u.email,
              s.name as sport_name,
              r.id as reservation_id,
              CASE 
                WHEN ${whereConditions.map((_, index) => `condition_${index + 1}`).join(" OR ")} 
                THEN 'conflict' 
                ELSE 'no_conflict' 
              END as conflict_reason
            FROM 
              courtmatchup_booking b
            LEFT JOIN 
              courtmatchup_user u ON b.user_id = u.id
            LEFT JOIN
              courtmatchup_sports s ON b.sport_id = s.id
            LEFT JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            LEFT JOIN
              courtmatchup_club_court cc ON b.court_id = cc.id
            WHERE 
              cc.club_id = ${club_id} AND
              (${whereConditions.join(" OR ")})
            ORDER BY 
              b.date ASC, b.start_time ASC
          `;
          
          // Replace condition placeholders
          whereConditions.forEach((condition, index) => {
            query = query.replace(`condition_${index + 1}`, condition);
          });
          
          affectedReservations = await sdk.rawQuery(query);
        }
      }

      return res.status(200).json({
        error: false,
        affected_reservations: affectedReservations,
        total_affected: affectedReservations.length,
        completed_events: affectedReservations.filter(event => event.date ? new Date(event.date) < new Date() : false).length,
        upcoming_events: affectedReservations.filter(event => event.date ? new Date(event.date) > new Date() : false).length,
        last_event_date: affectedReservations.length > 0 ? affectedReservations[affectedReservations.length - 1].date : null,
        count: affectedReservations.length
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Profile API",
      url: "/v3/api/custom/profile",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
      ],
      needToken: true
    }
  ];
};
