const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const stripe = new StripeService();
const { BOOKING_STATUSES, RESERVATION_TYPES } = require("../utils/constants.js");
const { check_staff_permission, reservation_hours_left, log_reservation } = require("../utils/util.js");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "staff" }),
];

const base = "/v3/api/custom/courtmatchup/staff/reservations";

module.exports = function (app) {
  // Permission middleware helper
  const permissionCheck = (permission) => {
    return async (req, res, next) => {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        
        // const hasPermission = await check_staff_permission(sdk, req.user_id, permission);
        
        // if (!hasPermission) {
        //   return res.status(403).json({
        //     error: true,
        //     message: `You don't have permission to access this feature`
        //   });
        // }
        
        // Add the staff profile to the request
        sdk.setTable("staff");
        const staff = await sdk.get({
          user_id: req.user_id,
        });
        
        if (!staff.length) {
          return res.status(403).json({
            error: true,
            message: "Staff profile not found",
          });
        }
        
        req.staff_profile = staff[0];
        req.club_id = staff[0].club_id;
        
        next();
      } catch (err) {
        return res.status(403).json({
          error: true,
          message: err.message
        });
      }
    };
  };

  // Get all reservations (requires appropriate permission)
  app.get(base, middlewares, permissionCheck('availability_dashboard'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { date, status, type, view_all } = req.query;
      
      // Check if staff can view all reservations
      const canViewAll = await check_staff_permission(sdk, req.user_id, 'availability_dashboard_all');
      
      // Build SQL query with filters
      let sql = `
        SELECT 
          r.id AS reservation_id,
          r.booking_id,
          r.buddy_id,
          r.user_id AS reservation_user_id,
          r.status AS reservation_status,
          r.create_at AS reservation_created_at,
          r.notes,
          r.space_assigned,
          b.user_id AS booking_user_id,
          b.sport_id,
          b.type,
          b.subtype AS sub_type,
          b.date AS booking_date,
          b.start_time,
          b.end_time,
          b.court_id,
          b.coach_id,
          b.clinic_id,
          b.lesson_id,
          b.price,
          b.custom_request,
          b.player_ids,
          b.duration,
          b.status AS booking_status,
          
          -- Calculate time difference
          CASE 
            WHEN DATE(b.date) = CURDATE() THEN
              TIMESTAMPDIFF(MINUTE, NOW(), CONCAT(b.date, ' ', b.start_time))
            ELSE NULL
          END AS time_to_reservation,
          
          -- Get creator details
          creator.first_name AS creator_first_name,
          creator.last_name AS creator_last_name,
          creator.email AS creator_email,
          
          -- Get sport details
          s.name AS sport_name,
          
          -- Get court details if court_id exists
          court.name AS court_name,
          
          -- Get coach details if coach_id exists
          coach_user.first_name AS coach_first_name,
          coach_user.last_name AS coach_last_name,
          
          -- Get clinic details if clinic_id exists
          clinic.name AS clinic_name,
          
          -- Determine the type based on the provided logic
          CASE
            WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
            WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
            WHEN b.court_id IS NOT NULL THEN 'Court'
            WHEN b.coach_id IS NOT NULL THEN 'Coach'
            WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
            ELSE 'Court'
          END AS booking_type
        FROM 
          courtmatchup_reservation r
        JOIN 
          courtmatchup_booking b ON r.booking_id = b.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_user creator ON b.user_id = creator.id
        LEFT JOIN
          courtmatchup_club_court court ON b.court_id = court.id
        LEFT JOIN
          courtmatchup_coach coach ON b.coach_id = coach.id
        LEFT JOIN
          courtmatchup_user coach_user ON coach.user_id = coach_user.id
        LEFT JOIN
          courtmatchup_clinics clinic ON b.clinic_id = clinic.id
        WHERE 
          r.club_id = ${club_id}
      `;
      
      // Add date filter if provided
      if (date) {
        sql += ` AND b.date = '${sqlDateFormat(new Date(date))}'`;
      }
      
      // Add status filter if provided
      if (status) {
        sql += ` AND r.status = ${parseInt(status)}`;
      }
      
      // Add type filter if provided
      if (type) {
        if (type === 'Clinic') {
          sql += ` AND b.clinic_id IS NOT NULL`;
        } else if (type === 'Lesson') {
          sql += ` AND b.lesson_id IS NOT NULL`;
        } else if (type === 'Court') {
          sql += ` AND b.court_id IS NOT NULL AND b.coach_id IS NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
        } else if (type === 'Coach') {
          sql += ` AND b.coach_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
        } else if (type === 'FindBuddy') {
          sql += ` AND r.buddy_id IS NOT NULL`;
        }
      }
      
      // Order by date and time
      sql += ` ORDER BY b.date ASC, b.start_time ASC`;
      
      const reservations = await sdk.rawQuery(sql);
      
      // Process the reservations data
      const processed_reservations = reservations.map(reservation => {
        // Parse JSON fields
        reservation.space_assigned = reservation.space_assigned ? JSON.parse(reservation.space_assigned) : [];
        reservation.player_ids = reservation.player_ids ? reservation.player_ids.split(',').map(id => parseInt(id)) : [];
        
        return reservation;
      });
      
      return res.status(200).json({
        error: false,
        reservations: processed_reservations,
        count: processed_reservations.length
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post(base, middlewares, permissionCheck('reservation_create'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      let {
        sport_id,
        type,
        date,
        start_time,
        end_time,
        duration,
        sub_type:subtype,
        court_id,
        court_ids=[],
        coach_ids=[],
        coach_id=null,
        clinic_id=null,
        court_fee,
        recurring=0,
        custom_request=0,
        reservation_type=1,
        space_assigned=[],
        notes="",
        player_ids,
        buddy_request = 0,
        buddy_details = null,
        payment_status = 0,
        payment_details = null,
      } = req.body;

      // if (await reservation_hours_left(sdk, req.user_id) + duration > 20) {
      //   throw new Error("Reservation hours exceeded for the week");
      // }

      if (!sport_id || !type || !date || !start_time || !end_time || !player_ids) {
        throw new Error("All fields are required");
      }

      if (!duration) {
        // Calculate duration based on start and end time in hours
        const [startHours, startMinutes] = start_time.split(':').map(Number);
        const [endHours, endMinutes] = end_time.split(':').map(Number);
        
        // Convert start and end times to total minutes
        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;
      
        // Calculate the difference in minutes and convert to hours
        duration = (endTotalMinutes - startTotalMinutes) / 60;
      }
      

      if (buddy_request === 1 && !buddy_details) {
        throw new Error("Please enter buddy details");
      }

      if (reservation_type === 3 && custom_request === 0 && !coach_id) {
        throw new Error("Please select a coach");
      }

      if (reservation_type === 2 && !clinic_id) {
        throw new Error("Please select a clinic");
      }else if (reservation_type === 2 && clinic_id) {
        sdk.setTable("clinics");
        const clinic = (await sdk.get({ id: clinic_id }))[0];
        if (!clinic) {
          throw new Error("Invalid clinic ID");
        }
        const clinic_date = new Date(clinic.date);
        if (clinic.recurring == 0 && clinic_date < new Date()) {
          throw new Error("Invalid clinic date");
        }

        start_time = (clinic.start_time)
        end_time = (clinic.end_time)

      }

      if (court_id) {
        const checkSql = `
          SELECT 
            b.id,
            b.date,
            b.start_time,
            b.end_time,
            b.court_id
          FROM 
            courtmatchup_booking b
          WHERE 
            b.court_id = ${court_id}
            AND b.date = '${sqlDateFormat(new Date(date))}'
            AND (
              (b.start_time <= '${formatTimeForMySQL(start_time)}' AND b.end_time > '${formatTimeForMySQL(start_time)}')
              OR 
              (b.start_time < '${formatTimeForMySQL(end_time)}' AND b.end_time >= '${formatTimeForMySQL(end_time)}')
              OR 
              (b.start_time >= '${formatTimeForMySQL(start_time)}' AND b.end_time <= '${formatTimeForMySQL(end_time)}')
            )
        `;
        
        const existingEvents = await sdk.rawQuery(checkSql);
        
        if (existingEvents.length > 0) {
          throw new Error("Cannot create event on this court due to an event already existing at that date/time.");
        }
      }


      sdk.setTable("clubs")
      const club = (await sdk.get({ id: req.club_id }))[0];
      if (!club) {
        throw new Error("Club not found");
      }

      // Create booking
      sdk.setTable("booking");
      // random numbers of length 11 for receipt_id
      const receipt_id = `${Math.floor(Math.random() * 10000000000000000)}`.slice(-11);

      const booking_id = await sdk.insert(filterEmptyFields({
        user_id: req.user_id,
        sport_id,
        type,
        subtype,
        status: payment_status,
        clinic_id,
        coach_id,
        club_id: req.club_id,
        court_id,
        lesson_id: null,
        duration,
        reservation_type,
        receipt_id,
        custom_request,
        payment_intent: payment_details ? payment_details.payment_intent : null,
        court_fee,
        player_id: player_ids[0],
        player_ids: JSON.stringify(player_ids),
        court_ids: JSON.stringify(court_ids),
        coach_ids: JSON.stringify(coach_ids),
        date: sqlDateFormat(new Date(date)),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      }));

      // Create reservation
      sdk.setTable("reservation");
      const reservation_id = await sdk.insert({
        booking_id,
        buddy_id: null,
        user_id: req.user_id,
        club_id: req.club_id,
        recurring,
        space_assigned: JSON.stringify(space_assigned),
        notes,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      // Add players to reservation team
      sdk.setTable("reservation_team");
      for (let i = 0; i < player_ids.length; i++) {
        await sdk.insert({
          user_id: player_ids[i],
          reservation_id,
          booking_id,
          creator: req.user_id === player_ids[i] ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });
      }

      await log_reservation(sdk, req.user_id, reservation_id);

      // Handle buddy request
      if (buddy_request === 1) {
        const { ntrp, num_players, num_needed = 1, type, need_coach = 0, notes = "" } = buddy_details;
        sdk.setTable("buddy");
        const buddy_id = await sdk.insert({
          sport_id,
          type,
          ntrp,
          reservation_id,
          num_players,
          num_needed,
          need_coach,
          date: sqlDateFormat(new Date(date)),
          start_time,
          end_time,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        sdk.setTable("reservation");
        await sdk.update({ buddy_id: buddy_id }, reservation_id);
      }

      return res.status(200).json({ error: false, reservation_id });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });
  
  // Get availability for club's courts, coaches, etc.
  app.get(`${base}/availability`, middlewares, permissionCheck('availability_dashboard'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { club_id } = req;
      const { date, time } = req.query;

      // Validate club_id
      if (!club_id) {
        throw new Error("Club ID is required");
      }

      // Get available courts
      const courts_sql = `
        SELECT 
          cc.id,
          cc.name,
          cc.type,
          cc.sub_type as subtype,
          cc.sport_id,
          cc.surface_id,
          cs.name as sport_name,
          csr.name as surface_name
        FROM 
          courtmatchup_club_court cc
        LEFT JOIN
          courtmatchup_sports cs ON cc.sport_id = cs.id
        LEFT JOIN
          courtmatchup_surface csr ON cc.surface_id = csr.id
        WHERE 
          cc.club_id = ${club_id}
          ${date ? `AND cc.id NOT IN (
            SELECT court_id FROM courtmatchup_booking 
            WHERE date = '${sqlDateFormat(new Date(date))}'
            ${time ? `AND '${time}' BETWEEN start_time AND end_time` : ''}
            AND status = 1
          )` : ''}
        GROUP BY 
          cc.id
      `;

      // Get available coaches with their sports
      const coaches_sql = `
        SELECT 
          c.id as coach_id,
          c.user_id,
          c.bio,
          c.hourly_rate,
          c.availability,
          u.first_name,
          u.last_name,
          u.email,
          u.photo,
          GROUP_CONCAT(
            DISTINCT CONCAT(
              cs.sport_id, '=',
              cs.type, '=',
              cs.sub_type, '=',
              cs.price, '=',
              s.name
            )
          ) as coach_sports
        FROM 
          courtmatchup_coach c
        JOIN 
          courtmatchup_user u ON c.user_id = u.id
        LEFT JOIN
          courtmatchup_coach_sports cs ON c.id = cs.coach_id
        LEFT JOIN
          courtmatchup_sports s ON cs.sport_id = s.id
        WHERE 
          c.club_id = ${club_id}
          AND u.role = 'coach'
          ${date ? `AND c.id NOT IN (
            SELECT coach_id FROM courtmatchup_booking 
            WHERE date = '${sqlDateFormat(new Date(date))}'
            ${time ? `AND '${time}' BETWEEN start_time AND end_time` : ''}
            AND status = 1
          )` : ''}
        GROUP BY 
          c.id
      `;

      // Get available staff
      const staff_sql = `
        SELECT 
          s.id as staff_id,
          s.user_id,
          u.role as staff_role,
          u.first_name,
          u.last_name,
          u.email,
          u.photo
        FROM 
          courtmatchup_staff s
        JOIN 
          courtmatchup_user u ON s.user_id = u.id
        WHERE 
          s.club_id = ${club_id}
          AND u.role = 'staff'
      `;

      // Execute all queries in parallel
      const [courts, coaches, staff] = await Promise.all([
        sdk.rawQuery(courts_sql),
        sdk.rawQuery(coaches_sql),
        sdk.rawQuery(staff_sql)
      ]);

      // Process coaches data to parse JSON fields and format sports
      const processed_coaches = coaches.map(coach => {
        const coach_sports = coach.coach_sports ? 
          coach.coach_sports.split(',').map(sport => {
            const [sport_id, type, sub_type, price, sport_name] = sport.split('=');
            return {
              sport_id: parseInt(sport_id),
              type,
              sub_type,
              price: parseInt(price),
              sport_name
            };
          }) : [];

        return {
          ...coach,
          availability: coach.availability ? JSON.parse(coach.availability) : [],
          sports: coach_sports
        };
      });

      // Process staff data to parse JSON fields
      const processed_staff = staff.map(staff_member => ({
        ...staff_member,
        availability: staff_member.availability ? JSON.parse(staff_member.availability) : []
      }));

      return res.status(200).json({
        error: false,
        courts: {
          available: courts,
          total: courts.length
        },
        hours: {
          available: Array.from({ length: 8 }, (_, i) => i + 3), // Example: 3-11
          total: 8
        },
        coaches: {
          available: processed_coaches,
          total: processed_coaches.length
        },
        staff: {
          available: processed_staff,
          total: processed_staff.length
        },
        schedule: date ? sqlDateFormat(new Date(date)) : null
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


      // Get staff billing history/invoices
      app.get(base + "/billing/staff-invoices", middlewares, async function (req, res) {
        try {
          let sdk = req.sdk;
          sdk.getDatabase();
          sdk.setProjectId(req.projectId);
      
          // Get query parameters
          const { sort = 'desc', invoice_type } = req.query;
          
          // Validate sort parameter
          if (sort !== 'asc' && sort !== 'desc') {
            throw new Error("Sort parameter must be 'asc' or 'desc'");
          }
    
          // First get the user's stripe_uid
          sdk.setTable("user");
          const user = await sdk.get({ id: req.user_id });
          if (!user.length) {
            throw new Error("User not found");
          }
      
          return res.status(200).json({
            error: false,
            invoices: [],
            invoices_by_type: {},
            total_earnings: 0,
            total_invoices: 0,
            filters: {
              sort,
              invoice_type: invoice_type || 'all'
            }
          });
      
        } catch (err) {
          console.error(err);
          res.status(403).json({
            error: true,
            message: err.message
          });
        }
      });
  
  
  // Add sports management (requires permission)
  app.post(`${base}/sports`, middlewares, permissionCheck('court_management'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const {
        name,
        status = 1,
        sport_types = []
      } = req.body;
      
      if (!name) {
        throw new Error("Sport name is required");
      }
      
      // Check if sport with this name already exists for this club
      sdk.setTable('sports');
      const existing = await sdk.get({
        club_id,
        name
      });
      
      if (existing.length > 0) {
        return res.status(403).json({
          error: true,
          message: "Sport with this name already exists"
        });
      }
      
      // Create the sport
      const sport_id = await sdk.insert({
        club_id,
        name,
        status,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
      
      // Add sport types if provided
      if (sport_types.length > 0) {
        sdk.setTable('club_sport_type');
        for (const type of sport_types) {
          await sdk.insert({
            sport_id,
            name: type.name,
            subtype: JSON.stringify(type.subtype || []),
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }
      }
      
      return res.status(200).json({
        error: false,
        message: "Sport created successfully",
        sport_id
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  
  // Update sport (requires permission)
  app.put(`${base}/sports/:id`, middlewares, permissionCheck('court_management'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { id } = req.params;
      const {
        name,
        status,
        sport_types = []
      } = req.body;
      
      // Verify the sport exists and belongs to this club
      sdk.setTable('sports');
      const sport = await sdk.get({
        id,
        club_id
      });
      
      if (!sport.length) {
        return res.status(404).json({
          error: true,
          message: "Sport not found or doesn't belong to this club"
        });
      }
      
      // Update sport
      const updateFields = filterEmptyFields({
        name,
        status,
        update_at: sqlDateTimeFormat(new Date())
      });
      
      if (Object.keys(updateFields).length > 0) {
        await sdk.update(updateFields, id);
      }
      
      // Handle sport types
      if (sport_types.length > 0) {
        sdk.setTable('club_sport_type');
        
        // Get existing types
        const existing = await sdk.get({
          sport_id: id
        });
        
        // Create a map of existing types by ID
        const existingMap = {};
        existing.forEach(type => {
          existingMap[type.id] = type;
        });
        
        // Process each type in the request
        for (const type of sport_types) {
          if (type.id && existingMap[type.id]) {
            // Update existing type
            await sdk.update({
              name: type.name,
              subtype: JSON.stringify(type.subtype || []),
              update_at: sqlDateTimeFormat(new Date())
            }, type.id);
            
            // Remove from map to track which ones to keep
            delete existingMap[type.id];
          } else {
            // Add new type
            await sdk.insert({
              sport_id: id,
              name: type.name,
              subtype: JSON.stringify(type.subtype || []),
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            });
          }
        }
        
        // Delete types that weren't included in the update
        const typeIdsToDelete = Object.keys(existingMap);
        for (const typeId of typeIdsToDelete) {
          await sdk.delete({
            id: typeId
          });
        }
      }
      
      return res.status(200).json({
        error: false,
        message: "Sport updated successfully"
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  
  // Create clinic (requires permission)
  app.post(`${base}/clinics`, middlewares, permissionCheck('clinics'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const {
        name,
        description,
        sport_id,
        type,
        sub_type: subtype,
        level,
        date,
        start_time,
        end_time,
        cost_per_head,
        max_participants,
        recurring = 0,
        days = [],
        end_date = null,
        coach_ids = []
      } = req.body;
      
      // Validation
      if (!name || !sport_id || !date || !start_time || !end_time || !cost_per_head) {
        throw new Error("Required fields missing");
      }
      
      // Insert clinic
      sdk.setTable('clinics');
      const clinic_id = await sdk.insert({
        club_id,
        name,
        description,
        sport_id,
        type,
        subtype,
        level,
        date: sqlDateFormat(new Date(date)),
        start_time,
        end_time,
        cost_per_head,
        max_participants,
        recurring,
        days: days.length ? JSON.stringify(days) : null,
        end_date: end_date ? sqlDateFormat(new Date(end_date)) : null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
      
      // Add coaches if provided
      if (coach_ids.length > 0) {
        sdk.setTable('clinic_coaches');
        for (const coach_id of coach_ids) {
          await sdk.insert({
            clinic_id,
            coach_id,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }
      }
      
      return res.status(200).json({
        error: false,
        message: "Clinic created successfully",
        clinic_id
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  
  // Get invoice for specific reservation
  app.get(`${base}/:id/invoice`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const reservation_id = req.params.id;
      
      if (!reservation_id) {
        return res.status(400).json({
          error: true,
          message: "Reservation ID is required"
        });
      }
      
      // Get reservation
      sdk.setTable("reservation");
      const reservation = await sdk.get({
        id: reservation_id
      });
      
      if (!reservation.length) {
        return res.status(404).json({
          error: true,
          message: "Reservation not found"
        });
      }
      
      const booking_id = reservation[0].booking_id;
      
      if (!booking_id) {
        return res.status(404).json({
          error: true,
          message: "No booking associated with this reservation"
        });
      }
      
      // Get booking/invoice details
      const invoiceSql = `
        SELECT 
          b.id AS invoice_id,
          b.payment_intent,
          b.price AS amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.date AS invoice_date,
          b.create_at,
          b.status,
          b.start_time,
          b.end_time,
          b.duration,
          b.reservation_type,
          b.notes,
          b.player_ids,
          b.type AS booking_type,
          b.subtype AS booking_subtype,
          b.last_4,
          b.custom_request,
          
          u.id AS customer_id,
          u.first_name AS customer_first_name,
          u.last_name AS customer_last_name,
          u.email AS customer_email,
          u.phone AS customer_phone,
          
          s.id AS sport_id,
          s.name AS sport_name,
          
          cc.name AS court_name,
          
          coach.id AS coach_id,
          coach_user.first_name AS coach_first_name,
          coach_user.last_name AS coach_last_name,
          
          clinic.name AS clinic_name,
          
          (COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0)) AS total_amount,
          
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS invoice_type
        FROM 
          courtmatchup_booking b
        JOIN 
          courtmatchup_user u ON b.user_id = u.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_club_court cc ON b.court_id = cc.id
        LEFT JOIN
          courtmatchup_coach coach ON b.coach_id = coach.id
        LEFT JOIN
          courtmatchup_user coach_user ON coach.user_id = coach_user.id
        LEFT JOIN
          courtmatchup_clinics clinic ON b.clinic_id = clinic.id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.id = ${booking_id}
      `;
      
      const invoice = await sdk.rawQuery(invoiceSql);
      
      if (!invoice.length) {
        return res.status(404).json({
          error: true,
          message: "Invoice not found or does not belong to this club"
        });
      }
      
      // Get all reservations for this booking
      const allReservationsSql = `
        SELECT 
          r.id AS reservation_id,
          r.user_id,
          r.status AS reservation_status,
          r.create_at,
          r.update_at,
          r.notes,
          r.buddy_id,
          r.space_assigned,
          
          u.first_name,
          u.last_name,
          u.email,
          u.phone
        FROM 
          courtmatchup_reservation r
        JOIN
          courtmatchup_user u ON r.user_id = u.id
        WHERE 
          r.booking_id = ${booking_id}
      `;
      
      const allReservations = await sdk.rawQuery(allReservationsSql);
      
      return res.status(200).json({
        error: false,
        invoice: invoice[0],
        reservation: reservation[0],
        all_reservations: allReservations
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  
  return [
    {
      method: "GET",
      name: "Staff Reservations API",
      url: "/v3/api/custom/courtmatchup/staff/reservations",
      successPayload: '{"error":false,"reservations":[],"count":0}',
      needToken: true
    }
  ];
};
