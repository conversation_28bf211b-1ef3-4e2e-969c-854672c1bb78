const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];


module.exports = function (app) {
  app.post("/v3/api/custom/cedric/customer/support", middlewares, async function (req, res) {
        try {
          const {sdk, user_id} = req;
          const { message } = req.body;
          sdk.getDatabase();
          sdk.setProjectId(req.projectId);
          sdk.setTable("user");
          if(!message) {
            return res.status(403).json({
              error: true,
              message: "Message is required"
            });
          }
          sdk.setTable("support");
          const save = await sdk.insert({
            user_id,
            message,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date()),
          })   
          return res.status(200).json({
            error: false,
            message: "Message sent successfully",
          });
        } catch (err) {
          console.log(err);
          res.status(403);
          res.json({
            error: true,
            message: err.message,
          });
        }
      });
  return [
    {
      method: "POST",
      name: "Support API",
      url: "/v3/api/custom/profile",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
      ],
      needToken: true
    }
  ];
};
