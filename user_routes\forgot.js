const AuthService = require("../../../services/AuthService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const DevLogService = require("../../../services/DevLogService");
const MkdEventService = require("../../../services/MkdEventService");
const config = require("../../../config");
const TwilloSmsService = require("../../../services/TwilloSmsService");
const { reset } = require("../utils/util");
const UploadService = require("../../../services/UploadService");
const { sqlDateFormat, sqlDateTimeFormat, sizeOfRemote } = require("../../../services/UtilService");
const sizeOf = require("image-size");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const uploadS3 = UploadService.s3_upload();

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, PermissionMiddleware];


const imageMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  // TokenMiddleware(),
  uploadS3.single("file"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];


let logService = new DevLogService();

module.exports = function (app) {

  app.post("/v3/api/custom/courtmatchup/default/s3/upload", imageMiddlewaresS3, async function (req, res) {
    try {
      const url = req.file.location;

      let params = {
        url: url,
        user_id: null,
        caption: req.body.caption || null,
        type: 1,
        width: 0,
        height: 0,
      };
      const whitelist = ["image/png", "image/jpeg", "image/jpg"];

      if (whitelist.includes(req.file.mimetype)) {
        const dimensions = await sizeOfRemote(url);
        params.width = dimensions.width;
        params.height = dimensions.height;
        params.type = 0;
      }

      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("photo");

      logService.log(req.body);
      logService.log(req.user_id);

      const result = await sdk.insert({
        url: params.url,
        caption: params.caption,
        user_id: null,
        width: params.width,
        height: params.height,
        type: params.type,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      res.set('Content-Type', 'application/json');
      return res.status(201).json({ id: result, url });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  app.post("/v3/api/custom/courtmatchup/default/forgot", middlewares, async function (req, res) {
    try {
      if (!req.body.phone) {
        return res.status(403).json({
          error: true,
          message: "Phone number Missing",
          validation: [{ field: "phone", message: "Phone number missing" }]
        });
      }

      let service = new AuthService();
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        phone: req.body.phone,
        ...(req.body.role && { role: req.body.role })
      });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (result.length != 1) {
        return res.status(403).json({
          error: true,
          message: "Cannot find User"
        });
      }

      if (result[0].oauth) {
        return res.status(403).json({
          error: true,
          message: "Cannot do forgot password on single sign on"
        });
      }

      const forgotResult = await service.forgot(sdk, req.projectId, req.body.phone, result[0].id);

      if (typeof forgotResult == "string") {
        return res.status(403).json({
          error: true,
          message: forgotResult
        });
      }

    //   const eventService = new MkdEventService(sdk, req.projectId, req.headers);

      let projectRow = {};

      if (config.env == "production") {
        projectRow = require("../../../../project");
      } else {
        sdk.setDatabase("baas");
        sdk.setProjectId("manaknight");
        sdk.setTable("projects");

        projectRow = (
          await sdk.get({
            slug: req.projectId
          })
        )[0];
      }

      sdk.setDatabase(await sdk.getProjectDatabase(req.projectId));
      sdk.setProjectId(req.projectId);

    //   await eventService.sendMail(
    //     {
    //       email: req.body.email,
    //       to: req.body.email,
    //       link: "https://" + projectRow.hostname + "/" + (req.body.role ? req.body.role : "admin") + "/reset?token=" + forgotResult.token,
    //       from: config.from_mail,
    //       code: forgotResult.code,
    //       token: forgotResult.token
    //     },
    //     "reset-password"
    //   );
    // check if there is + in phone number
    if (req.body.phone.includes("+")) {
      // your code for CourtMatchup is
      await TwilloSmsService.send(req.body.phone,`Your code for CourtMatchup is ${forgotResult.code}.`)
    }else{
        await TwilloSmsService.send("+" + req.body.phone,`Your code for CourtMatchup is ${forgotResult.code}.`)
    }

      return res.status(200).json({
        error: false,
        message: "sms Sent"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/courtmatchup/default/reset", middlewares, async function (req, res) {
    try {
      if (!req.body.code || isNaN(req.body.code)) {
        return res.status(403).json({
          error: true,
          message: "Invalid Reset Code",
          validation: [{ field: "code", message: "Invalid Reset Code" }]
        });
      }

      if (!req.body.phone) {
        return res.status(403).json({
          error: true,
          message: "Phone number Missing",
          validation: [{ field: "phone", message: "Phone number missing" }]
        });
      }

      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");

      const user = await sdk.get({
        phone: req.body.phone,
      });

      if (user.length <= 0) {
        return res.status(403).json({
          error: true,
          message: "Cannot find User"
        });
      }

      sdk.setTable("token");
      const result = await sdk.get({
        user_id: user[0].id
      });

      if (!result.length){
        return res.status(403).json({
          error: true,
          message: "Invalid Reset Request"
        });
      }

    //   if (result.length != 1) {
    //     return res.status(403).json({
    //       error: true,
    //       message: "Invalid Reset Request"
    //     });
    //   }

      const data = JSON.parse(result[0].data);
      const code = data.code;

      logService.log(result[0].code, req.body.code);

      if (req.body.code != code) {
        return res.status(403).json({
          error: true,
          message: "Mismatch Reset Code",
          validation: [{ field: "code", message: "Mismatch Reset Code" }]
        });
      }else if (!req.body.password) {
        return res.status(200).json({
          error: false,
          message: "Code was successfully verified",
        });
      }

      let service = new AuthService();

      const resetResult = await reset(sdk, req.projectId, result[0], req.body.password);

      if (typeof resetResult == "string") {
        return res.status(403).json({
          error: true,
          message: resetResult
        });
      }

      sdk.setTable("token");
      await sdk.delete({ }, result[0].id);

      return res.status(200).json({
        error: false,
        message: "Reset Password"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Forgot Password API",
      url: "/v2/api/lambda/forgot",
      successBody: '{ "email": "<EMAIL>", "role": "admin"}',
      successPayload: '{"error": false, "message": "Email Sent"}',
      errors: [
        {
          name: "403",
          body: '{"password": "123456", "role": "admin"}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true, "message": "Cannot find user"}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true, "message": "Cannot do forgot password on single sign on"}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    },
    {
      method: "POST",
      name: "Mobile Forgot Password API",
      url: "/v2/api/lambda/mobile/forgot",
      successBody: '{ "email": "<EMAIL>"}',
      successPayload: '{"error": false, "message": "Email Sent"}',
      errors: [
        {
          name: "403",
          body: '{"password": "123456", "role": "admin"}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true, "message": "Cannot find user"}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true, "message": "Cannot do forgot password on single sign on"}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    }
  ];
};
