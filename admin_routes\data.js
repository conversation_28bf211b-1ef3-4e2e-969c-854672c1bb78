const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const ValidationService = require("../../../services/ValidationService");
const PaginationService = require("../../../services/PaginationService");
const { saveCount } = require("../services/logs");
const { emitEvent, getCount } = require("../services/EmitEventService");
const { sendNotification } = require("../services/PushNotificaionService");
const MkdEventService = require("../services/MkdEventService");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware
  // TokenMiddleware(),
];
const grouping_sql = `
cedric_professional_profile.user_id,
cedric_user.id, 
cedric_user.first_name, 
cedric_user.last_name, 
cedric_user.phone, 
cedric_user.email, 
cedric_user.photo, 
cedric_professional_profile.id,
cedric_professional_profile.user_id,
cedric_professional_profile.bio,
cedric_professional_profile.profession,
cedric_professional_profile.category,
cedric_professional_profile.address,
cedric_professional_profile.subcategory,
cedric_professional_profile.experience,
cedric_professional_profile.tiktok_url,
cedric_professional_profile.facebook_url,
cedric_professional_profile.instagram_url,
cedric_professional_profile.cover_photo,
cedric_professional_profile.cover_top,
cedric_professional_profile.cover_left,
cedric_professional_profile.details,
cedric_professional_profile.country,
cedric_professional_profile.island,
cedric_professional_profile.city,
cedric_professional_profile.enterprise,
cedric_professional_profile.visible,
cedric_professional_profile.hidden,
cedric_professional_profile.joined,
cedric_professional_profile.create_at
`;

module.exports = function (app) {
  app.get("/v3/api/custom/customer/category", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      //   sdk.setTable("category")
      const categorySql = "SELECT * FROM cedric_category";
      const subCategorySql = "SELECT * FROM cedric_subcategory";

      const categories = await sdk.rawQuery(categorySql);
      const subCategories = await sdk.rawQuery(subCategorySql);
      console.log(categories);

      categories.forEach(function (category) {
        const children = subCategories.filter((child) => child.category_id === category.id);
        category.subCategories = children;
        delete category.sub_category_id;
      });

      return res.status(200).json({
        error: false,
        list: categories,
        total: categories.length
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/registration/complete", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("user");
      const user = await sdk.get({
        id: req.user_id
      });

      if (user.length <= 0) {
        return res.status(400).json({ error: true, message: "Invalid user" });
      }

      if (user[0].complete_c === 1) {
        return res.status(400).json({ error: true, message: "User already completed registration" });
      } else {
        if (req.query.complete_c) {
          await sdk.update(
            {
              complete_c: 1
            },
            req.user_id
          );
          // send welcome mail
          const eventService = new MkdEventService(sdk, req.projectId, req.headers);
          await eventService.sendWelcomeMail({
            to: user[0].email,
          })
        }
        if (user[0].complete_p === 1) {
          
        } else {
          if (req.query.complete_p) {
            await sdk.update(
              {
                complete_p: 1
              },
              req.user_id
            );
            // send welcome mail
            const eventService = new MkdEventService(sdk, req.projectId, req.headers);
            await eventService.sendWelcomeMail({
              to: user[0].email,
            })
          }
        }
      }

      return res.status(200).json({
        error: false,
        message: "Registration complete"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/customer/islands", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const islandsSql = "SELECT * FROM cedric_islands";
      const citiesSql = "SELECT * FROM cedric_cities";

      const islands = await sdk.rawQuery(islandsSql);
      const cities = await sdk.rawQuery(citiesSql);

      islands.forEach(function (island) {
        const children = cities.filter((child) => child.island_id === island.id);
        island.cities = children;
      });
      return res.status(200).json({
        error: false,
        list: islands,
        total: islands.length
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/customer/autocomplete", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { search } = req.query;
      const islandSql = `SELECT * FROM cedric_islands WHERE cedric_islands.name LIKE '%${search}%'`;
      const categorySql = `SELECT * FROM cedric_category WHERE cedric_category.name LIKE '%${search}%'`;
      const subCategorySql = `SELECT * FROM cedric_subcategory WHERE cedric_subcategory.name LIKE '%${search}%'`;
      const islands = await sdk.rawQuery(islandSql);
      const categories = await sdk.rawQuery(categorySql);
      const subCategories = await sdk.rawQuery(subCategorySql);
      let sql = "";
      // check if search is double
      if (search.indexOf(" ") > 0) {
        const names = search.split(" ");
        sql = `SELECT first_name,last_name,email,phone, photo, cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars  FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id 
        LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island  WHERE first_name LIKE "%${names[0]}%" OR first_name LIKE "%${names[1]}%" OR last_name LIKE "%${names[0]}%" OR last_name LIKE "%${names[1]}%" OR email LIKE "%${search}%" OR phone LIKE "%${search}%" `;
      } else {
        sql = `SELECT first_name,last_name,email,phone, photo, cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars  FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id 
        LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island  WHERE first_name LIKE "%${search}%" OR last_name LIKE "%${search}%" OR email LIKE "%${search}%" OR phone LIKE "%${search}%" `;
      }

      // SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island where cedric_user.complete_p="1" ORDER BY cedric_professional_profile.create_at DESC
      if (categories.length > 0) {
        selected = categories.map((item) => item.id);
        sql += " OR ";
        selected.forEach((item, index) => {
          sql += `cedric_professional_profile.category LIKE "%${item}%" `;
          if (index < selected.length - 1) {
            console.log("here", selected.length - 1, index);
            sql += " OR ";
          }
        });
      }

      if (subCategories.length > 0) {
        selected = subCategories.map((item) => item.id);
        sql += " OR ";
        selected.forEach((item, index) => {
          sql += `cedric_professional_profile.subcategory LIKE "%${item}%" `;
          if (index < selected.length - 1) {
            sql += " OR ";
          }
        });
      }

      if (islands.length > 0) {
        selected = islands.map((item) => item.id);
        sql += " OR ";
        selected.forEach((item, index) => {
          sql += `cedric_professional_profile.island = "${item}" `;
          if (index < selected.length - 1) {
            sql += " OR ";
          }
        });
      }

      sql += ` GROUP BY cedric_professional_profile.user_id,
      ${grouping_sql},
      cedric_islands.name
      `;

      const result = await sdk.rawQuery(sql);

      return res.status(200).json({
        error: false,
        list: result
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // get professionals api
  // get the latest professionals
  // get professionals by users chosen categories and chosen islands
  // get professionals by filters passed

  app.get("/v3/api/custom/cedric/customer/professionals", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      //     `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, COUNT(cedric_rating.stars) AS num, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile
      //   ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_rating ON cedric_rating.professional_id=cedric_user.id`

      const data = req.query;
      let sql = "";
      sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island where cedric_user.complete_p="1" 
      GROUP BY cedric_professional_profile.user_id, ${grouping_sql}, cedric_islands.name
      ORDER BY cedric_professional_profile.update_at DESC
          `;

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.body.sortId);
      paginationService.setSortDirection(req.body.direction);

      const page = paginationService.getPage(),
        limit = paginationService.getLimit();
      const offset = (page - 1) * limit;

      sql += ` LIMIT ${offset} , ${limit}`;

      const mainQL = sql.split("FROM")[1];
      const endQL = mainQL.split("GROUP BY")[0];
      let sqlCount = `SELECT COUNT(*) as num FROM` + endQL;
      const count = await sdk.rawQuery(sqlCount);
      paginationService.setCount(count[0].num);
      console.log("sql", sql);

      const result = await sdk.rawQuery(sql);
      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          const starsSql = `SELECT COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_rating WHERE cedric_rating.professional_id = ${result[i].user_id}`;
          const stars = await sdk.rawQuery(starsSql);
          if (stars.length > 0) {
            result[i].total_ratings = stars[0].total_ratings;
            result[i].total_stars = stars[0].total_stars;
          }
        }
      }
      return res.status(200).json({
        error: false,
        list: result,
        total: count[0].num
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/cedric/customer/popular", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      });
      if (user.length <= 0) {
        return res.status(400).json({ error: true, message: "Invalid user" });
      }
      console.log(user[0]);
      let sql = "";
      if (req.query.category || req.query.category_ids) {
        let selected = [];
        if (req.query.category_ids) {
          selected = req.query.category_ids.split(",");
        } else {
          selected = user[0].categories ? JSON.parse(user[0].categories) : [];
        }
        console.log(selected);
        if (selected.length > 0) {
          sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island WHERE cedric_professional_profile.category IN (${selected}) AND cedric_user.complete_p="1" 
          GROUP BY cedric_professional_profile.user_id, ${grouping_sql}, cedric_islands.name
          ORDER BY cedric_professional_profile.create_at DESC`;
        } else {
          sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island where cedric_user.complete_p="1" 
          GROUP BY cedric_professional_profile.user_id, ${grouping_sql}, cedric_islands.name
          ORDER BY cedric_professional_profile.update_at DESC
              `;
        }
      } else {
        let selected = [];
        if (req.query.island_ids) {
          selected = req.query.island_ids.split(",");
        } else {
          selected = user[0].islands ? JSON.parse(user[0].islands) : [];
        }
        if (selected.length > 0) {
          sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island WHERE cedric_professional_profile.island IN (${selected}) AND cedric_user.complete_p="1" 
          GROUP BY cedric_professional_profile.user_id, ${grouping_sql}, cedric_islands.name
          ORDER BY cedric_professional_profile.update_at DESC`;
        } else {
          sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island where cedric_user.complete_p="1" 
          GROUP BY cedric_professional_profile.user_id, ${grouping_sql}, cedric_islands.name
          ORDER BY cedric_professional_profile.update_at DESC`;
        }
      }

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.body.sortId);
      paginationService.setSortDirection(req.body.direction);

      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      sql += ` LIMIT ${offset} , ${limit}`;

      const mainQL = sql.split("FROM")[1];
      const endQL = mainQL.split("GROUP BY")[0];
      let sqlCount = `SELECT COUNT(*) as num FROM` + endQL;
      const count = await sdk.rawQuery(sqlCount);
      paginationService.setCount(count[0].num);
      console.log("sql", sql);

      const result = await sdk.rawQuery(sql);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          const starsSql = `SELECT COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_rating WHERE cedric_rating.professional_id = ${result[i].user_id}`;
          const stars = await sdk.rawQuery(starsSql);
          if (stars.length > 0) {
            result[i].total_ratings = stars[0].total_ratings;
            result[i].total_stars = stars[0].total_stars;
          }
        }
      }

      return res.status(200).json({
        error: false,
        list: result,
        total: count[0].num
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/cedric/users/professionals", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      let sql = "";
      let extra = "";
      const today = sqlDateFormat(new Date());
      const order = req.query.order_by ?? "DESC";
      const sortId = req.query.sort_id ?? "cedric_professional_profile.create_at";
      let experience = "";
      const orders_sql = "(select count(*) from cedric_job where cedric_job.professional_id=cedric_user.id AND cedric_job.status=2) orders, ";
      // planetscale latest mysql only group mode restriction fix
      const grouping_sql = `
      cedric_professional_profile.user_id,
      cedric_user.id, 
      cedric_user.first_name, 
      cedric_user.last_name, 
      cedric_user.phone, 
      cedric_user.email, 
      cedric_user.photo, 
      cedric_islands.name,
      cedric_professional_profile.id,
      cedric_professional_profile.user_id,
      cedric_professional_profile.bio,
      cedric_professional_profile.profession,
      cedric_professional_profile.category,
      cedric_professional_profile.address,
      cedric_professional_profile.subcategory,
      cedric_professional_profile.experience,
      cedric_professional_profile.tiktok_url,
      cedric_professional_profile.facebook_url,
      cedric_professional_profile.instagram_url,
      cedric_professional_profile.cover_photo,
      cedric_professional_profile.cover_top,
      cedric_professional_profile.cover_left,
      cedric_professional_profile.details,
      cedric_professional_profile.country,
      cedric_professional_profile.island,
      cedric_professional_profile.city,
      cedric_professional_profile.enterprise,
      cedric_professional_profile.visible,
      cedric_professional_profile.hidden,
      cedric_professional_profile.joined,
      cedric_professional_profile.create_at
      `;
      if (req.query.subcategory || req.query.subcategory_ids) {
        let selected = [];
        if (req.query.subcategory_ids) {
          selected = req.query.subcategory_ids.split(",");
          selected.forEach((item, index) => {
            extra += `cedric_professional_profile.subcategory LIKE "%${item}%"`;
            if (index < selected.length - 1) {
              extra += " OR ";
            }
          });
        }

        /**
         * New sql
            SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name,COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id  where cedric_user.complete_p="1" GROUP BY cedric_professional_profile.user_id 
            HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) > 3 AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) < 4
            ORDER BY cedric_professional_profile.create_at DESC
         */

        console.log(selected);
        if (selected.length > 0) {
          if (req.query.experience) {
            experience =
              " DATEDIFF(" +
              today +
              ",cedric_user.create_at) >= " +
              req.query.experience.split(",")[0] +
              " AND  DATEDIFF(" +
              today +
              ",cedric_user.create_at) <= " +
              req.query.experience.split(",")[1] +
              " AND ";
          }
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id), cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,   cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id WHERE ${experience} ${extra} AND cedric_user.complete_p="1" 
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.reviews.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
          // sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.* FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id WHERE cedric_professional_profile.subcategory IN (${selected}) ORDER BY ${sortId} ${order}`
        } else {
          if (req.query.experience) {
            experience =
              " DATEDIFF(" +
              today +
              ",cedric_user.create_at) >= " +
              req.query.experience.split(",")[0] +
              " AND  DATEDIFF(" +
              today +
              ",cedric_user.create_at) <= " +
              req.query.experience.split(",")[1] +
              " AND ";
          }
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id) as profile_id, cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,  cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id where ${experience} cedric_user.complete_p="1" 
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.filter.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
        }
      } else if (req.query.category || req.query.category_ids) {
        let selected = [];
        if (req.query.category_ids) {
          selected = req.query.category_ids.split(",");
          selected.forEach((item, index) => {
            extra += `cedric_professional_profile.category LIKE "%${item}%"`;
            if (index < selected.length - 1) {
              extra += " OR ";
            }
          });
        }
        console.log(selected);
        if (req.query.experience) {
          experience =
            " DATEDIFF(" +
            today +
            ",cedric_user.create_at) >= " +
            req.query.experience.split(",")[0] +
            " AND  DATEDIFF(" +
            today +
            ",cedric_user.create_at) <= " +
            req.query.experience.split(",")[1] +
            " AND ";
        }
        if (selected.length > 0) {
          // sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.* FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id WHERE cedric_professional_profile.category IN (${selected}) ORDER BY ${sortId} ${order}`
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id) as profile_id, cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,  cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id WHERE ${experience} ${extra} AND cedric_user.complete_p="1" 
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.reviews.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
        } else {
          if (req.query.experience) {
            experience =
              " DATEDIFF(" +
              today +
              ",cedric_user.create_at) >= " +
              req.query.experience.split(",")[0] +
              " AND  DATEDIFF(" +
              today +
              ",cedric_user.create_at) <= " +
              req.query.experience.split(",")[1] +
              " AND ";
          }
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id), cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,   cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id where ${experience} cedric_user.complete_p="1" 
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.reviews.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
        }
      } else {
        let selected = [];
        let selected_cities = [];
        if (req.query.island_ids) {
          selected = req.query.island_ids.split(",");
        }
        if (req.query.island_ids) {
          selected_cities = req.query.cities.split(",");
        }
        if (req.query.experience) {
          experience =
            " DATEDIFF(" +
            today +
            ",cedric_user.create_at) >= " +
            req.query.experience.split(",")[0] +
            " AND  DATEDIFF(" +
            today +
            ",cedric_user.create_at) <= " +
            req.query.experience.split(",")[1] +
            " AND ";
        }
        if (selected_cities.length > 0) {
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id), cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,  cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id WHERE ${experience} cedric_professional_profile.city IN (${selected_cities}) AND cedric_user.complete_p="1" \
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.reviews.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
        } else {
          if (req.query.experience) {
            experience =
              " DATEDIFF(" +
              today +
              ",cedric_user.create_at) >= " +
              req.query.experience.split(",")[0] +
              " AND  DATEDIFF(" +
              today +
              ",cedric_user.create_at) <= " +
              req.query.experience.split(",")[1] +
              " AND ";
          }
          sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id), cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,   cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id where ${experience} cedric_user.complete_p="1" 
          GROUP BY ${grouping_sql}
          `;
          if (req.query.reviews) {
            const action = req.query.action ?? ">=";
            const filter = req.query.reviews.split(",");
            if (filter.length > 1) {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
            } else {
              sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
            }
          }
        }
      }

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.query.sortId);
      paginationService.setSortDirection(req.query.direction);

      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      const mainQL = sql.split("FROM")[1];
      const endQL = mainQL.split("GROUP BY")[0];
      let sqlCount = `SELECT COUNT(*) as num FROM` + endQL;
      sql += ` ORDER BY ${sortId} ${order}`;
      sql += ` LIMIT ${offset} , ${limit}`;
      const count = await sdk.rawQuery(sqlCount);
      paginationService.setCount(count.length);
      console.log("sql", sql);

      const result = await sdk.rawQuery(sql);

      for (let i = 0; i < result.length; i++) {
        const current = result[i];
        const experience = `SELECT DATEDIFF("${today}",cedric_user.create_at) AS experience FROM cedric_user WHERE cedric_user.id=${current.user_id}`;
        current.experience = (await sdk.rawQuery(experience))[0]?.experience;
      }

      // console.log(result)

      // if(result.length > 0){
      //   for(let i = 0; i < result.length; i++){
      //     const starsSql = `SELECT COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_rating WHERE cedric_rating.professional_id = ${result[i].user_id}`
      //     const stars = await sdk.rawQuery(starsSql);
      //     if(stars.length > 0){
      //       result[i].total_ratings = stars[0].total_ratings
      //       result[i].total_stars = stars[0].total_stars
      //     }
      //   }
      // }

      return res.status(200).json({
        error: false,
        list: result,
        total: count.length
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/cedric/users/professionals/GET", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      let experience = "";
      if (req.query.experience) {
        experience =
          " DATEDIFF(" +
          today +
          ",cedric_user.create_at) >= " +
          req.query.experience.split(",")[0] +
          " AND  DATEDIFF(" +
          today +
          ",cedric_user.create_at) <= " +
          req.query.experience.split(",")[1] +
          "AND";
      }
      const groupBy = `cedric_user.complete_p="1" GROUP BY 
      cedric_professional_profile.user_id,
      ${grouping_sql}
      `;
      const orders_sql = "(select count(*) from cedric_job where cedric_job.professional_id=cedric_user.id AND cedric_job.status=2) orders, ";
      let sql = `SELECT ${orders_sql}  MAX(cedric_professional_profile.id) as profile_id, cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo,  cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id WHERE ${experience}`;
      let extra = "";
      const today = sqlDateFormat(new Date());
      const order = req.query.order_by ?? "DESC";
      const sortId = req.query.sort_id ?? "cedric_professional_profile.create_at";

      if (req.query.subcategory || req.query.subcategory_ids) {
        let selected = [];
        if (req.query.subcategory_ids) {
          selected = req.query.subcategory_ids.split(",");
          selected.forEach((item, index) => {
            extra += `cedric_professional_profile.subcategory LIKE "%${item}%"`;
            if (index < selected.length - 1) {
              extra += " OR ";
            }
          });
        }
        sql += `${extra} AND `;
      }

      let selected = [];
      let selected_cities = [];
      if (req.query.island_ids) {
        selected = req.query.island_ids.split(",");
      }
      if (req.query.island_ids) {
        selected_cities = req.query.cities.split(",");
      }

      if (selected_cities.length > 0) {
        sql += `cedric_professional_profile.city IN (${selected_cities}) AND `;
      }
      sql += groupBy;
      if (req.query.reviews) {
        const action = req.query.action ?? ">";
        const filter = req.query.reviews.split(",");
        if (filter.length > 1) {
          sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) >= ${filter[0]} AND (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) <= ${filter[1]}`;
        } else {
          sql += ` HAVING (SUM(cedric_rating.stars)/COUNT(cedric_rating.stars)) ${action} ${filter[0]}`;
        }
      }

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.query.sortId);
      paginationService.setSortDirection(req.query.direction);

      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      const mainQL = sql.split("FROM")[1];
      const endQL = mainQL.split("GROUP BY")[0];
      let sqlCount = `SELECT COUNT(*) as num FROM` + endQL;
      sql += ` ORDER BY ${sortId} ${order}`;
      sql += ` LIMIT ${offset} , ${limit}`;
      const count = await sdk.rawQuery(sqlCount);
      paginationService.setCount(count.length);
      console.log("sql", sql);

      const result = await sdk.rawQuery(sql);

      for (let i = 0; i < result.length; i++) {
        const current = result[i];
        const experience = `SELECT DATEDIFF("${today}",cedric_user.create_at) AS experience FROM cedric_user WHERE cedric_user.id=${current.user_id}`;
        current.experience = (await sdk.rawQuery(experience))[0]?.experience;
      }

      console.log(result);

      return res.status(200).json({
        error: false,
        list: result,
        total: count.length
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/cedric/room/my", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      // req.user_id = 378;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      console.log("****", req.projectId);
      sdk.setTable("room");
      let finalResult = [];
      const room1Sql = `SELECT * FROM cedric_room WHERE cedric_room.user_id = "${req.user_id}" AND cedric_room.delete_for != "${req.user_id}"`;
      const room2Sql = `SELECT * FROM cedric_room WHERE cedric_room.other_user_id = "${req.user_id}" AND cedric_room.delete_for != "${req.user_id}"`;
      // const result = await sdk.get({
      //   user_id: req.user_id,
      //   delete_for:0
      // });
      // const result2 = await sdk.get({
      //   other_user_id: req.user_id
      // });
      const result = await sdk.rawQuery(room1Sql);
      const result2 = await sdk.rawQuery(room2Sql);
      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }
      for (let i = 0; i < result.length; i++) {
        const sql = "SELECT * FROM cedric_chat WHERE cedric_chat.room_id = " + result[i].id + " ORDER BY cedric_chat.update_at DESC LIMIT 1";
        const chat = await sdk.rawQuery(sql);
        if (chat.length > 0) {
          result[i].last_message = chat[0];
        } else {
          result[i].last_message = { create_at: new Date().toISOString().split("T")[0].replace(/-/g, "/") };
        }
      }

      finalResult = result;

      if (typeof result2 == "string") {
        return res.status(403).json({
          error: true,
          message: result2
        });
      }

      for (let i = 0; i < result2.length; i++) {
        const sql = "SELECT * FROM cedric_chat WHERE cedric_chat.room_id = " + result2[i].id + " ORDER BY cedric_chat.update_at DESC LIMIT 1";
        const chat = await sdk.rawQuery(sql);
        if (chat.length > 0) {
          result2[i].last_message = chat[0];
        } else {
          result2[i].last_message = { create_at: new Date().toISOString().split("T")[0].replace(/-/g, "/") };
        }
      }

      if (result2.length) {
        finalResult = finalResult.concat(
          result2.map((i) => {
            return {
              ...i,
              other_user_id: i.user_id,
              user_id: req.user_id
            };
          })
        );
      }

      let projectId = req.projectId;

      // const messages = await sdk.rawQuery(
      //   `SELECT ${projectId}_chat.* FROM ${projectId}_chat LEFT JOIN ${projectId}_room ON ${projectId}_chat.room_id = ${projectId}_room.id WHERE (${projectId}_room.user_id = ${req.user_id} OR ${projectId}_room.other_user_id = ${req.user_id}) AND ${projectId}_chat.unread = 1 ORDER BY ${projectId}_chat.create_at DESC`
      // );

      finalResult = finalResult.sort((a, b) => new Date(b?.last_message.create_at) - new Date(a?.last_message.create_at));

      let data = [];
      for (let i = 0; i < finalResult.length; i++) {
        sdk.setTable("user");
        const user = await sdk.get(
          {
            id: finalResult[i].other_user_id
          },
          "id, role, first_name, last_name, email, photo"
        );

        console.log("user is ", user);

        if (user.length > 0) {
          let details = { ...finalResult[i], unread: undefined, chat_id: undefined };
          details.first_name = user[0].first_name;
          details.last_name = user[0].last_name;
          details.email = user[0].email;
          details.photo = user[0].photo;
          data.push(details);
        }
      }

      return res.status(200).json({
        error: false,
        list: data,
        rooms: finalResult
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/cedric/emit-test", [...middlewares], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      req.user_id = 394;
      sdk.setProjectId(req.projectId);
      try {
        await emitEvent(req.app, req.user_id, sdk, (other = false), req.projectId);
      } catch (e) {
        console.log(e);
      }

      return res.status(200).json({
        error: false,
        message: "hire request sent"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/cedric/opened", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("activity_log");
      const { id, all = false } = req.query;

      if (all) {
        await sdk.updateWhere(
          {
            viewed: 1
          },
          {
            for_user_id: req.user_id
          }
        );
        return res.status(200).json({
          error: false,
          message: "Notifications opened successfully"
        });
      }

      if (!id) {
        return res.status(400).json({ error: true, message: "Invalid request" });
      }
      await sdk.update(
        {
          viewed: 1
        },
        id
      );

      return res.status(200).json({
        error: false,
        message: "Notification opened successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/cedric/notifications", [...middlewares, TokenMiddleware()], async function (req, res) {
    let client = req.app.get("subscriber");
    let user_id = req.user_id;
    const startTime = new Date().getTime();
    let messageTriggered = false;
    let timer;

    //We assume user going send room
    try {
      timer = setTimeout(() => {
        if (!messageTriggered) {
          const endTime = new Date().getTime();
          clearTimeout(timer);
          return res.status(408).json({ error: true, message: "TIMEOUT", diff: (endTime - startTime) / 1000 });
        }
      }, 60000);

      const name = req.projectId + "_user_" + user_id;
      console.log("name", name);

      await client.subscribe(name, async (data) => {
        const endTime = new Date().getTime();
        await client.unsubscribe(name);
        clearTimeout(timer);
        messageTriggered = true;
        const payloadReceived = JSON.parse(data);
        // return timer && res.status(200).json({ count: payloadReceived.count, diff: (endTime - startTime) / 1000 });
        if (timer) return res.status(200).json({ count: payloadReceived.count, diff: (endTime - startTime) / 1000 });
      });

      return;
    } catch (error) {
      console.log("Poll Error", error);
      clearTimeout(timer);
      const endTime = new Date().getTime();
      res.status(500).json({ error: true, message: "Something went wrong", diff: (endTime - startTime) / 1000 });
      return;
    }
  });
  app.get("/v3/api/custom/cedric/notifications-count", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("activity_log");
      const count = await getCount(req);

      return res.status(200).json({
        error: false,
        count
      });
    } catch (error) {
      console.log("Poll Error", error);
      clearTimeout(timer);
      const endTime = new Date().getTime();
      res.status(500).json({ error: true, message: "Something went wrong", diff: (endTime - startTime) / 1000 });
      return;
    }
  });

  app.get("/v3/api/custom/cedric/fetch-test", [...middlewares], async function (req, res) {
    req.user_id = 394;
    let client = req.app.get("subscriber");
    let user_id = req.user_id;
    const startTime = new Date().getTime();
    let messageTriggered = false;
    let timer;

    //We assume user going send room
    try {
      timer = setTimeout(() => {
        if (!messageTriggered) {
          const endTime = new Date().getTime();
          clearTimeout(timer);
          res.status(408).json({ error: true, message: "TIMEOUT", diff: (endTime - startTime) / 1000 });
          return;
        }
      }, 60000);

      const name = req.projectId + "_user_" + user_id;
      console.log("name", name);

      await client.subscribe(name, async (data) => {
        const endTime = new Date().getTime();
        await client.unsubscribe(name);
        clearTimeout(timer);
        messageTriggered = true;
        const payloadReceived = JSON.parse(data);
        return timer && res.status(200).json({ count: payloadReceived.count, diff: (endTime - startTime) / 1000 });
      });

      return;
    } catch (error) {
      console.log("Poll Error", error);
      clearTimeout(timer);
      const endTime = new Date().getTime();
      res.status(500).json({ error: true, message: "Something went wrong", diff: (endTime - startTime) / 1000 });
      return;
    }
  });
  app.post("/v3/api/custom/cedric/customer/hire", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      });

      if (user.length <= 0) {
        return res.status(400).json({ error: true, message: "Invalid user" });
      }

      const { professional_id, description, budget, timeframe, location } = req.body;
      if (!professional_id || !description || !location) {
        return res.status(400).json({ error: true, message: "Invalid request" });
      }

      sdk.setTable("job");
      await sdk.insert(
        filterEmptyFields({
          customer_id: req.user_id,
          professional_id,
          description,
          budget,
          status: 0,
          location,
          timeframe,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        })
      );

      sdk.setTable("activity_log");
      await sdk.insert({
        user_id: req.user_id,
        other_user_id: professional_id,
        event: "Created a Job",
        event_fr: "A créé un emploi",
        data: JSON.stringify({}),
        for_user_id: professional_id,
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      try {
        await emitEvent(app, req.user_id, sdk, (other = false), req.projectId, professional_id);
      } catch (err) {
        console.log("err >>", err);
      }
      sdk.setTable("user");
      const customer_user = await sdk.get({
        id: req.user_id
      });
      const professional_user = await sdk.get({
        id: professional_id
      });
      console.log("professional_user >> ", professional_user);
      const result = await sendNotification(
        JSON.parse(professional_user[0].subscription),
        "Ohie",
        professional_user[0].lng == "en"
          ? `${customer_user[0].first_name} ${customer_user[0].last_name} wants to hire you`
          : `${customer_user[0].first_name} ${customer_user[0].last_name} veut vous embaucher`
      );
      return res.status(200).json({
        error: false,
        message: "hire request sent"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  //fetch all jobs
  // filter jobs
  app.get("/v3/api/custom/cedric/jobs", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      const grouping_sql = `
      cedric_professional_profile.user_id,
      cedric_user.id, 
      cedric_user.first_name, 
      cedric_user.last_name, 
      cedric_user.phone, 
      cedric_user.email, 
      cedric_user.photo, 
      cedric_professional_profile.id,
      cedric_professional_profile.user_id,
      cedric_professional_profile.bio,
      cedric_professional_profile.profession,
      cedric_professional_profile.category,
      cedric_professional_profile.address,
      cedric_professional_profile.subcategory,
      cedric_professional_profile.experience,
      cedric_professional_profile.tiktok_url,
      cedric_professional_profile.facebook_url,
      cedric_professional_profile.instagram_url,
      cedric_professional_profile.cover_photo,
      cedric_professional_profile.cover_top,
      cedric_professional_profile.cover_left,
      cedric_professional_profile.details,
      cedric_professional_profile.country,
      cedric_professional_profile.island,
      cedric_professional_profile.city,
      cedric_professional_profile.enterprise,
      cedric_professional_profile.visible,
      cedric_professional_profile.hidden,
      cedric_professional_profile.joined,
      cedric_professional_profile.create_at
      `;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      });
      const { status } = req.query;
      let find = [0, 1, 2, 3, 4, 5];
      if (status) {
        switch (status) {
          case "current":
            find = [0, 1];
            break;
          case "completed":
            find = [2, 3, 4, 5];
            break;
        }
      }
      let sql = `SELECT cedric_job.*, cedric_job.update_at AS completed_date FROM cedric_job WHERE cedric_job.customer_id = ${
        req.user_id
      } AND cedric_job.status IN (${find.join(",")}) ORDER BY cedric_job.update_at DESC`;

      let sql1 = `SELECT cedric_job.*, cedric_job.update_at AS completed_date FROM cedric_job WHERE cedric_job.professional_id = ${
        req.user_id
      } AND cedric_job.status IN (${find.join(",")}) ORDER BY cedric_job.update_at DESC`;

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.query.sortId ?? "id");
      paginationService.setSortDirection(req.query.direction ?? "DESC");
      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      const mainQL = sql.split("FROM")[1];
      const mainQL1 = sql1.split("FROM")[1];
      sql += ` LIMIT ${offset} , ${limit}`;
      sql1 += ` LIMIT ${offset} , ${limit}`;

      const endQL = mainQL.split("GROUP BY")[0];
      const endQL1 = mainQL1.split("GROUP BY")[0];
      let sqlCount = `SELECT COUNT(*) as num FROM` + endQL;
      let sqlCount1 = `SELECT COUNT(*) as num FROM` + endQL1;
      const count = await sdk.rawQuery(sqlCount);
      const count1 = await sdk.rawQuery(sqlCount1);
      paginationService.setCount(count[0].num + count1[0].num);
      console.log("sql", sql);

      const result1 = await sdk.rawQuery(sql);
      const result2 = await sdk.rawQuery(sql1);
      const result = [...result1, ...result2];

      for (let i = 0; i < result.length; i++) {
        const id = result[i].professional_id;
        const customer_id = result[i].customer_id;
        const professional_sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.photo, cedric_professional_profile.island, 
        COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars 
        FROM cedric_user 
        LEFT JOIN cedric_professional_profile ON cedric_user.id = cedric_professional_profile.user_id 
       LEFT JOIN cedric_rating ON cedric_rating.professional_id = cedric_user.id WHERE cedric_user.id = ${id}
       GROUP BY ${grouping_sql}
       `;

        const customer_sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.photo FROM cedric_user WHERE cedric_user.id = ${customer_id}`;
        sdk.setTable("rating");
        const reviewed = await sdk.get({
          customer_id: customer_id,
          job_id: result[i].id
        });
        if (reviewed.length > 0) {
          result[i].reviewed = true;
          result[i].job_review = reviewed[0];
        } else {
          result[i].reviewed = false;
          result[i].job_review = {};
        }
        const user = await sdk.rawQuery(professional_sql);
        const customer = await sdk.rawQuery(customer_sql);
        console.log(user, "an");
        if (user.length > 0) {
          sdk.setTable("islands");
          const islands = await sdk.get({
            id: user[0].island
          });

          result[i].professional_details = {
            first_name: user[0].first_name,
            last_name: user[0].last_name,
            photo: user[0].photo,
            island: islands[0]?.name ?? "",
            total_ratings: user[0].total_ratings,
            total_stars: user[0].total_stars
          };
        }

        if (customer.length > 0) {
          result[i].customer_details = {
            first_name: customer[0].first_name,
            last_name: customer[0].last_name,
            photo: customer[0].photo
          };
        }
      }

      return res.status(200).json({
        error: false,
        list: result,
        total: count[0].num + count1[0].num
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // get all activities
  app.get("/v3/api/custom/cedric/customer/logs", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      // const user = await sdk.get({
      //     user_id: req.user_id
      // })
      // let sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.id AS user_id, cedric_activity_log.* FROM cedric_activity_log JOIN cedric_user ON cedric_user.id=cedric_activity_log.user_id WHERE cedric_activity_log.user_id = ${req.user_id}`
      // cedric_activity_log.user_id = ${req.user_id} OR
      let sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.photo, cedric_activity_log.* FROM cedric_activity_log JOIN cedric_user ON cedric_user.id=cedric_activity_log.user_id WHERE cedric_activity_log.other_user_id = ${req.user_id} 
      GROUP BY 
      cedric_activity_log.user_id,
      cedric_activity_log.id,
      cedric_activity_log.other_user_id,
      cedric_activity_log.create_at,
      cedric_activity_log.update_at,
      cedric_activity_log.event,
      cedric_activity_log.viewed,
      cedric_activity_log.data,
      cedric_activity_log.event_fr,
      cedric_activity_log.for_user_id
      ORDER BY cedric_activity_log.create_at DESC`;

      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.query.sortId ?? "id");
      paginationService.setSortDirection(req.query.direction ?? "DESC");
      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      const mainQL = sql.split("FROM")[1];
      sql += ` LIMIT ${offset} , ${limit}`;

      let sqlCount = `SELECT COUNT(*) as num FROM` + mainQL;
      const count = await sdk.rawQuery(sqlCount);
      paginationService.setCount(count[0].num);
      console.log("sql", sql);

      const result = await sdk.rawQuery(sql);

      for (let i = 0; i < result.length; i++) {
        const id = result[i].user_id;
        let professional_sql = "";
        if ((result[i].role = "professional")) {
          sql = `SELECT cedric_professional_profile.island FROM cedric_professional_profile WHERE cedric_professional_profile.user_id = ${id}`;
          const user = await sdk.rawQuery(professional_sql);
          console.log(user, "an");
          if (user.length > 0) {
            sdk.setTable("islands");
            const islands = await sdk.get({
              id: user[0].island
            });
            result[i].island = islands[0].name;
          } else {
            result[i].island = "";
          }
        }
      }

      return res.status(200).json({
        error: false,
        list: result,
        total: count[0].num
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/cedric/customer/reviews/:id", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      // req.user_id = 30
      sdk.setProjectId(req.projectId);

      let sql = `SELECT cedric_rating.*, cedric_user.first_name, cedric_user.last_name, cedric_user.photo AS user_photo, cedric_job.id AS job_id, cedric_professional_profile.island, cedric_job.description, cedric_job.budget, cedric_job.timeframe  FROM cedric_rating JOIN cedric_user ON cedric_user.id=cedric_rating.customer_id JOIN cedric_professional_profile ON cedric_professional_profile.user_id=cedric_rating.professional_id  JOIN cedric_job ON cedric_rating.job_id=cedric_job.id WHERE cedric_rating.professional_id = ${req.params.id}
      GROUP BY
      cedric_rating.id,
      cedric_rating.stars,
      cedric_rating.comments,
      cedric_rating.create_at,
      cedric_rating.update_at,
      cedric_rating.job_id,
      cedric_rating.customer_id,
      cedric_rating.professional_id,
      cedric_rating.answer,
      cedric_rating.status,
      cedric_user.first_name,
      cedric_professional_profile.island
      `;
      let paginationService = new PaginationService(req.query.page ?? 1, req.query.limit ?? 10);
      paginationService.setSortField(req.query.sortId ?? "id");
      paginationService.setSortDirection(req.query.direction ?? "DESC");
      const page = paginationService.getPage(),
        limit = paginationService.getLimit();

      console.log("sql", sql);

      const offset = (page - 1) * limit;

      const mainQL = sql.split("FROM")[1];
      let sqlCount = `SELECT COUNT(*) as num FROM` + mainQL;
      sql += ` LIMIT ${offset} , ${limit}`;
      const count = await sdk.rawQuery(sqlCount);
      console.log("sql", count);
      paginationService.setCount(count[0]?.num);
      console.log("sql", sql);
      sdk.setTable("islands");
      const result = await sdk.rawQuery(sql);
      if (result.length > 0) {
        const island_name = await sdk.get({
          id: result[0].island
        });
        result.forEach((data) => {
          data.island = island_name[0]?.name;
        });
      }
      return res.status(200).json({
        error: false,
        list: result,
        total: count[0]?.num
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/cedric/customer/reviews/:id", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      // req.user_id = 30
      sdk.setProjectId(req.projectId);
      sdk.setTable("rating");
      const { stars, review } = req.body;

      if (!stars) {
        return res.status(403).json({ error: true, message: "Missing stars or review" });
      }
      sdk.setTable("job");
      const job = await sdk.get({
        id: req.params.id
      });

      if (job.length <= 0) {
        return res.status(403).json({ error: true, message: "Invalid job" });
      }

      if (job[0].status != 2) {
        return res.status(403).json({ error: true, message: "Job not completed" });
      }

      sdk.setTable("rating");
      const previous = await sdk.get({
        job_id: req.params.id,
        customer_id: req.user_id
      });

      if (previous.length > 0) {
        return res.status(403).json({ error: true, message: "Already reviewed" });
      }

      const rating = await sdk.insert({
        customer_id: req.user_id,
        professional_id: job[0].professional_id,
        stars,
        job_id: req.params.id,
        comments: review,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return res.status(200).json({
        error: false,
        message: "Review added Successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  //mark all messages as read
  app.get("/v3/api/custom/cedric/customer/mark-read/:room_id", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("room");
      const room = await sdk.get({
        id: req.params.room_id,
        user_id: req.user_id
      });
      const room2 = await sdk.get({
        id: req.params.room_id,
        other_user_id: req.user_id
      });
      if (room.length <= 0 && room2.length <= 0) {
        return res.status(403).json({ error: true, message: "Invalid room" });
      }
      sdk.setTable("chat");
      const { room_id } = req.params;
      const chat = await sdk.get({
        room_id
      });
      if (chat.length <= 0) {
        return res.status(403).json({ error: true, message: "Invalid room" });
      }
      await sdk.updateWhere(
        {
          unread: 0
        },
        {
          room_id
        }
      );
      return res.status(200).json({
        error: false,
        message: "Marked as read"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  //get all favorites
  app.get("/v3/api/custom/cedric/customer/favorites", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      });
      if (user.length <= 0) {
        return res.status(404).json({
          error: true,
          message: "User profile not found"
        });
      }
      const favorites = user[0].favorites ? JSON.parse(user[0].favorites) : [];
      if (favorites.length <= 0) {
        return res.status(200).json({
          error: false,
          list: [],
          total: 0
        });
      } else {
        let sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars, cedric_islands.name AS island_name  FROM cedric_user JOIN cedric_professional_profile ON cedric_user.id=cedric_professional_profile.user_id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_professional_profile.user_id
            WHERE cedric_user.id IN (${favorites})  
            GROUP BY 
            ${grouping_sql}
            ORDER BY cedric_professional_profile.update_at DESC`;
        const result = await sdk.rawQuery(sql);
        return res.status(200).json({
          error: false,
          list: result,
          total: result.length
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  //add or remove favorites
  app.post("/v3/api/custom/cedric/customer/favorites", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      });
      if (!user) {
        return res.status(400).json({ error: true, message: "Invalid user" });
      }

      const favorites = user[0].favorites ? JSON.parse(user[0].favorites) : [];
      let removed = false;
      const id = req.body.professional_id;
      if (favorites.length > 0) {
        if (favorites.includes(Number(id))) {
          favorites.splice(favorites.indexOf(Number(id)), 1);
          const profile = await sdk.update(
            {
              favorites: JSON.stringify(favorites)
            },
            user[0].id
          );
          return res.status(200).json({ error: false, message: "removed from favorites" });
        } else {
          favorites.push(id);
          const profile = await sdk.update(
            {
              favorites: JSON.stringify(favorites)
            },
            user[0].id
          );
        }
      } else {
        favorites.push(id);
        sdk.setTable("customer_profile");
        const profile = await sdk.update(
          {
            favorites: JSON.stringify(favorites)
          },
          user[0].id
        );
      }

      return res.status(200).json({
        error: false,
        message: "Added to favorites"
      });
    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  //get chats

  app.post("/v3/api/custom/cedric/realtime/chat", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("chat");

      if (!req.body.room_id) {
        return res.status(403).json({
          error: true,
          message: "Room is required",
          validation: [{ field: "room_id", message: "Room is required" }]
        });
      }

      //req.user_id = 378

      // if (!req.body.date) {
      //   return res.status(403).json({
      //     error: true,
      //     message: "Date is required",
      //     validation: [{ field: "date", message: "Date is required" }]
      //   });
      // }

      // let result = await sdk.get({
      //   room_id: req.body.room_id,
      // });
      sdk.setTable("room");
      const room1Sql = `SELECT * FROM cedric_room WHERE cedric_room.id = "${req.body.room_id}" AND cedric_room.delete_for != "${req.user_id}"`;
      let room = await sdk.rawQuery(room1Sql);
      if (!room.length) {
        throw new Error(`No room found`);
      }
      sdk.setTable("chat");
      const chatSql = `SELECT * FROM cedric_chat WHERE cedric_chat.room_id = "${req.body.room_id}" AND cedric_chat.delete_for != "${req.user_id}"`;
      // let result = await sdk.get({
      //   room_id: req.body.room_id,
      // });
      let result = await sdk.rawQuery(chatSql);
      console.log(room);
      let temp = [];
      for (let i = result.length - 1; i > -1; i--) {
        temp.push(result[i]);
      }
      result = temp;

      if (!result.length) {
        sdk.setTable("chat");

        await sdk.insert({
          room_id: req.body.room_id,
          unread: 0,
          chat: "[]",
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        return res.status(200).json({
          error: false,
          model: []
        });
      }
      sdk.setTable("activity_log");
      let sql = `SELECT * FROM cedric_activity_log WHERE (cedric_activity_log.user_id = ${req.user_id} OR cedric_activity_log.other_user_id = ${req.user_id}) AND (cedric_activity_log.user_id = ${room[0].other_user_id} OR cedric_activity_log.other_user_id = ${room[0].other_user_id})`;
      // let inchat_logs = await sdk.get({
      //   user_id: req.user_id,
      //   other_user_id: room[0].other_user_id,
      //   user_id: room[0].user_id
      // });
      let inchat_logs = await sdk.rawQuery(sql);
      console.log("inchat_logs >> ", inchat_logs);
      sdk.setTable("job");
      const job1 = await sdk.get({
        professional_id: req.user_id,
        customer_id: room[0].other_user_id
      });
      const job2 = await sdk.get({
        customer_id: req.user_id,
        professional_id: room[0].other_user_id
      });

      let status = "inactive";

      let jobs = [...job1, ...job2];
      if (jobs.length > 0) {
        jobs.forEach((job) => {
          if (job.status === 3 || job.status === 1 || job.status === 0) {
            status = "active";
          }
        });
      }

      sdk.setTable("user");
      const other_user = await sdk.get({
        id: room[0].other_user_id === req.user_id ? room[0].user_id : room[0].other_user_id
      });
      const current_user = await sdk.get({
        id: req.user_id
      });
      if (other_user.length > 0) delete other_user[0].password;
      delete current_user[0].password;
      temp = [];
      for (let i = inchat_logs.length - 1; i > -1; i--) {
        temp.push(inchat_logs[i]);
      }
      inchat_logs = temp;
      result = [...result, ...inchat_logs];
      // 707.704.2333
      const sortedAsc = result.sort((objA, objB) => Number(new Date(objB.update_at)) - Number(new Date(objA.update_at)));

      if (current_user[0].role === "customer") {
        other_user[0].hire_status = status;
      }

      return res.status(200).json({
        error: false,
        model: sortedAsc,
        room: room[0],
        status,
        current_user: current_user[0],
        other_user: other_user[0]
      });
    } catch (err) {
      res.status(403);
      res.json({
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/cedric/realtime-room/delete", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("chat");

      if (!req.body.room_id) {
        return res.status(403).json({
          error: true,
          message: "Room is required",
          validation: [{ field: "room_id", message: "Room is required" }]
        });
      }

      //req.user_id = 378

      sdk.setTable("room");
      const room1 = await sdk.get({
        id: req.body.room_id,
        user_id: req.user_id
      });
      const room2 = await sdk.get({
        id: req.body.room_id,
        other_user_id: req.user_id
      });

      if (room1.length <= 0 && room2.length <= 0) {
        return res.status(403).json({
          error: true,
          message: "No room found for user by this id"
        });
      }
      let deleteAll = false;
      if (room1.length > 0) {
        if (room1[0].delete_for !== 0 && room1[0].delete_for !== req.user_id) {
          deleteAll = true;
        }
      } else if (room2.length > 0) {
        if (room2[0].delete_for !== 0 && room2[0].delete_for !== req.user_id) {
          deleteAll = true;
        }
      }
      if (deleteAll) {
        // delete room and cascade chats
        await sdk.delete({}, req.body.room_id);
      } else {
        await sdk.update(
          {
            delete_for: req.user_id
          },
          req.body.room_id
        );
      }

      return res.status(200).json({
        error: false,
        message: "Room and chats deleted successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/cedric/realtime/room", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("room");

      if (!req.query.room_id) {
        return res.status(403).json({
          error: true,
          message: "Room is required",
          validation: [{ field: "room_id", message: "Room is required" }]
        });
      }

      // const result = await sdk.get({
      //   id: req.query.room_id
      // });
      const room1Sql = `SELECT * FROM cedric_room WHERE cedric_room.id = "${req.query.room_id}" AND cedric_room.delete_for != "${req.user_id}"`;
      const result = await sdk.rawQuery(room1Sql);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (result.length != 1) {
        return res.status(403).json({
          error: true,
          message: "Room does not exist"
        });
      }

      if (!(result[0].user_id == req.user_id || result[0].other_user_id == req.user_id)) {
        return res.status(403).json({
          error: true,
          message: "Invalid Room"
        });
      }
      sdk.setTable("activity_log");
      const inchat_logs = sdk.get({
        user_id: req.user_id,
        other_id: result[0].other_user_id
      });
      result[0];

      return res.status(200).json({
        error: false,
        model: result[0]
      });
    } catch (err) {
      res.status(403);
      res.json({
        message: err.message
      });
    }
  });

  // get a single professional by id
  app.get("/v3/api/custom/cedric/customer/professionals/:id", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const grouping_sql = `
        cedric_professional_profile.user_id,
        cedric_user.id, 
        cedric_user.first_name, 
        cedric_user.last_name, 
        cedric_user.phone, 
        cedric_user.email, 
        cedric_user.photo, 
        cedric_islands.name,
        cedric_professional_profile.id,
        cedric_professional_profile.user_id,
        cedric_professional_profile.bio,
        cedric_professional_profile.profession,
        cedric_professional_profile.category,
        cedric_professional_profile.address,
        cedric_professional_profile.subcategory,
        cedric_professional_profile.experience,
        cedric_professional_profile.tiktok_url,
        cedric_professional_profile.facebook_url,
        cedric_professional_profile.instagram_url,
        cedric_professional_profile.cover_photo,
        cedric_professional_profile.cover_top,
        cedric_professional_profile.cover_left,
        cedric_professional_profile.details,
        cedric_professional_profile.country,
        cedric_professional_profile.island,
        cedric_professional_profile.city,
        cedric_professional_profile.enterprise,
        cedric_professional_profile.visible,
        cedric_professional_profile.hidden,
        cedric_professional_profile.joined,
        cedric_professional_profile.create_at
        `;
      let sql = `SELECT cedric_user.first_name, cedric_user.last_name, cedric_user.phone, cedric_user.email, cedric_user.photo, cedric_professional_profile.*, cedric_islands.name AS island_name, COUNT(cedric_rating.stars) AS total_ratings, SUM(cedric_rating.stars) as total_stars FROM cedric_user JOIN cedric_professional_profile 
          ON cedric_user.id=cedric_professional_profile.user_id LEFT JOIN cedric_rating ON cedric_rating.professional_id=cedric_user.id JOIN cedric_islands ON cedric_islands.id=cedric_professional_profile.island where cedric_user.id = "${req.params.id}" AND cedric_user.complete_p="1"
          GROUP BY ${grouping_sql}
          `;
      sdk.setTable("job");
      const jobs = await sdk.get({
        professional_id: req.params.id
      });

      const neww = jobs ? jobs.filter((job) => job.status === 0) : [];
      const accepted = jobs ? jobs.filter((job) => job.status === 1) : [];
      const active = jobs ? jobs.filter((job) => job.status === 1 || job.status === 3) : [];
      const cancelled = jobs ? jobs.filter((job) => job.status === 5) : [];
      const completed = jobs ? jobs.filter((job) => job.status === 2) : [];
      const pending = jobs ? jobs.filter((job) => job.status === 3) : [];
      const declined = jobs ? jobs.filter((job) => job.status === 4) : [];
      const total = jobs ? jobs.length : 0;

      let media = [];

      sdk.setTable("photo");
      const photos = await sdk.get({
        user_id: req.params.id
      });
      let workSamples = [];
      sdk.setTable("professional_profile");
      const professionalData = await sdk.get({
        user_id: req.params.id
      });
      let details = {};
      if (professionalData.length > 0) {
        details = professionalData[0].details ? JSON.parse(professionalData[0].details) : null;
        if (details) {
          if (details.workSamples) {
            workSamples = details.workSamples;
          }
        }
      }

      media = photos ? photos : [];

      const professional = await sdk.rawQuery(sql);
      if (professional.length > 0) {
        professional[0].active_orders = active.length;
        professional[0].accepted_orders = accepted.length;
        professional[0].cancelled_orders = cancelled.length;
        professional[0].pending_orders = pending.length;
        professional[0].declined_orders = declined.length;
        professional[0].completed_orders = completed.length;
        professional[0].total_orders = total;
        professional[0].media = media;
        delete professional[0].details;
      } else {
        return res.status(404).json({
          error: true,
          message: "Not Found"
        });
      }
      sdk.setTable("job");
      const job = await sdk.get({
        professional_id: req.params.id,
        customer_id: req.user_id
      });
      professional[0].jobs = job;
      jobs.forEach(function (job) {
        if (job.status === 3 || job.status === 1 || job.status === 0) {
          professional[0].hire_status = "active";
        }
      });
      professional[0].workSamples = workSamples;
      delete details?.workSamples;
      professional[0].details = details;
      // const saved = await saveCount(sdk,req.user_id);
      return res.status(200).json({
        error: false,
        list: professional
      });
    } catch (err) {
      console.log(err, "err");
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // send api
  app.post("/v3/api/custom/cedric/realtime/send", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      let client = app.get("publisher");
      let redisCach = app.get("redisClient");

      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("chat");
      let id = 0;
      let room_id = 0;

      if (!req.body.room_id) {
        return res.status(403).json({
          error: true,
          message: "Room is required",
          validation: [{ field: "room_id", message: "Room is required" }]
        });
      }

      if (!req.body.user_id) {
        return res.status(403).json({
          error: true,
          message: "User is required",
          validation: [{ field: "user_id", message: "User is required" }]
        });
      }

      if (!req.body.message && !req.body.image) {
        return res.status(403).json({
          error: true,
          message: "Message is required",
          validation: [
            { field: "message", message: "Message is required" },
            { field: "image", message: "image is required" }
          ]
        });
      }

      sdk.setTable("room");
      let roomResult = await redisCach?.get(`${req.projectId}_room_${room_id}`);

      if (!roomResult) {
        roomResult = await sdk.get({
          id: req.body.room_id
        });
        await redisCach?.set(`${req.projectId}_${room_id}`, JSON.stringify(roomResult));
      } else roomResult = JSON.parse(roomResult);

      let check = await sdk.get({
        id: req.body.room_id,
        delete_for: req.user_id == roomResult[0].user_id ? roomResult[0].other_user_id : roomResult[0].user_id
      });
      if (check.length > 0) {
        await sdk.update(
          {
            delete_for: 0,
            update_at: sqlDateTimeFormat(new Date())
          },
          req.body.room_id
        );
      }

      await client.publish(
        req.projectId + "_chat_" + (req.user_id == roomResult[0].user_id ? roomResult[0].other_user_id : roomResult[0].user_id),
        JSON.stringify({ message: req.body.message ? req.body.message : req.body.image, user_id: req.body.user_id })
      );

      sdk.setTable("chat");
      const new_chat = {
        message: req.body.message,
        image: req.body.image,
        user_id: req.body.user_id,
        other_user_id: req.body.other_user_id,
        is_image: req.body.is_image ? true : false,
        timestamp: new Date().getTime(),
        thread: []
      };
      const chatResult = await sdk.insert({
        room_id: req.body.room_id,
        unread: 1,
        chat: JSON.stringify(new_chat),
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      let payload = {
        unread: 1,
        update_at: sqlDateTimeFormat(new Date())
      };

      if (req.body.user_id == roomResult[0].user_id) {
        payload.user_update_at = sqlDateTimeFormat(new Date());
      } else {
        payload.other_user_update_at = sqlDateTimeFormat(new Date());
      }
      sdk.setTable("room");
      await sdk.update(payload, room_id);

      //await client.publish(req.projectId + '_' + room_id, JSON.stringify({ message: req.body.message, user_id: req.body.user_id }));

      sdk.setTable("user");
      const other_user = await sdk.get({
        id: req.user_id == roomResult[0].user_id ? roomResult[0].other_user_id : roomResult[0].user_id
      });
      const user = await sdk.get({
        id: req.user_id
      });
      const result = await sendNotification(
        JSON.parse(other_user[0].subscription),
        "Ohie",
        `${
          other_user[0].lng == "fr"
            ? `${user[0].first_name} ${user[0].last_name} vous a envoyé un message`
            : `${user[0].first_name} ${user[0].last_name} has sent you a message`
        }`
      );
      return res.status(200).json({
        error: false,
        message: "Updated"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/cedric/count/:id", [...middlewares, TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const saved = await saveCount(sdk, req.params.id);
      return res.status(200).json({
        error: false,
        message: "count increased"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // increase count

  // move job status
  app.post("/v3/api/custom/cedric/job/update", [...middlewares, TokenMiddleware()], async function (req, res) {
    let sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);

    try {
      const { job_id, status, reason } = req.body;
      if (!status || !job_id) {
        return res.status(400).json({
          error: true,
          message: "missing status or job_id"
        });
      }
      sdk.setTable("user");
      const user = await sdk.get({
        id: req.user_id
      });
      let role = "";
      const customer = [2, 5];
      const professional = [1, 2, 4, 5];
      let other = "";
      let current = "";
      let allow = false;
      let accepted = false;
      let cancelled = false;
      let declined = false;
      let completed = false;
      if (user.length > 0) {
        role = user[0].role;
      } else {
        return res.status(400).json({ error: true, message: "user not found" });
      }

      let event = "";
      let event_fr = "";
      switch (status) {
        case 1:
          event = "Accepted a job";
          event_fr = "A accepté une embauche";
          accepted = true;
          break;
        case 2:
          event = "Marked job as Completed";
          event_fr = "A marqué l'emploi comme terminé";
          // event_fr = "A fini un travail"
          completed = true;
          break;
        case 4:
          event = "Declined a job";
          event_fr = "J'ai refusé un emploi";
          declined = true;
          if (!reason) {
            return res.status(400).json({
              error: true,
              message: "Include a reason for cancelling the job"
            });
          }
          break;
        case 5:
          event = "Cancelled a job";
          event_fr = "A annulé une embauche";
          cancelled = true;
          if (!reason) {
            return res.status(400).json({
              error: true,
              message: "Include a reason for cancelling the job"
            });
          }
          break;
        default:
          event = "";
          event_fr = "";
      }

      if (role === "customer") {
        if (customer.includes(status) || customer.includes(Number(status)) || customer.includes("" + status)) {
          allow = true;
          other = "professional_id";
          current = "customer";
        } else {
          return res.status(404).json({
            error: true,
            message: "customer is not allowed to update job to this status"
          });
        }
      }
      if (role === "professional") {
        if (professional.includes(status) || professional.includes(Number(status)) || professional.includes("" + status)) {
          allow = true;
          other = "customer_id";
          current = "professional";
        } else {
          return res.status(404).json({
            error: true,
            message: "professional is not allowed to update job to this status"
          });
        }
      }

      sdk.setTable("job");
      const job = await sdk.get({
        id: job_id
      });

      if (job.length <= 0) {
        return res.status(404).json({
          error: true,
          message: "Job not found"
        });
      }
      let data = JSON.stringify({});

      if (allow) {
        sdk.setTable("job");
        if (accepted) {
          await sdk.update(
            {
              status,
              professional_id: req.user_id
            },
            job_id
          );
        } else if (cancelled || declined) {
          await sdk.update(
            {
              status,
              reason
            },
            job_id
          );
        } else if (completed) {
          if (job[0].status !== 2 && job[0].status !== 3) {
            event = current + " marked job as completed awaiting " + other.split("_")[0];
            event_fr = `A fini un travail qui doit etre noté`;
            data = JSON.stringify({ job_id, next: other.split("_")[0], current });
            await sdk.update(
              {
                status: 3
              },
              job_id
            );
          } else if (job[0].status === 3) {
            sdk.setTable("activity_log");
            const first = await sdk.get({
              user_id: req.user_id,
              other_user_id: job[0][other]
            });
            const second = await sdk.get({
              other_user_id: req.user_id,
              user_id: job[0][other]
            });
            const activity_log = [...first, ...second];
            let past = {};
            if (activity_log.length > 0) {
              activity_log.forEach((act) => {
                if (act.data && JSON.parse(act.data)?.job_id === job_id) {
                  past = act;
                }
              });
            }
            const formerData = past?.data ? JSON.parse(past.data) : {};
            if (formerData?.current && formerData?.current === current) {
              return res.json({
                error: false,
                message: "You've already marked as completed awaiting " + other.split("_")[0]
              });
            } else {
              sdk.setTable("job");
              await sdk.update(
                {
                  status
                },
                job_id
              );
            }
          }
        } else {
          sdk.setTable("job");
          await sdk.update(
            {
              status
            },
            job_id
          );
        }
        sdk.setTable("user");
        const other_user = await sdk.get({
          id: job[0][other]
        });
        console.log("other_user >> ", other_user);
        const result = await sendNotification(
          JSON.parse(other_user[0].subscription),
          "Ohie",
          `${
            other_user[0].lng == "fr"
              ? `${user[0].first_name} ${user[0].last_name} ${event_fr.toLowerCase()}`
              : `${user[0].first_name} ${user[0].last_name} ${event.toLowerCase()}`
          }`
        );

        sdk.setTable("activity_log");
        await sdk.insert({
          user_id: req.user_id,
          other_user_id: job[0][other],
          event,
          event_fr,
          for_user_id: job[0][other],
          data,
          reason,
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
        const oth = role === "customer" ? false : true;
        try {
          await emitEvent(req.app, job[0][other], sdk, oth, req.projectId, job[0][other]);
        } catch (e) {
          console.log(e);
        }
      }

      return res.status(200).json({
        error: false,
        message: "Job status updated"
      });
    } catch (e) {
      return res.status(400).json({
        error: true,
        message: e.message
      });
    }
  });

  app.post("/v3/api/custom/cedric/notificaion/send", [...middlewares, TokenMiddleware()], async function (req, res) {
    let sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);

    try {
      sdk.setTable("user");
      const user = await sdk.get({
        id: req.body.id
      });
      if (user[0].subscription) {
        console.log("subscription", JSON.parse(user[0].subscription));
        const result = await sendNotification(JSON.parse(user[0].subscription), "Ohie", req.body.message ? req.body.message : "message");
        return res.status(200).json({
          error: false,
          message: "Notifications send",
          result: result
        });
      } else {
        return res.status(200).json({
          error: false,
          message: "Message is empty."
        });
      }
    } catch (e) {
      return res.status(400).json({
        error: true,
        message: e.message
      });
    }
  });

  app.post("/v3/api/custom/cedric/notificaion/subcription", [...middlewares, TokenMiddleware()], async function (req, res) {
    let sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);

    try {
      console.log("subcription body", req.body);
      sdk.setTable("user");
      const user_update = await sdk.update(
        {
          subscription: JSON.stringify(req.body)
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Subscription saved!`"
      });
    } catch (e) {
      return res.status(400).json({
        error: true,
        message: e.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
