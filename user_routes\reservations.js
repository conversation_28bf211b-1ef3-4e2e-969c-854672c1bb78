const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const stripe = new StripeService();
const { reservation_hours_left, log_reservation,formatTimeForMySQL,generateAvailabilityForNextMonth, mergeAvailabilities,mapTimeslotsWithDuration, generateDefaultAvailability } = require("../utils/util.js");
const { BOOKING_STATUSES, RESERVATION_TYPES } = require("../utils/constants.js");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const _base = "/v3/api/custom/courtmatchup/user";

module.exports = function (app) {
  /**
 * Parse a time string "HH:mm" or "HH:mm:ss" into seconds since midnight.
 * @param {string} timeStr - e.g. "17:00", "07:30:45"
 * @returns {number} seconds since 00:00 (0 through 86399)
 * @throws {Error} if format is invalid or out-of-range
 */
function parseTimeToSeconds(timeStr) {
  if (typeof timeStr !== 'string') {
    throw new Error(`Invalid time format: expected string but got ${typeof timeStr}`);
  }
  // Trim whitespace
  const s = timeStr.trim();
  // Regex: hours: 0-23, minutes: 00-59, optional :seconds 00-59
  const m = /^(\d{1,2}):([0-5]\d)(?::([0-5]\d))?$/.exec(s);
  if (!m) {
    throw new Error(`Invalid time format: "${timeStr}". Expected "HH:mm" or "HH:mm:ss"`);
  }
  let hours = Number(m[1]);
  const minutes = Number(m[2]);
  const seconds = m[3] !== undefined ? Number(m[3]) : 0;
  if (hours < 0 || hours > 23) {
    throw new Error(`Hour out of range in time: "${timeStr}"`);
  }
  // Compute total seconds
  return hours * 3600 + minutes * 60 + seconds;
}

  // Get reservations
  app.get("/v3/api/custom/courtmatchup/user/reservations", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {custom_request} = req.query;

      const today = sqlDateFormat(new Date());
      
      // Fetch reservations for the user
      sdk.setTable("reservation");
      // First get reservations
      const sql = `SELECT 
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              cs.name AS sport_name,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.receipt_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.subtype as sub_type,
              b.type as type,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type,
              'reservation' as entry_type,
              1 as reserved
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          LEFT JOIN
              courtmatchup_sports cs ON b.sport_id = cs.id
          WHERE 
              (
                  r.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(player_ids, '[', ''), ']', '')) > 0
              )
              AND b.date >= '${today}'
              ${custom_request ? `AND b.custom_request = 1` : ''}`;
      
      // Now get buddy requests created by the user
      const buddy_sql = `
          SELECT 
              cb.id AS buddy_id,
              cb.sport_id,
              cb.type,
              cb.sub_type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.num_players,
              cb.num_needed,
              cb.ntrp,
              cb.max_ntrp,
              cb.slots,
              cb.need_coach,
              cb.notes,
              cb.player_ids,
              cb.date AS booking_date,
              cb.start_time,
              cb.end_time,
              cb.create_at AS reservation_created_at,
              cb.update_at AS reservation_updated_at,
              cu_main.first_name AS owner_first_name,
              cu_main.last_name AS owner_last_name,
              cu_main.email AS owner_email,
              cu_main.photo AS owner_photo,
              'buddy' as entry_type,
              0 as reserved,
              (SELECT 
                  GROUP_CONCAT(
                      DISTINCT CONCAT(
                          p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                      ) SEPARATOR ';;'
                  )
              FROM 
                  courtmatchup_user p
              LEFT JOIN 
                  courtmatchup_profile prof ON prof.user_id = p.id
              WHERE 
                  FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
              ) AS player_details
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          WHERE 
              cb.user_id = ${req.user_id}
              AND cb.date >= '${today}'`;

      const reservations = await sdk.rawQuery(sql);
      let buddy_requests = await sdk.rawQuery(buddy_sql);

      // Process buddy requests to match reservation format
      buddy_requests = buddy_requests.map(request => {
        const player_details = request.player_details?.split(";;") || [];
        request.player_details = player_details.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=");
          return { first_name, last_name, email, ntrp, photo };
        });
        return {
          ...request,
          reservation_status: 1, // Active status
          booking_type: 'Find Buddy',
          custom_request: 1
        };
      });

      // Remove buddy requests that are already in reservations
      const reservation_buddy_ids = new Set(reservations.filter(r => r.buddy_id).map(r => r.buddy_id));
      buddy_requests = buddy_requests.filter(br => !reservation_buddy_ids.has(br.buddy_id));

      // Combine and sort both lists by create_at date
      const combined_list = [...reservations, ...buddy_requests].sort((a, b) => 
        new Date(b.reservation_created_at) - new Date(a.reservation_created_at)
      );

      // Get other stats
      const program_sql = `SELECT COUNT(*) as programs FROM courtmatchup_clinics WHERE date <= '${sqlDateFormat(new Date())}'`;
      const programs = await sdk.rawQuery(program_sql);

      sdk.setTable("profile");
      const user = await sdk.get({ user_id: req.user_id });
      const ntrp = user[0]?.ntrp || 0;

      const buddy_count_sql = `SELECT COUNT(*) as buddies FROM courtmatchup_buddy WHERE ntrp <= ${ntrp}`;
      const buddies = await sdk.rawQuery(buddy_count_sql);
      
      const total_programs = programs[0]?.programs || 0;
      const total_reservations = combined_list.length;
      const total_hours_left = reservation_hours_left(sdk, req.user_id);

      return res.status(200).json({
        error: false,
        total_programs,
        find_buddy: buddies[0]?.buddies,
        list: combined_list,
        total_reservations,
        total_hours_left
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });
  // Outdated reservations
  app.get("/v3/api/custom/courtmatchup/user/past-reservations", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {custom_request} = req.query;

      const today = sqlDateFormat(new Date());
      
      // Fetch reservations for the user
      sdk.setTable("reservation");
      // First get reservations
      const sql = `SELECT 
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              cs.name AS sport_name,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.receipt_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.subtype as sub_type,
              b.type as type,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type,
              'reservation' as entry_type,
              1 as reserved
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          LEFT JOIN
              courtmatchup_sports cs ON b.sport_id = cs.id
          WHERE 
              (
                  r.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(player_ids, '[', ''), ']', '')) > 0
              )
              AND (b.date <= '${today}' OR b.status = 3)
              ${custom_request ? `AND b.custom_request = 1` : ''}`;
      
      // Now get buddy requests created by the user
      const buddy_sql = `
          SELECT 
              cb.id AS buddy_id,
              cb.sport_id,
              cb.type,
              cb.sub_type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.num_players,
              cb.num_needed,
              cb.ntrp,
              cb.max_ntrp,
              cb.slots,
              cb.need_coach,
              cb.notes,
              cb.player_ids,
              cb.date AS booking_date,
              cb.start_time,
              cb.end_time,
              cb.create_at AS reservation_created_at,
              cb.update_at AS reservation_updated_at,
              cu_main.first_name AS owner_first_name,
              cu_main.last_name AS owner_last_name,
              cu_main.email AS owner_email,
              cu_main.photo AS owner_photo,
              'buddy' as entry_type,
              0 as reserved,
              (SELECT 
                  GROUP_CONCAT(
                      DISTINCT CONCAT(
                          p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                      ) SEPARATOR ';;'
                  )
              FROM 
                  courtmatchup_user p
              LEFT JOIN 
                  courtmatchup_profile prof ON prof.user_id = p.id
              WHERE 
                  FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
              ) AS player_details
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          WHERE 
              cb.user_id = ${req.user_id}
              AND cb.date <= '${today}'`;

      const reservations = await sdk.rawQuery(sql);
      let buddy_requests = await sdk.rawQuery(buddy_sql);

      // Process buddy requests to match reservation format
      buddy_requests = buddy_requests.map(request => {
        const player_details = request.player_details?.split(";;") || [];
        request.player_details = player_details.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=");
          return { first_name, last_name, email, ntrp, photo };
        });
        return {
          ...request,
          reservation_status: 1, // Active status
          booking_type: 'Find Buddy',
          custom_request: 1
        };
      });

      // Remove buddy requests that are already in reservations
      const reservation_buddy_ids = new Set(reservations.filter(r => r.buddy_id).map(r => r.buddy_id));
      buddy_requests = buddy_requests.filter(br => !reservation_buddy_ids.has(br.buddy_id));

      // Combine and sort both lists by create_at date
      const combined_list = [...reservations, ...buddy_requests].sort((a, b) => 
        new Date(b.reservation_created_at) - new Date(a.reservation_created_at)
      );

      // Get other stats
      const program_sql = `SELECT COUNT(*) as programs FROM courtmatchup_clinics WHERE date <= '${sqlDateFormat(new Date())}'`;
      const programs = await sdk.rawQuery(program_sql);

      sdk.setTable("profile");
      const user = await sdk.get({ user_id: req.user_id });
      const ntrp = user[0]?.ntrp || 0;

      const buddy_count_sql = `SELECT COUNT(*) as buddies FROM courtmatchup_buddy WHERE ntrp <= ${ntrp}`;
      const buddies = await sdk.rawQuery(buddy_count_sql);
      
      const total_programs = programs[0]?.programs || 0;
      const total_reservations = combined_list.length;
      const total_hours_left = reservation_hours_left(sdk, req.user_id);

      return res.status(200).json({
        error: false,
        total_programs,
        find_buddy: buddies[0]?.buddies,
        list: combined_list,
        total_reservations,
        total_hours_left
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });
  app.get("/v3/api/custom/courtmatchup/user/past-family-reservations/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {id} = req.params;
      const {custom_request} = req.query;
      // check if the user is the owner of the family member
      sdk.setTable("user");
      const family_member = await sdk.get({ id });
      if (family_member[0]?.guardian !== req.user_id) {
        return res.status(401).json({ error: true, message: "Invalid access" });
      }

      // get the reservations for the family member
      req.user_id = id;


      const today = sqlDateFormat(new Date());
      
      // Fetch reservations for the user
      sdk.setTable("reservation");
      // First get reservations
      const sql = `SELECT 
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              cs.name AS sport_name,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.receipt_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.subtype as sub_type,
              b.type as type,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type,
              'reservation' as entry_type,
              1 as reserved
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          LEFT JOIN
              courtmatchup_sports cs ON b.sport_id = cs.id
          WHERE 
              (
                  r.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(player_ids, '[', ''), ']', '')) > 0
              )
              AND (b.date <= '${today}' OR b.status = 3)
              ${custom_request ? `AND b.custom_request = 1` : ''}`;
      
      // Now get buddy requests created by the user
      const buddy_sql = `
          SELECT 
              cb.id AS buddy_id,
              cb.sport_id,
              cb.type,
              cb.sub_type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.num_players,
              cb.num_needed,
              cb.ntrp,
              cb.max_ntrp,
              cb.slots,
              cb.need_coach,
              cb.notes,
              cb.player_ids,
              cb.date AS booking_date,
              cb.start_time,
              cb.end_time,
              cb.create_at AS reservation_created_at,
              cb.update_at AS reservation_updated_at,
              cu_main.first_name AS owner_first_name,
              cu_main.last_name AS owner_last_name,
              cu_main.email AS owner_email,
              cu_main.photo AS owner_photo,
              'buddy' as entry_type,
              0 as reserved,
              (SELECT 
                  GROUP_CONCAT(
                      DISTINCT CONCAT(
                          p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                      ) SEPARATOR ';;'
                  )
              FROM 
                  courtmatchup_user p
              LEFT JOIN 
                  courtmatchup_profile prof ON prof.user_id = p.id
              WHERE 
                  FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
              ) AS player_details
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          WHERE 
              cb.user_id = ${req.user_id}
              AND cb.date <= '${today}'`;

      const reservations = await sdk.rawQuery(sql);
      let buddy_requests = await sdk.rawQuery(buddy_sql);

      // Process buddy requests to match reservation format
      buddy_requests = buddy_requests.map(request => {
        const player_details = request.player_details?.split(";;") || [];
        request.player_details = player_details.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=");
          return { first_name, last_name, email, ntrp, photo };
        });
        return {
          ...request,
          reservation_status: 1, // Active status
          booking_type: 'Find Buddy',
          custom_request: 1
        };
      });

      // Remove buddy requests that are already in reservations
      const reservation_buddy_ids = new Set(reservations.filter(r => r.buddy_id).map(r => r.buddy_id));
      buddy_requests = buddy_requests.filter(br => !reservation_buddy_ids.has(br.buddy_id));

      // Combine and sort both lists by create_at date
      const combined_list = [...reservations, ...buddy_requests].sort((a, b) => 
        new Date(b.reservation_created_at) - new Date(a.reservation_created_at)
      );

      // Get other stats
      const program_sql = `SELECT COUNT(*) as programs FROM courtmatchup_clinics WHERE date <= '${sqlDateFormat(new Date())}'`;
      const programs = await sdk.rawQuery(program_sql);

      sdk.setTable("profile");
      const user = await sdk.get({ user_id: req.user_id });
      const ntrp = user[0]?.ntrp || 0;

      const buddy_count_sql = `SELECT COUNT(*) as buddies FROM courtmatchup_buddy WHERE ntrp <= ${ntrp}`;
      const buddies = await sdk.rawQuery(buddy_count_sql);
      
      const total_programs = programs[0]?.programs || 0;
      const total_reservations = combined_list.length;
      const total_hours_left = reservation_hours_left(sdk, req.user_id);

      return res.status(200).json({
        error: false,
        total_programs,
        find_buddy: buddies[0]?.buddies,
        list: combined_list,
        total_reservations,
        total_hours_left
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });
  // Get reservations for a family member
  app.get("/v3/api/custom/courtmatchup/user/family-reservations/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {id} = req.params;
      const {custom_request} = req.query;
      // check if the user is the owner of the family member
      sdk.setTable("user");
      const family_member = await sdk.get({ id });
      if (family_member[0]?.guardian !== req.user_id) {
        return res.status(401).json({ error: true, message: "Invalid access" });
      }

      // get the reservations for the family member
      req.user_id = id;


      const today = sqlDateFormat(new Date());
      
      // Fetch reservations for the user
      sdk.setTable("reservation");
      // First get reservations
      const sql = `SELECT 
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              cs.name AS sport_name,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.receipt_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.subtype as sub_type,
              b.type as type,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type,
              'reservation' as entry_type,
              1 as reserved
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          LEFT JOIN
              courtmatchup_sports cs ON b.sport_id = cs.id
          WHERE 
              (
                  r.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(player_ids, '[', ''), ']', '')) > 0
              )
              AND b.date >= '${today}'
              ${custom_request ? `AND b.custom_request = 1` : ''}`;
      
      // Now get buddy requests created by the user
      const buddy_sql = `
          SELECT 
              cb.id AS buddy_id,
              cb.sport_id,
              cb.type,
              cb.sub_type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.num_players,
              cb.num_needed,
              cb.ntrp,
              cb.max_ntrp,
              cb.slots,
              cb.need_coach,
              cb.notes,
              cb.player_ids,
              cb.date AS booking_date,
              cb.start_time,
              cb.end_time,
              cb.create_at AS reservation_created_at,
              cb.update_at AS reservation_updated_at,
              cu_main.first_name AS owner_first_name,
              cu_main.last_name AS owner_last_name,
              cu_main.email AS owner_email,
              cu_main.photo AS owner_photo,
              'buddy' as entry_type,
              0 as reserved,
              (SELECT 
                  GROUP_CONCAT(
                      DISTINCT CONCAT(
                          p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                      ) SEPARATOR ';;'
                  )
              FROM 
                  courtmatchup_user p
              LEFT JOIN 
                  courtmatchup_profile prof ON prof.user_id = p.id
              WHERE 
                  FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
              ) AS player_details
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          WHERE 
              cb.user_id = ${req.user_id}
              AND cb.date >= '${today}'`;

      const reservations = await sdk.rawQuery(sql);
      let buddy_requests = await sdk.rawQuery(buddy_sql);

      // Process buddy requests to match reservation format
      buddy_requests = buddy_requests.map(request => {
        const player_details = request.player_details?.split(";;") || [];
        request.player_details = player_details.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=");
          return { first_name, last_name, email, ntrp, photo };
        });
        return {
          ...request,
          reservation_status: 1, // Active status
          booking_type: 'Find Buddy',
          custom_request: 1
        };
      });

      // Remove buddy requests that are already in reservations
      const reservation_buddy_ids = new Set(reservations.filter(r => r.buddy_id).map(r => r.buddy_id));
      buddy_requests = buddy_requests.filter(br => !reservation_buddy_ids.has(br.buddy_id));

      // Combine and sort both lists by create_at date
      const combined_list = [...reservations, ...buddy_requests].sort((a, b) => 
        new Date(b.reservation_created_at) - new Date(a.reservation_created_at)
      );

      // Get other stats
      const program_sql = `SELECT COUNT(*) as programs FROM courtmatchup_clinics WHERE date <= '${sqlDateFormat(new Date())}'`;
      const programs = await sdk.rawQuery(program_sql);

      sdk.setTable("profile");
      const user = await sdk.get({ user_id: req.user_id });
      const ntrp = user[0]?.ntrp || 0;

      const buddy_count_sql = `SELECT COUNT(*) as buddies FROM courtmatchup_buddy WHERE ntrp <= ${ntrp}`;
      const buddies = await sdk.rawQuery(buddy_count_sql);
      
      const total_programs = programs[0]?.programs || 0;
      const total_reservations = combined_list.length;
      const total_hours_left = reservation_hours_left(sdk, req.user_id);

      return res.status(200).json({
        error: false,
        total_programs,
        find_buddy: buddies[0]?.buddies,
        list: combined_list,
        total_reservations,
        total_hours_left
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });

  app.get("/v3/api/custom/courtmatchup/user/clinics", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("reservation");
      const filters = [];
      const { filter, date, category, tag, subcategory } = req.query;
  
      if (filter) {
        const filterArray = filter.split(",");
        const dayFilters = [];
        const timeFilters = [];
        let weekFilter = null;
  
        filterArray.forEach((f) => {
          switch (f.toLowerCase()) {
            // Day filters
            case "monday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 2`);
              break;
            case "tuesday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 3`);
              break;
            case "wednesday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 4`);
              break;
            case "thursday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 5`);
              break;
            case "friday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 6`);
              break;
            case "saturday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 7`);
              break;
            case "sunday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) = 1`);
              break;
            case "weekend":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) IN (1, 7)`);
              break;
            case "weekday":
              dayFilters.push(`DAYOFWEEK(COALESCE(ut.clinic_date, c.date)) BETWEEN 2 AND 6`);
              break;
  
            // Time filters
            case "morning":
              timeFilters.push(`TIME(c.start_time) BETWEEN '06:00:00' AND '11:59:59'`);
              break;
            case "afternoon":
              timeFilters.push(`TIME(c.start_time) BETWEEN '12:00:00' AND '17:59:59'`);
              break;
            case "evening":
              timeFilters.push(`TIME(c.start_time) >= '18:00:00'`);
              break;
  
            // Week filter
            default:
              if (f.toLowerCase().startsWith("week")) {
                const weekOffset = parseInt(f.toLowerCase().replace("week", ""), 10) || 0;
                weekFilter = `WEEK(COALESCE(ut.clinic_date, c.date)) = WEEK(CURDATE()) + ${weekOffset}`;
              }
              break;
          }
        });
  
        if (dayFilters.length > 0) {
          filters.push(`(${dayFilters.join(" OR ")})`);
        }
        if (timeFilters.length > 0) {
          filters.push(`(${timeFilters.join(" OR ")})`);
        }
        if (weekFilter) {
          filters.push(weekFilter);
        }
      }


      if (date) {
        filters.push(`DATE(COALESCE(ut.clinic_date, c.date)) = '${date}'`);
      }

      let or_filters = [];
      
      // Filter by category name if provided
      if (category) {
        let category_list = category.split(",");
        for (let cat of category_list) {
          // Check if it's a wildcard search with %%
          if (cat.includes('%%')) {
            const searchTerm = cat.replace(/%%/g, '%');
            or_filters.push(`(c.categories IS NOT NULL AND c.categories REGEXP '"name":"[^"]*${searchTerm}[^"]*"')`);
          } else {
            // Use REGEXP for partial matching
            or_filters.push(`(c.categories IS NOT NULL AND c.categories REGEXP '"name":"[^"]*${cat}[^"]*"')`);
          }
        }
      }
      
      // Filter by tag name if provided
      if (tag) {
        let tag_list = tag.split(",");
        for (let tg of tag_list) {
          // Check if it's a wildcard search with %%
          if (tg.includes('%%')) {
            const searchTerm = tg.replace(/%%/g, '%');
            or_filters.push(`(c.tags IS NOT NULL AND c.tags REGEXP '"name":"[^"]*${searchTerm}[^"]*"')`);
          } else {
            // Use REGEXP for partial matching
            or_filters.push(`(c.tags IS NOT NULL AND c.tags REGEXP '"name":"[^"]*${tg}[^"]*"')`);
          }
        }
      }
      
      // Filter by subcategory name if provided
      if (subcategory) {
        let subcategory_list = subcategory.split(",");
        for (let sub of subcategory_list) {
          // Check if it's a wildcard search with %%
          if (sub.includes('%%')) {
            const searchTerm = sub.replace(/%%/g, '%');
            or_filters.push(`(c.subcategories IS NOT NULL AND c.subcategories REGEXP '"name":"[^"]*${searchTerm}[^"]*"')`);
          } else {
            // Use REGEXP for partial matching
            or_filters.push(`(c.subcategories IS NOT NULL AND c.subcategories REGEXP '"name":"[^"]*${sub}[^"]*"')`);
          }
        }
      }
    // end_date
  
      const sql = `WITH RECURSIVE upcoming_thursdays AS (
              SELECT 
                  courtmatchup_clinics.id AS clinic_id,
                  courtmatchup_clinics.description AS details,
                  courtmatchup_clinics.name AS clinic_name,
                  courtmatchup_clinics.date AS clinic_date,
                  courtmatchup_clinics.end_date AS clinic_end_date,
                  courtmatchup_clinics.categories,
                  courtmatchup_clinics.tags,
                  courtmatchup_clinics.subcategories,
                  courtmatchup_clinics.recurring,
                  courtmatchup_clinics.max_participants,
                  courtmatchup_clinics.start_time,
                  courtmatchup_clinics.end_time,
                  courtmatchup_clinics.cost_per_head,
                  courtmatchup_clinics.level AS clinic_level,
                  courtmatchup_clinics.cancellation_policy_days,
                  courtmatchup_clinics.sport_id,
                  cs.name AS sport_name,
                  courtmatchup_clinics.type,
                  courtmatchup_clinics.sub_type,
                  courtmatchup_clinics.club_id,
                  courtmatchup_clinics.surface_id,
                  courtmatchup_clinics.age_group,
                  DATE_ADD(courtmatchup_clinics.date, INTERVAL 1 WEEK) AS next_date
              FROM courtmatchup_clinics
              LEFT JOIN courtmatchup_sports cs ON courtmatchup_clinics.sport_id = cs.id
              WHERE courtmatchup_clinics.recurring = 1 AND courtmatchup_clinics.date >= CURDATE()
              
              UNION ALL
              
              SELECT 
                  c.id,
                  description AS details,
                  c.name AS clinic_name,
                  ut.next_date AS clinic_date,
                  c.end_date AS clinic_end_date,
                  c.categories,
                  c.tags,
                  c.subcategories,
                  c.recurring,
                  c.max_participants,
                  c.start_time,
                  c.end_time,
                  c.cost_per_head,
                  c.level AS clinic_level,
                  c.cancellation_policy_days,
                  c.sport_id,
                  cs.name AS sport_name,
                  c.type,
                  c.sub_type,
                  c.surface_id,
                  c.club_id,
                  c.age_group,
                  DATE_ADD(ut.next_date, INTERVAL 1 WEEK) AS next_date
              FROM upcoming_thursdays ut
              JOIN courtmatchup_clinics c ON ut.clinic_id = c.id
              LEFT JOIN courtmatchup_sports cs ON c.sport_id = cs.id
              WHERE ut.next_date < CURDATE() + INTERVAL 6 MONTH
          )
          SELECT 
              c.id,
              c.name,
              description AS details,
              c.type,
              c.sub_type,
              c.surface_id,
              c.categories,
              c.tags,
              c.subcategories,
              COALESCE(ut.clinic_date, c.date) AS clinic_date,
              c.end_date AS clinic_end_date,
              c.recurring,
              c.start_time AS clinic_start_time,
              c.sport_id,
              cs.name AS sport_name,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              c.cancellation_policy_days,
              COUNT(DISTINCT r.id) AS reservations_count,
              CASE 
                  WHEN c.max_participants - COUNT(DISTINCT r.id) - 
                      SUM(
                          CASE 
                              WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                                  LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                              ELSE 0
                          END
                      ) > 0 THEN c.max_participants - COUNT(DISTINCT r.id) - 
                      SUM(
                          CASE 
                              WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                                  LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                              ELSE 0
                          END
                      )
                  ELSE 0
              END AS slots_remaining,
              CASE 
                  WHEN ${req.user_id} IN (r.user_id) OR 
                      FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(b.player_ids, '[', ''), ']', '')) > 0 THEN 1
                  ELSE 0
              END AS is_user_participating,
              DAYNAME(COALESCE(ut.clinic_date, c.date)) AS clinic_day,
              CASE 
                  WHEN TIME(c.start_time) BETWEEN '06:00:00' AND '11:59:59' THEN 'Morning'
                  WHEN TIME(c.start_time) BETWEEN '12:00:00' AND '17:59:59' THEN 'Afternoon'
                  WHEN TIME(c.start_time) >= '18:00:00' THEN 'Evening'
                  ELSE 'Unknown'
              END AS time_of_day,
              GROUP_CONCAT(DISTINCT cc.coach_id) AS coach_ids,
              GROUP_CONCAT(
                  DISTINCT CONCAT(
                      u.first_name, '==', u.last_name, '==' ,u.email,
                      '==', COALESCE(co.bio, 'N/A'),
                      '==', COALESCE(co.hourly_rate, 'N/A'),
                      '==', u.id,
                      '==', COALESCE(u.photo, 'N/A')
                  ) SEPARATOR ';;'
              ) AS coach_details
          FROM 
              courtmatchup_clinics c
          LEFT JOIN 
              courtmatchup_booking b ON c.id = b.clinic_id
          LEFT JOIN 
              courtmatchup_reservation r ON b.id = r.booking_id
          LEFT JOIN 
              upcoming_thursdays ut ON c.id = ut.clinic_id
          LEFT JOIN 
              courtmatchup_clinic_coaches cc ON c.id = cc.clinic_id
          LEFT JOIN 
              courtmatchup_coach co ON cc.coach_id = co.user_id
          LEFT JOIN 
              courtmatchup_user u ON co.user_id = u.id
          LEFT JOIN 
              courtmatchup_sports cs ON c.sport_id = cs.id
          WHERE 
              (c.date >= CURDATE() OR c.recurring = 1)
              ${filters.length > 0 ? `AND (${filters.join(" AND ")})` : ""}
              ${or_filters.length > 0 ? `AND (${or_filters.join(" OR ")})` : ""}
          GROUP BY 
              c.id, c.name, COALESCE(ut.clinic_date, c.date), c.start_time, c.end_time, c.cost_per_head, c.max_participants, c.level
          ORDER BY 
              clinic_date ASC;`;
  
      const programs = await sdk.rawQuery(sql);

      for (let program of programs) {
        // Parse JSON fields
        if (program.categories) {
          try {
            program.categories = JSON.parse(program.categories);
          } catch (e) {
            program.categories = [];
          }
        } else {
          program.categories = [];
        }
        
        if (program.tags) {
          try {
            program.tags = JSON.parse(program.tags);
          } catch (e) {
            program.tags = [];
          }
        } else {
          program.tags = [];
        }
        
        if (program.subcategories) {
          try {
            program.subcategories = JSON.parse(program.subcategories);
          } catch (e) {
            program.subcategories = [];
          }
        } else {
          program.subcategories = [];
        }
        
        // Process coach details
        program.coach_details = program.coach_details?.split(";;") ? program.coach_details.split(";;").map(coach => {
          const c = coach?.split("==") ?? [];
          return {
            first_name: c[0],
            last_name: c[1],
            email: c[2],
            bio: c[3],
            hourly_rate: c[4],
            user_id: c[5],
            photo: c[6]
          }
        }) : [];
        program.coach_ids = program.coach_ids?.split(",") ?? [];
      }
  
      // Extract unique category names, tag names, and subcategory names from programs
      const uniqueCategories = new Set();
      const uniqueTags = new Set();
      const uniqueSubcategories = new Set();
      
      for (const program of programs) {
        if (program.categories) {
          program.categories.forEach(cat => {
            if (cat.name) uniqueCategories.add(cat.name);
          });
        }
        
        if (program.tags) {
          program.tags.forEach(tag => {
            if (tag.name) uniqueTags.add(tag.name);
          });
        }
        
        if (program.subcategories) {
          program.subcategories.forEach(subcat => {
            if (subcat.name) uniqueSubcategories.add(subcat.name);
          });
        }
      }
      
      return res.status(200).json({
        error: false,
        programs,
        filter: {
          days: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
          times: ["morning", "afternoon", "evening"],
          others: ["weekend", "weekday", "week", "week+1", "week+2"],
          categories: Array.from(uniqueCategories),
          tags: Array.from(uniqueTags),
          subcategories: Array.from(uniqueSubcategories)
        }
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });
  
  

  // Create reservation
  app.post("/v3/api/custom/courtmatchup/user/reservations", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      let {
        sport_id,
        type=1,
        sub_type:subtype="Hard",
        user_id,
        date,
        start_time,
        end_time,
        duration,
        court_id,
        coach_id,
        clinic_id,
        min_ntrp,
        max_ntrp,
        coaches = null,
        buddy_id = null,
        buddy_request = 0,
        last_4 = 0,
        reservation_type=1,
        notes="",
        price=0,
        club_fee=0,
        service_fee=0,
        coach_fee=0,
        clinic_fee=0,
        court_fee=0,
        player_ids,
        num_players=null,
        custom_request = 0,
        buddy_details = null,
        payment_status = 0,
        payment_intent = null,
      } = req.body;

      // Format times for consistency
      start_time = formatTimeForMySQL(start_time);
      end_time = formatTimeForMySQL(end_time);

      // Calculate duration if not provided
      if (!duration) {
        // Calculate duration based on start and end time in hours
        const [startHours, startMinutes] = start_time.split(':').map(Number);
        const [endHours, endMinutes] = end_time.split(':').map(Number);
        
        // Convert start and end times to total minutes
        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;
      
        // Calculate the difference in minutes and convert to hours
        duration = (endTotalMinutes - startTotalMinutes) / 60;
      }

      sdk.setTable("user");
      const user = (await sdk.get({ id: req.user_id }))[0];
      const club_id = user?.club_id;
      if(user_id){
        const user = (await sdk.get({ id: user_id }))[0];
        if(!user){
          throw new Error("User not found");
        }
        // check if user is the guardian
        if(user.guardian !== req.user_id){
          throw new Error("You are not the guardian of this user");
        }else{
          req.user_id = user_id;
        }
      }


      // Find qualifying courts based on sport, type, and subtype
      sdk.setTable("club_court");
      const qualifyingCourts = await sdk.get(filterEmptyFields({
        club_id: club_id,
        sport_id,
        type,
        sub_type: subtype
      }));

      if (!qualifyingCourts.length) {
        throw new Error("No courts available that match the selected criteria");
      }

      // Check for booking conflicts on all qualifying courts
      const courtIds = qualifyingCourts.map(court => court.id);
      const checkSql = `
        SELECT 
          b.id,
          b.court_id
        FROM 
          courtmatchup_booking b
        WHERE 
          b.court_id IN (${courtIds.join(',')})
          AND b.date = '${sqlDateFormat(new Date(date))}'
          AND (
            (b.start_time <= '${start_time}' AND b.end_time > '${start_time}')
            OR 
            (b.start_time < '${end_time}' AND b.end_time >= '${end_time}')
            OR 
            (b.start_time >= '${start_time}' AND b.end_time <= '${end_time}')
          )
      `;
      
      const existingEvents = await sdk.rawQuery(checkSql);
      
      // Create a map of court IDs with conflicts
      const conflictingCourtIds = new Set(existingEvents.map(event => event.court_id));
      
      // Filter out courts with conflicts
      const availableCourts = qualifyingCourts.filter(court => !conflictingCourtIds.has(court.id));
      
      if (availableCourts.length === 0) {
        throw new Error("No courts available for the selected date and time");
      }

      // Find minimum booking time among available courts
      let minBookingTime = 60; // Default minimum booking time in minutes
      let selectedCourt = null;

      for (const court of availableCourts) {
        try {
          const courtSettings = JSON.parse(court.court_settings || '{"min_booking_time":60}');
          const courtMinTime = courtSettings.min_booking_time || 60;
          
          // Convert duration from hours to minutes for comparison
          if ((duration * 60) >= courtMinTime) {
            // If court_id was specified and matches this court, select it
            if (court_id && court.id == court_id) {
              selectedCourt = court;
              break;
            }
            
            // Otherwise take the first available court that meets the duration requirement
            if (!selectedCourt) {
              selectedCourt = court;
            }
          }
        } catch(e) {
          console.log("Error parsing court settings:", e);
        }
      }

      if (!selectedCourt) {
        // Find the minimum booking time among all available courts
        let minRequiredTime = Number.MAX_SAFE_INTEGER;
        availableCourts.forEach(court => {
          try {
            const courtSettings = JSON.parse(court.court_settings || '{"min_booking_time":60}');
            const courtMinTime = courtSettings.min_booking_time || 60;
            minRequiredTime = Math.min(minRequiredTime, courtMinTime);
          } catch(e) {
            console.log("Error parsing court settings:", e);
          }
        });
        
        if (minRequiredTime === Number.MAX_SAFE_INTEGER) {
          minRequiredTime = 60;
        }
        
        // Convert minutes to hours for error message
        const minRequiredHours = minRequiredTime / 60;
        throw new Error(`Selected duration (${duration} hours) is less than the minimum booking time required (${minRequiredHours} hours)`);
      }

      // Use the selected court
      court_id = selectedCourt.id;

      if (await reservation_hours_left(sdk, req.user_id) + duration > 20) {
        throw new Error("Reservation hours exceeded for the week");
      }

      if (!sport_id || !date || !start_time || !end_time || !duration || !player_ids) {
        throw new Error("All fields are required");
      }

      if(custom_request === 1 && reservation_type !== 3){
        throw new Error("Custom request is only allowed for lesson");
      }

      if (buddy_request === 1 && !buddy_id) {
        throw new Error("Please pass buddy_id");
      } else if (buddy_request === 1 && buddy_id) {
        sdk.setTable("buddy");
        const buddy = (await sdk.get({ id: buddy_id, user_id: req.user_id }))[0];
        if (!buddy) {
          throw new Error("Invalid buddy ID");
        }
        date = new Date(buddy.date);
        // check if date has passed
        if (date <= new Date()) {
          throw new Error("Date from find a buddy has passed");
        }
        
        if (!start_time || !end_time) {
          start_time = (buddy.start_time)
          end_time = (buddy.end_time)
        }
        sport_id = (buddy.sport_id)
        type = (buddy.type)
        subtype = (buddy.subtype)
        try{
          player_ids =(buddy.player_ids) ? JSON.parse(buddy.player_ids) : []
        }catch(e){
          player_ids = []
        }
        num_players = (buddy.num_players)
        notes = (buddy.notes)
      }

      if (reservation_type === 3 && custom_request === 0 && !coach_id) {
        throw new Error("Please select a coach");
      }

      if (reservation_type === 2 && !clinic_id) {
        throw new Error("Please select a clinic");
      }else if (reservation_type === 2 && clinic_id) {
        sdk.setTable("clinics");
        const clinic = (await sdk.get({ id: clinic_id }))[0];
        if (!clinic) {
          throw new Error("Invalid clinic ID");
        }
        const clinic_date = new Date(clinic.date);
        if (clinic.recurring == 0 && clinic_date < new Date()) {
          throw new Error("Invalid clinic date");
        }

        start_time = (clinic.start_time)
        end_time = (clinic.end_time)
      }

      // random numbers of length 11 for receipt_id
      const receipt_id = `${Math.floor(Math.random() * 10000000000000000)}`.slice(-11);

      // Create booking
      sdk.setTable("booking");
      const booking_id = await sdk.insert(filterEmptyFields({
        user_id: req.user_id,
        sport_id,
        type,
        subtype,
        status: payment_status,
        clinic_id,
        coach_id,
        coaches: coaches ? JSON.stringify(coaches) : null,
        court_id, // Now using the selected court ID
        last_4,
        min_ntrp,
        max_ntrp,
        club_fee,
        receipt_id,
        service_fee,
        coach_fee,
        clinic_fee,
        court_fee,
        reservation_type,
        custom_request,
        notes,
        price,
        duration,
        payment_intent: payment_intent ? payment_intent : null,
        player_ids: JSON.stringify(player_ids),
        date: sqlDateFormat(new Date(date)),
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
        start_time,
        end_time,
        club_id
      }));

      // Create reservation
      sdk.setTable("reservation");
      const reservation_id = await sdk.insert(filterEmptyFields({
        booking_id,
        buddy_id,
        user_id: req.user_id,
        club_id,
        reservation_type,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      }));

      // Add players to reservation team
      sdk.setTable("reservation_team");
      for (let i = 0; i < player_ids.length; i++) {
        await sdk.insert({
          user_id: player_ids[i],
          reservation_id,
          booking_id,
          creator: req.user_id === player_ids[i] ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });
      }

      await log_reservation(sdk, req.user_id, reservation_id);

      // Handle buddy request
      if (buddy_request === 1) {
        sdk.setTable("buddy");
        const updated = await sdk.update({
          reservation_id,
        }, buddy_id);

        sdk.setTable("reservation");
        await sdk.update({ buddy_id: buddy_id }, reservation_id);
      }

      return res.status(200).json({ 
        error: false, 
        reservation_id, 
        booking_id,
        court_id,
        court_name: selectedCourt.name
      });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });

  app.post("/v3/api/custom/courtmatchup/user/reservations/subscribe",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { clinic_id } = req.body;

      if (!clinic_id) throw new Error("Clinic ID is required");

      sdk.setTable("clinics");
      const clinic = (await sdk.get({id: clinic_id}))[0];
      if (!clinic) throw new Error("Clinic not found");

      if (clinic.recurring == 0 && clinic.date < new Date()) {
        throw new Error("Clinic date has passed");
      }
      
      sdk.setTable("clinic_subscription");
      const subscription_id = await sdk.insert({
        clinic_id,
        user_id: req.user_id,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      
      return res.status(200).json({
        error: false,
        message: "Subscribed to clinic",
        subscription_id
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/courtmatchup/user/reservations/clinic-subscription",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("clinic_subscription");
      const subscriptions = await sdk.rawQuery(`SELECT courtmatchup_clinics.* FROM courtmatchup_clinic_subscription LEFT JOIN courtmatchup_clinics ON courtmatchup_clinic_subscription.clinic_id = courtmatchup_clinics.id WHERE user_id = ${req.user_id}`);
      
      return res.status(200).json({
        error: false,
        subscriptions
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // unsubcribe from clinic
  app.post("/v3/api/custom/courtmatchup/user/reservations/unsubscribe",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { subscription_id } = req.body;

      if (!subscription_id) throw new Error("Subscription ID is required");

      
      
      sdk.setTable("clinic_subscription");
      const subscription = (await sdk.get({id: subscription_id}))[0];
      if (!subscription) throw new Error("Subscription not found");

      await sdk.delete({id: subscription_id});

      
      return res.status(200).json({
        error: false,
        message: "Unsubscribed from clinic",
        subscription_id
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/courtmatchup/user/reservations/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {id} = req.params;

      
      // Fetch reservations for the user
      sdk.setTable("reservation");
      // const reservations = await sdk.get({ user_id: req.user_id });
      const sql = `SELECT 
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              -- Fetch details from clinics if clinic_id is not null
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
          -- Count player_ids
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              -- Determine the type based on the provided logic
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          WHERE 
              r.id = ${id}
                            `
      const reservations = await sdk.rawQuery(sql);

      return res.status(200).json({
        error: false,
        model: reservations[0] || {}
      });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });

  // Get detail lesson reservation
  app.get(_base + "/reservations/lesson/:reservation_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { reservation_id } = req.params;

      const sql = `
        SELECT 
          r.id AS reservation_id,
          r.booking_id,
          r.status AS reservation_status,
          r.create_at AS reservation_created_at,
          r.update_at AS reservation_updated_at,
          b.user_id AS booking_user_id,
          b.sport_id,
          b.type AS type,
          b.subtype AS subtype,
          'Lesson' as booking_type,
          b.date AS booking_date,
          b.start_time,
          b.end_time,
          b.coach_id,
          b.notes,
          b.price,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.payment_intent,
          b.player_ids,
          b.duration,
          b.status AS booking_status,
          -- Coach details
          coach.first_name AS coach_first_name,
          coach.last_name AS coach_last_name,
          coach.email AS coach_email,
          coach.photo AS coach_photo,
          coach_profile.bio AS coach_bio,
          coach_profile.hourly_rate AS coach_rate,
          -- Creator details
          creator.first_name AS creator_first_name,
          creator.last_name AS creator_last_name,
          creator.email AS creator_email,
          creator.photo AS creator_photo,
          -- Sport details
          s.name AS sport_name
        FROM 
          courtmatchup_reservation r
        LEFT JOIN 
          courtmatchup_booking b ON r.booking_id = b.id
        LEFT JOIN
          courtmatchup_user coach ON b.coach_id = coach.id
        LEFT JOIN
          courtmatchup_coach coach_profile ON coach.id = coach_profile.user_id
        LEFT JOIN
          courtmatchup_user creator ON b.user_id = creator.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        WHERE 
          r.id = ${reservation_id}
      `;

      const reservations = await sdk.rawQuery(sql);

      if (!reservations.length) {
        return res.status(404).json({
          error: true,
          message: "Lesson reservation not found"
        });
      }

      const reservation = reservations[0];
      // Parse player_ids and get player details
      const player_ids = JSON.parse(reservation.player_ids || "[]");
      
      if (player_ids.length) {
        const playersSql = `
          SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            u.photo,
            p.ntrp
          FROM courtmatchup_user u
          LEFT JOIN courtmatchup_profile p ON u.id = p.user_id
          WHERE u.id IN (${player_ids.join(",")})
        `;
        const players = await sdk.rawQuery(playersSql);
        reservation.players = players;
      } else {
        reservation.players = [];
      }

      return res.status(200).json({
        error: false,
        reservation
      });

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get detail court reservation
  app.get(_base + "/reservations/court/:reservation_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { reservation_id } = req.params;

      const sql = `
        SELECT 
          r.id AS reservation_id,
          r.booking_id,
          r.status AS reservation_status,
          r.create_at AS reservation_created_at,
          r.update_at AS reservation_updated_at,
          b.user_id AS booking_user_id,
          b.sport_id,
          b.type AS type,
          b.subtype AS sub_type,
          'Court' as booking_type,
          b.date AS booking_date,
          b.start_time,
          b.end_time,
          b.court_id,
          b.notes,
          b.price,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.payment_intent,
          b.player_ids,
          b.duration,
          b.status AS booking_status,
          -- Court details (if exists)
          COALESCE(c.name, '') AS court_name,
          -- Creator details
          creator.first_name AS creator_first_name,
          creator.last_name AS creator_last_name,
          creator.email AS creator_email,
          creator.photo AS creator_photo,
          -- Sport details
          s.name AS sport_name
        FROM 
          courtmatchup_reservation r
        LEFT JOIN 
          courtmatchup_booking b ON r.booking_id = b.id
        LEFT JOIN
          courtmatchup_club_court c ON b.court_id = c.id
        LEFT JOIN
          courtmatchup_user creator ON b.user_id = creator.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        WHERE 
          r.id = ${reservation_id}
      `;

      const reservations = await sdk.rawQuery(sql);

      if (!reservations.length) {
        return res.status(404).json({
          error: true,
          message: "Court reservation not found"
        });
      }

      const reservation = reservations[0];
      // Parse player_ids and get player details
      const player_ids = JSON.parse(reservation.player_ids || "[]");
      
      if (player_ids.length) {
        const playersSql = `
          SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            u.photo,
            p.ntrp
          FROM courtmatchup_user u
          LEFT JOIN courtmatchup_profile p ON u.id = p.user_id
          WHERE u.id IN (${player_ids.join(",")})
        `;
        const players = await sdk.rawQuery(playersSql);
        reservation.players = players;
      } else {
        reservation.players = [];
      }

      return res.status(200).json({
        error: false,
        reservation
      });

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get detail clinic reservation
  app.get(_base + "/reservations/clinic/:reservation_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { reservation_id } = req.params;

      const sql = `
        SELECT 
          r.id AS reservation_id,
          r.booking_id,
          r.status AS reservation_status,
          r.create_at AS reservation_created_at,
          r.update_at AS reservation_updated_at,
          b.user_id AS booking_user_id,
          b.sport_id,
          b.type AS type,
          b.subtype AS sub_type,
          'Clinic' as booking_type,
          b.date AS booking_date,
          b.start_time,
          b.end_time,
          b.clinic_id,
          b.notes,
          b.price,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.payment_intent,
          b.player_ids,
          b.duration,
          b.status AS booking_status,
          -- Clinic details
          c.name AS clinic_name,
          c.description AS clinic_description,
          c.start_time AS clinic_start_time,
          c.end_time AS clinic_end_time,
          c.cost_per_head AS clinic_cost,
          c.max_participants,
          c.level AS clinic_level,
          -- Creator details
          creator.first_name AS creator_first_name,
          creator.last_name AS creator_last_name,
          creator.email AS creator_email,
          creator.photo AS creator_photo,
          -- Sport details
          s.name AS sport_name,
          -- Coach details from clinic_coaches
          GROUP_CONCAT(
            DISTINCT CONCAT(
              coach.first_name, '==',
              coach.last_name, '==',
              coach.email, '==',
              COALESCE(cp.bio, ''), '==',
              COALESCE(cp.hourly_rate, ''), '==',
              coach.id, '==',
              COALESCE(coach.photo, '')
            ) SEPARATOR ';;'
          ) AS coach_details
        FROM 
          courtmatchup_reservation r
        LEFT JOIN 
          courtmatchup_booking b ON r.booking_id = b.id
        LEFT JOIN
          courtmatchup_clinics c ON b.clinic_id = c.id
        LEFT JOIN
          courtmatchup_user creator ON b.user_id = creator.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_clinic_coaches cc ON c.id = cc.clinic_id
        LEFT JOIN
          courtmatchup_user coach ON cc.coach_id = coach.id
        LEFT JOIN
          courtmatchup_coach cp ON coach.id = cp.user_id
        WHERE 
          r.id = ${reservation_id}
        GROUP BY r.id
      `;

      const reservations = await sdk.rawQuery(sql);

      if (!reservations.length) {
        return res.status(404).json({
          error: true,
          message: "Clinic reservation not found"
        });
      }

      const reservation = reservations[0];
      
      // Parse coach details
      reservation.coaches = reservation.coach_details?.split(";;").map(coach => {
        const [first_name, last_name, email, bio, hourly_rate, id, photo] = coach?.split("==") || [];
        return { first_name, last_name, email, bio, hourly_rate, id, photo };
      }) || [];
      delete reservation.coach_details;

      // Parse player_ids and get player details
      const player_ids = JSON.parse(reservation.player_ids || "[]");
      
      if (player_ids.length) {
        const playersSql = `
          SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            u.photo,
            p.ntrp
          FROM courtmatchup_user u
          LEFT JOIN courtmatchup_profile p ON u.id = p.user_id
          WHERE u.id IN (${player_ids.join(",")})
        `;
        const players = await sdk.rawQuery(playersSql);
        reservation.players = players;
      } else {
        reservation.players = [];
      }

      return res.status(200).json({
        error: false,
        reservation
      });

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get detail find buddy reservation
  app.get(_base + "/reservations/buddy/:reservation_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { reservation_id } = req.params;

      const sql = `
        SELECT 
          r.id AS reservation_id,
          r.booking_id,
          r.buddy_id,
          r.status AS reservation_status,
          r.create_at AS reservation_created_at,
          r.update_at AS reservation_updated_at,
          -- Booking details
          b.user_id AS booking_user_id,
          b.sport_id,
          b.type AS type,
          b.subtype AS sub_type,
          'Find Buddy' as booking_type,
          b.date AS booking_date,
          b.start_time,
          b.end_time,
          b.notes,
          b.price,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.payment_intent,
          b.player_ids,
          b.duration,
          b.status AS booking_status,
          -- Buddy details
          bd.sport_id AS buddy_sport_id,
          bd.type AS buddy_type,
          bd.ntrp AS buddy_ntrp,
          bd.max_ntrp AS buddy_max_ntrp,
          bd.num_players AS buddy_num_players,
          bd.num_needed AS buddy_num_needed,
          bd.need_coach AS buddy_need_coach,
          bd.notes AS buddy_notes,
          bd.date AS buddy_date,
          bd.start_time AS buddy_start_time,
          bd.end_time AS buddy_end_time,
          -- Creator details
          creator.first_name AS creator_first_name,
          creator.last_name AS creator_last_name,
          creator.email AS creator_email,
          creator.photo AS creator_photo,
          -- Sport details
          s.name AS sport_name
        FROM 
          courtmatchup_reservation r
        LEFT JOIN 
          courtmatchup_booking b ON r.booking_id = b.id
        LEFT JOIN
          courtmatchup_buddy bd ON r.buddy_id = bd.id
        LEFT JOIN
          courtmatchup_user creator ON b.user_id = creator.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        WHERE 
          r.id = ${reservation_id}
          AND r.buddy_id IS NOT NULL
      `;

      const reservations = await sdk.rawQuery(sql);

      if (!reservations.length) {
        return res.status(404).json({
          error: true,
          message: "Buddy reservation not found"
        });
      }

      const reservation = reservations[0];
      
      // Parse player_ids and get player details
      const player_ids = JSON.parse(reservation.player_ids || "[]");
      
      if (player_ids.length) {
        const playersSql = `
          SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            u.photo,
            p.ntrp
          FROM courtmatchup_user u
          LEFT JOIN courtmatchup_profile p ON u.id = p.user_id
          WHERE u.id IN (${player_ids.join(",")})
        `;
        const players = await sdk.rawQuery(playersSql);
        reservation.players = players;
      } else {
        reservation.players = [];
      }

      // Get buddy requests
      if (reservation.buddy_id) {
        const requestsSql = `
          SELECT 
            br.id AS request_id,
            br.user_id,
            br.ntrp,
            br.num_players,
            br.status,
            br.create_at,
            u.first_name,
            u.last_name,
            u.email,
            u.photo
          FROM courtmatchup_buddy_request br
          LEFT JOIN courtmatchup_user u ON br.user_id = u.id
          WHERE br.buddy_id = ${reservation.buddy_id}
        `;
        const requests = await sdk.rawQuery(requestsSql);
        reservation.buddy_requests = requests;
      } else {
        reservation.buddy_requests = [];
      }

      return res.status(200).json({
        error: false,
        reservation
      });

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // app.get("/v3/api/custom/courtmatchup/user/reservations/availability/:court_id", middlewares, async function (req, res) {
  //   try {
  //     let sdk = req.sdk;
  //     sdk.getDatabase();
  //     sdk.setProjectId(req.projectId);

  //     const {court_id} = req.params;
  //     console.log('court_id :>> ', court_id);

  //     if (!court_id) {
  //       throw new Error("Court id is required");
  //     }
      
  //     sdk.setTable("club_court")
  //     const court = (await sdk.get({id: req.params.court_id}))[0];

  //     if (!court) {
  //       throw new Error("Court not found");
  //     }
  //     const availability = JSON.parse(court?.availability || "[]");
  //     const slots_for_next_month = generateAvailabilityForNextMonth(availability);

  //     return res.status(200).json({
  //       error: false,
  //       availability,
  //       slots_for_next_month
  //     })
  //   }catch (err) {
  //     res.status(403);
  //     res.json({
  //       error: true,
  //       message: err.message
  //     });
  //   }
  // });
  app.get("/v3/api/custom/courtmatchup/user/reservations/availability/:club_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {club_id} = req.params;
      const { sport_id, type=null, subtype=null, date=null, court_id=null, start_time=null, end_time=null } = req.query;
      console.log('club_id :>> ', club_id);
      
      // Get club details to check working hours, days off, and exceptions
      sdk.setTable("clubs");
      const clubData = await sdk.get({ id: club_id });
      
      if (!clubData.length) {
        throw new Error("Club not found");
      }
      
      const club = clubData[0];
      const clubTimes = JSON.parse(club.times || '[]');
      const clubDaysOff = JSON.parse(club.days_off || '[]');
      const clubExceptions = JSON.parse(club.exceptions || '[]');
      
      sdk.setTable("club_court")
      // Get courts that match sport_id, type, and subtype criteria
      let courts = (await sdk.get(filterEmptyFields({
        club_id: club_id, 
        sport_id, 
        type: type,
        id: court_id,
        sub_type: subtype
      })));

      // Filter out courts that have allow_reservation set to false in court_settings
      courts = courts.filter(court => {
        try {
          const courtSettings = JSON.parse(court.court_settings || '{}');
          // Only include courts that allow reservations (default to true if not specified)
          return courtSettings.allow_reservation !== false;
        } catch(e) {
          console.log("Error parsing court settings:", e);
          return true; // Default to allowing if we can't parse settings
        }
      });

      if (!courts.length) {
        throw new Error("No courts available that match the selected criteria");
      }

      // Extract court IDs to check for booking conflicts
      const courtIds = courts.map(court => court.id);
      
      // Check for existing bookings on the matching courts if date is provided
      let unavailableTimeSlots = [];
      if (date) {
        const bookingsSql = `
          SELECT 
            court_id, start_time, end_time, date
          FROM 
            courtmatchup_booking
          WHERE 
            court_id IN (${courtIds.join(',')})
            AND date = '${sqlDateFormat(new Date(date))}'
            AND club_id = ${club_id}
        `;
        
        const existingBookings = await sdk.rawQuery(bookingsSql);
        
        // Extract unavailable time slots from bookings
        unavailableTimeSlots = existingBookings.map(booking => ({
          court_id: booking.court_id,
          date: booking.date,
          start_time: booking.start_time,
          end_time: booking.end_time
        }));
      } 
      if(court_id){
        const bookingsSql = `
          SELECT 
            court_id, start_time, end_time, date
          FROM 
            courtmatchup_booking
          WHERE
            court_id = ${court_id}
            AND club_id = ${club_id}
        `;
        const existingBookings = await sdk.rawQuery(bookingsSql);
        unavailableTimeSlots = existingBookings.map(booking => ({
          court_id: booking.court_id,
          date: booking.date,
          start_time: booking.start_time,
          end_time: booking.end_time
        }));
      }

      // check for existing reservations for next 30 days
      const reservationsSql = `
        SELECT 
          court_id, start_time, end_time, date
        FROM 
          courtmatchup_booking
        WHERE
          date >= '${sqlDateFormat(new Date())}'
          AND date <= '${sqlDateFormat(new Date(new Date().setDate(new Date().getDate() + 30)))}'
          AND club_id = ${club_id}
      `;
      const _existingReservations = await sdk.rawQuery(reservationsSql);
      unavailableTimeSlots = _existingReservations.map(reservation => ({
        court_id: reservation.court_id,
        date: reservation.date,
        start_time: reservation.start_time,
        end_time: reservation.end_time,
      }));
      try{
        if (start_time && end_time) {
          const startSec = parseTimeToSeconds(start_time);
          const endSec = parseTimeToSeconds(end_time);
        
          courts = courts.filter(court => {
            const availability = JSON.parse(court.availability || '[]');
            // Keep court if any availability slot overlaps the requested range
            return availability.some(slot => {
              const slotStart = parseTimeToSeconds(slot.start_time);
              const slotEnd = parseTimeToSeconds(slot.end_time);
              // Overlap condition: slot starts before end AND slot ends after start
              return slotStart < endSec && slotEnd > startSec;
            });
          });
        }
        
      }catch(e){
        console.log(e)
      }

      // Find minimum booking time among qualifying courts
      let minBookingTime = Number.MAX_SAFE_INTEGER;
      courts.forEach(court => {
        try {
          const courtSettings = JSON.parse(court.court_settings || '{"min_booking_time":60}');
          const courtMinTime = courtSettings.min_booking_time || 60;
          minBookingTime = Math.min(minBookingTime, courtMinTime);
        } catch(e) {
          console.log("Error parsing court settings:", e);
        }
      });
      
      // Default to 60 minutes if no valid min booking time found
      if (minBookingTime === Number.MAX_SAFE_INTEGER) {
        minBookingTime = 60;
      }

      // Process availabilities
      const availability_list = [];
      const courtsWithAvailability = [];
      const courtsWithoutAvailability = [];
      
      // First, collect courts with and without availability
      for (let i = 0; i < courts.length; i++) {
        try {
          // Parse court settings to check if reservations are allowed
          const courtSettings = JSON.parse(courts[i].court_settings || '{}');
          
          // Skip this court if reservations are not allowed (should already be filtered out earlier,
          // but this is an extra safety check)
          if (courtSettings.allow_reservation === false) {
            console.log(`Skipping court ${courts[i].id} as reservations are not allowed`);
            continue;
          }
          
          const courtAvailability = JSON.parse(courts[i]?.availability || "[]");
          if (courtAvailability.length > 0) {
            availability_list.push(courtAvailability);
            courtsWithAvailability.push(courts[i]);
          } else {
            courtsWithoutAvailability.push(courts[i]);
          }
        } catch(e) {
          console.log(e, courts[i]);
          courtsWithoutAvailability.push(courts[i]);
        }
      }
      
      // Generate default availability for courts that don't have it defined
      let availabilities = [];
      if (courtsWithoutAvailability.length > 0 && club.opening_time && club.closing_time) {
        console.log(`Using club opening/closing times for ${courtsWithoutAvailability.length} courts without availability`);
        
        // Normalize days off to ensure proper capitalization
        // The club's days off might be stored with different capitalization than what our function expects
        const normalizedDaysOff = Array.isArray(clubDaysOff) ? 
          clubDaysOff.map(day => typeof day === 'string' ? day.charAt(0).toUpperCase() + day.slice(1).toLowerCase() : day) : 
          [];
          
        const defaultAvailability = generateDefaultAvailability(
          club.opening_time, 
          club.closing_time, 
          normalizedDaysOff,
          30 // 30-minute intervals for slots
        );
        
        // Add default availability to the list
        availability_list.push(defaultAvailability);
      }
      
      if (availability_list.length > 0) {
        // Merge all availabilities (custom and default)
        availabilities = mergeAvailabilities(availability_list);
      } else {
        console.log("No availability found for any courts and no club opening/closing times defined");
      }
      
      // Generate slots for next month
      const slots_for_next_month = generateAvailabilityForNextMonth(availabilities);

      // Filter out unavailable time slots based on bookings, club hours, days off, and exceptions
      let filteredAvailabilities = availabilities;
      let filteredSlots = slots_for_next_month;
      
      // Helper function to convert time string to minutes for easier comparison
      const timeToMinutes = (timeStr) => {
        const [hours, minutes, seconds] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
      };
      
      if (date) {
        const dateObj = new Date(date);
        const dayIndex = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.
        
        // Days of week mapping
        const dayMapping = {
          0: 'sunday',
          1: 'monday',
          2: 'tuesday',
          3: 'wednesday',
          4: 'thursday',
          5: 'friday',
          6: 'saturday'
        };
        
        const dayName = dayMapping[dayIndex];
        const dayNameCapitalized = dayName.charAt(0).toUpperCase() + dayName.slice(1);
        
        // Check if the requested date falls on a day off
        if (clubDaysOff.includes(dayNameCapitalized)) {
          // If it's a day off, no time slots are available
          filteredAvailabilities = availabilities.map(dayAvailability => {
            if (dayAvailability.day.toLowerCase() === dayName) {
              return { ...dayAvailability, timeslots: [] };
            }
            return dayAvailability;
          });
          
          // Filter out slots for this date
          filteredSlots = slots_for_next_month.filter(slot => {
            const slotDate = new Date(slot);
            return slotDate.toDateString() !== dateObj.toDateString();
          });
        } else {
          // It's not a day off, so apply other filters
          
          // Check club working hours
          let validTimeRanges = [];
          if (clubTimes && clubTimes.length > 0) {
            validTimeRanges = clubTimes.map(timeRange => ({
              start: timeToMinutes(timeRange.from),
              end: timeToMinutes(timeRange.until)
            }));
          }
          
          // Check exceptions for this day
          let exceptionTimeslots = [];
          if (clubExceptions && clubExceptions.length > 0) {
            // Collect all exception timeslots for this day
            clubExceptions.forEach(exception => {
              if (exception.days) {
                exception.days.forEach(day => {
                  if (day.day.toLowerCase() === dayName && day.timeslots) {
                    exceptionTimeslots = [...exceptionTimeslots, ...day.timeslots];
                  }
                });
              }
            });
          }
          
          // Filter availabilities based on booking conflicts, working hours, and exceptions
          filteredAvailabilities = availabilities.map(dayAvailability => {
            if (dayAvailability.day.toLowerCase() === dayName) {
              // This day matches the requested date's day of week
              const filteredTimeslots = dayAvailability.timeslots.filter(timeslot => {
                const slotMinutes = timeToMinutes(timeslot);
                
                // Check if time is within working hours
                const withinWorkingHours = validTimeRanges.length === 0 || 
                  validTimeRanges.some(range => slotMinutes >= range.start && slotMinutes < range.end);
                
                // Check if time is not in exceptions
                const notInExceptions = !exceptionTimeslots.some(exceptionTime => 
                  timeToMinutes(exceptionTime) === slotMinutes
                );
                
                // Check if not conflicting with bookings
                const notConflictingWithBookings = !unavailableTimeSlots.some(booking => {
                  const bookingStartMinutes = timeToMinutes(booking.start_time);
                  const bookingEndMinutes = timeToMinutes(booking.end_time);
                  return slotMinutes >= bookingStartMinutes && slotMinutes < bookingEndMinutes;
                });
                
                return withinWorkingHours && notInExceptions && notConflictingWithBookings;
              });
              
              return {
                ...dayAvailability,
                timeslots: filteredTimeslots
              };
            }
            return dayAvailability;
          });
          
          // Filter slots for next month based on the same criteria
          filteredSlots = slots_for_next_month.filter(slot => {
            const slotDate = new Date(slot);
            
            // Skip filtering for dates other than the requested date
            if (slotDate.toDateString() !== dateObj.toDateString()) {
              return true;
            }
            
            // Extract time portion
            const slotTime = slotDate.toTimeString().substring(0, 8); // Format: HH:MM:SS
            const slotMinutes = timeToMinutes(slotTime);
            
            // Check if time is within working hours
            const withinWorkingHours = validTimeRanges.length === 0 || 
              validTimeRanges.some(range => slotMinutes >= range.start && slotMinutes < range.end);
            
            // Check if time is not in exceptions
            const notInExceptions = !exceptionTimeslots.some(exceptionTime => 
              timeToMinutes(exceptionTime) === slotMinutes
            );
            
            // Check if not conflicting with bookings
            const notConflictingWithBookings = !unavailableTimeSlots.some(booking => {
              const bookingStartMinutes = timeToMinutes(booking.start_time);
              const bookingEndMinutes = timeToMinutes(booking.end_time);
              return slotMinutes >= bookingStartMinutes && slotMinutes < bookingEndMinutes;
            });
            
            return withinWorkingHours && notInExceptions && notConflictingWithBookings;
          });
        }
      }

      return res.status(200).json({
        error: false,
        availability: mapTimeslotsWithDuration(filteredAvailabilities),
        slots_for_next_month: filteredSlots.sort((a, b) => new Date(a) - new Date(b)),
        unavailableTimeSlots,
        min_booking_time: minBookingTime,
        qualifying_courts: courts.map(court => {
          try {
            const courtSettings = JSON.parse(court.court_settings || '{}');
            return {
              id: court.id,
              name: court.name,
              min_booking_time: courtSettings.min_booking_time || 60,
              allow_reservation: courtSettings.allow_reservation !== false,
              allow_lesson: courtSettings.allow_lesson === true,
              allow_clinic: courtSettings.allow_clinic === true,
              allow_buddy: courtSettings.allow_buddy === true
            };
          } catch(e) {
            return {
              id: court.id,
              name: court.name,
              min_booking_time: 60,
              allow_reservation: true
            };
          }
        })
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Create payment intent
  app.post(`/v3/api/custom/courtmatchup/user/reservations/payment-intent/create`, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { amount } = req.body;
      

      sdk.setTable("user");
      const user = (await sdk.get({ id: req.user_id }))[0];

      let stripe_customer = user.stripe_uid;
      if (!stripe_customer) {
        stripe_customer = (await stripe.createStripeCustomer({ email: user.email })).id;
      }

      // Create payment intent with forced Google Pay and Apple Pay
      const paymentIntent = await stripe.stripe.paymentIntents.create({
        amount: amount * 100,
        currency: "usd",
        customer: stripe_customer,
        setup_future_usage: "off_session",
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never'
        }
      });

      // create ephemeral key
      const ephemeralKey = await stripe.createCustomerEphemeralKey({
        customer: stripe_customer,
        apiVersion: "2023-10-16"
      });

      return res.status(200).json({
        error: false,
        client_secret: paymentIntent.client_secret,
        payment_intent: paymentIntent.id,
        customer_stripe_id: stripe_customer,
        ephemeral_key: ephemeralKey,
      });
    } catch (error) {
      console.log(error);
      return res.status(400).json({ error: true, message: "Something went wrong" });
    }
  });

  // Verify payment status
  app.post(`/v3/api/custom/courtmatchup/user/reservations/payment/verify`, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { status, booking_id, payment_intent=null, last_4=0 } = req.body;

      if(payment_intent){

        const paymentStatus = await stripe.retrievePaymentIntent(payment_intent);
        console.log('paymentStatus :>> ', paymentStatus);
  
        if (paymentStatus.status === "succeeded") {
          sdk.setTable("booking");
          await sdk.update({ status: BOOKING_STATUSES.SUCCESS, payment_intent: payment_intent, last_4: last_4 }, booking_id); // Update booking status to success

          // sdk.setTable("payment_history");
          // await sdk.insert({
          //   booking_id,
          //   amount: paymentStatus.amount / 100,
          //   status: paymentStatus.status,
          //   payment_intent: payment_intent,
          //   user_id: req.user_id,
          // });
  
          return res.status(200).json({ error: false, message: "Payment verified successfully" });
        } else {
          sdk.setTable("booking");
          await sdk.update({ status: BOOKING_STATUSES.FAIL, payment_intent: payment_intent, last_4: last_4 }, booking_id); // Update booking status to failed
          // sdk.setTable("payment_history");
          // await sdk.insert({
          //   booking_id,
          //   amount: paymentStatus.amount / 100,
          //   status: paymentStatus.status,
          //   payment_intent: payment_intent,
          //   user_id: req.user_id,
          // });
          return res.status(400).json({ error: true, message: "Payment verification failed" });
        }
        
      }else{

        sdk.setTable("booking");
        await sdk.update({ status }, booking_id); // Update booking status to success
        // sdk.setTable("payment_history");
        // await sdk.insert({
        //   booking_id,
        //   amount: 0,
        //   status: "succeeded",
        //   payment_intent: null,
        //   user_id: req.user_id,
        // });

        return res.status(200).json({ error: false, message: "Payment verified successfully" });
      }
    } catch (error) {
      console.log(error);
      return res.status(400).json({ error: true, message: "Unable to verify payment" });
    }
  });

  // Get user's billing history/invoices
  app.get(_base + "/billing/invoices", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
  
      // Get query parameters
      const { sort = 'desc', invoice_type } = req.query;
      
      // Validate sort parameter
      if (sort !== 'asc' && sort !== 'desc') {
        throw new Error("Sort parameter must be 'asc' or 'desc'");
      }

      // First get the user's stripe_uid
      sdk.setTable("user");
      const user = await sdk.get({ id: req.user_id });
      if (!user.length) {
        throw new Error("User not found");
      }
      const stripe_uid = user[0].stripe_uid;
  
      // Get one-time payments from bookings
      let bookings_sql = `
        SELECT 
          b.id,
          b.receipt_id,
          b.price as amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.last_4,
          b.court_fee,
          b.payment_intent,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0) AS total_amount
        FROM 
          courtmatchup_booking b
        WHERE 
          b.user_id = ${req.user_id}
          AND b.status = ${BOOKING_STATUSES.SUCCESS}
          AND b.payment_intent IS NOT NULL
      `;
  
      // Add invoice_type filter for bookings if specified
      if (invoice_type) {
        switch(invoice_type.toLowerCase()) {
          case 'court':
            bookings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
            break;
          case 'lesson':
            bookings_sql += ` AND b.lesson_id IS NOT NULL`;
            break;
          case 'clinic':
            bookings_sql += ` AND b.clinic_id IS NOT NULL`;
            break;
        }
      }
  
      // Get subscription payments
      const subscriptions_sql = `
        SELECT 
          s.id,
          s.stripe_id,
          s.stripe_id as receipt_id,
          s.price_id,
          s.status,
          s.create_at,
          s.object,
          'Subscription' as invoice_type
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.user_id = ${req.user_id}
          AND s.object IS NOT NULL
      `;
  
      let bookings = [], subscriptions = [];
      
      // Only fetch relevant data based on invoice_type filter
      if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
        bookings = await sdk.rawQuery(bookings_sql);
      }
      
      if (!invoice_type || invoice_type.toLowerCase() === 'subscription') {
        subscriptions = await sdk.rawQuery(subscriptions_sql);
      }
  
      // Process subscription data to extract amount and other details
      const processed_subscriptions = subscriptions.map(sub => {
        const obj = JSON.parse(sub.object);
        const paymentMethod = obj.default_payment_method;
        let paymentMethodDisplay = 'Unknown Payment Method';
        
        if (paymentMethod && typeof paymentMethod === 'string') {
          // If it's just the ID, show generic "Credit Card"
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethod && paymentMethod.card) {
          // If we have card details, show last 4
          paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
        }

        return {
          id: sub.id,
          stripe_id: sub.stripe_id,
          subscription_id: obj.id,
          price_id: sub.price_id,
          status: sub.status,
          create_at: sub.create_at,
          invoice_type: 'Subscription',
          type: obj.plan?.nickname || 'Subscription Plan',
          plan_name: obj.plan?.product || 'Standard Plan',
          total_amount: obj.plan?.amount || 0,
          currency: obj.currency || 'usd',
          interval: obj.plan?.interval || 'month',
          payment_method: paymentMethodDisplay,
          valid_until: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          current_period_start: obj.current_period_start ? 
            new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
          current_period_end: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          canceled_at: obj.canceled_at ? 
            new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
        };
      });
  
      // Format booking data to match structure
      const processed_bookings = bookings.map(booking => ({
        ...booking,
        currency: 'usd',
        total_amount: parseFloat(booking.amount),
        status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        valid_until: booking.date // For bookings, valid until is the booking date
      }));
  
      // Combine and sort all invoices by create_at date
      const all_invoices = [...processed_bookings, ...processed_subscriptions].sort((a, b) => {
        const dateComparison = new Date(b.create_at) - new Date(a.create_at);
        return sort === 'desc' ? dateComparison : -dateComparison;
      });
  
      // Calculate totals
      const total_spent = all_invoices.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      const active_subscriptions = processed_subscriptions.filter(sub => 
        sub.status === 'active'
      );
  
      // Group invoices by type
      const invoices_by_type = all_invoices.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: all_invoices,
        invoices_by_type,
        total_spent: total_spent / 100, // Convert from cents to dollars
        active_subscriptions: active_subscriptions,
        total_invoices: all_invoices.length,
        filters: {
          sort,
          invoice_type: invoice_type || 'all'
        }
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(_base + "/billing/family-invoices/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      // Get query parameters
      const { id } = req.params;
      const { sort = 'desc', invoice_type } = req.query;
      
      // Validate sort parameter
      if (sort !== 'asc' && sort !== 'desc') {
        throw new Error("Sort parameter must be 'asc' or 'desc'");
      }

      // First get the user's stripe_uid
      sdk.setTable("user");
      // const user = await sdk.get({ id: req.user_id });
      const user = await sdk.get({ guardian: req.user_id, id: id });
      if (!user.length) {
        throw new Error("User not found");
      }

      req.user_id = id;
      const stripe_uid = user[0].stripe_uid;
  
      // Get one-time payments from bookings
      let bookings_sql = `
        SELECT 
          b.id,
          b.receipt_id,
          b.price as amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.last_4,
          b.court_fee,
          b.payment_intent,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0) AS total_amount
        FROM 
          courtmatchup_booking b
        WHERE 
          b.user_id = ${req.user_id}
          AND b.status = ${BOOKING_STATUSES.SUCCESS}
          AND b.payment_intent IS NOT NULL
      `;
  
      // Add invoice_type filter for bookings if specified
      if (invoice_type) {
        switch(invoice_type.toLowerCase()) {
          case 'court':
            bookings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
            break;
          case 'lesson':
            bookings_sql += ` AND b.lesson_id IS NOT NULL`;
            break;
          case 'clinic':
            bookings_sql += ` AND b.clinic_id IS NOT NULL`;
            break;
        }
      }
  
      // Get subscription payments
      const subscriptions_sql = `
        SELECT 
          s.id,
          s.stripe_id,
          s.stripe_id as receipt_id,
          s.price_id,
          s.status,
          s.create_at,
          s.object,
          'Subscription' as invoice_type
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.user_id = ${req.user_id}
          AND s.object IS NOT NULL
      `;
  
      let bookings = [], subscriptions = [];
      
      // Only fetch relevant data based on invoice_type filter
      if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
        bookings = await sdk.rawQuery(bookings_sql);
      }
      
      if (!invoice_type || invoice_type.toLowerCase() === 'subscription') {
        subscriptions = await sdk.rawQuery(subscriptions_sql);
      }
  
      // Process subscription data to extract amount and other details
      const processed_subscriptions = subscriptions.map(sub => {
        const obj = JSON.parse(sub.object);
        const paymentMethod = obj.default_payment_method;
        let paymentMethodDisplay = 'Unknown Payment Method';
        
        if (paymentMethod && typeof paymentMethod === 'string') {
          // If it's just the ID, show generic "Credit Card"
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethod && paymentMethod.card) {
          // If we have card details, show last 4
          paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
        }

        return {
          id: sub.id,
          stripe_id: sub.stripe_id,
          subscription_id: obj.id,
          price_id: sub.price_id,
          status: sub.status,
          create_at: sub.create_at,
          invoice_type: 'Subscription',
          type: obj.plan?.nickname || 'Subscription Plan',
          plan_name: obj.plan?.product || 'Standard Plan',
          total_amount: obj.plan?.amount || 0,
          currency: obj.currency || 'usd',
          interval: obj.plan?.interval || 'month',
          payment_method: paymentMethodDisplay,
          valid_until: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          current_period_start: obj.current_period_start ? 
            new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
          current_period_end: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          canceled_at: obj.canceled_at ? 
            new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
        };
      });
  
      // Format booking data to match structure
      const processed_bookings = bookings.map(booking => ({
        ...booking,
        currency: 'usd',
        total_amount: parseFloat(booking.amount),
        status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        valid_until: booking.date // For bookings, valid until is the booking date
      }));
  
      // Combine and sort all invoices by create_at date
      const all_invoices = [...processed_bookings, ...processed_subscriptions].sort((a, b) => {
        const dateComparison = new Date(b.create_at) - new Date(a.create_at);
        return sort === 'desc' ? dateComparison : -dateComparison;
      });
  
      // Calculate totals
      const total_spent = all_invoices.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      const active_subscriptions = processed_subscriptions.filter(sub => 
        sub.status === 'active'
      );
  
      // Group invoices by type
      const invoices_by_type = all_invoices.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: all_invoices,
        invoices_by_type,
        total_spent: total_spent / 100, // Convert from cents to dollars
        active_subscriptions: active_subscriptions,
        total_invoices: all_invoices.length,
        filters: {
          sort,
          invoice_type: invoice_type || 'all'
        }
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(_base + "/billing/invoices/:invoice_type/:invoice_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        invoice_type,
        invoice_id
      } = req.params;

  
      // Get one-time payments from bookings
      let bookings_sql = `
        SELECT 
          b.id,
          b.price as amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.receipt_id,
          b.court_fee,
          b.last_4,
          b.payment_intent,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0) AS total_amount
        FROM 
          courtmatchup_booking b
        WHERE 
          b.user_id = ${req.user_id}
          AND b.status = ${BOOKING_STATUSES.SUCCESS}
          AND b.payment_intent IS NOT NULL
          AND b.id = ${invoice_id}
      `;
  

      // Get subscription payments
      const subscriptions_sql = `
        SELECT 
          s.id,
          s.stripe_id,
          s.price_id,
          s.status,
          s.create_at,
          s.object,
          'Subscription' as invoice_type
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.user_id = ${req.user_id}
          AND s.object IS NOT NULL
          AND s.id = ${invoice_id}
      `;
  
      let bookings = [], subscriptions = [];
      
      // Only fetch relevant data based on invoice_type filter
      if (invoice_type && invoice_type.toLowerCase() !== 'subscription') {
        bookings = await sdk.rawQuery(bookings_sql);
      }
      
      if (invoice_type && invoice_type.toLowerCase() === 'subscription') {
        subscriptions = await sdk.rawQuery(subscriptions_sql);
      }
  
      // Process subscription data to extract amount and other details
      const processed_subscriptions = subscriptions.map(sub => {
        const obj = JSON.parse(sub.object);
        const paymentMethod = obj.default_payment_method;
        let paymentMethodDisplay = 'Unknown Payment Method';
        
        if (paymentMethod && typeof paymentMethod === 'string') {
          // If it's just the ID, show generic "Credit Card"
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethod && paymentMethod.card) {
          // If we have card details, show last 4
          paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
        }

        return {
          id: sub.id,
          stripe_id: sub.stripe_id,
          subscription_id: obj.id,
          price_id: sub.price_id,
          status: sub.status,
          create_at: sub.create_at,
          invoice_type: 'Subscription',
          type: obj.plan?.nickname || 'Subscription Plan',
          plan_name: obj.plan?.product || 'Standard Plan',
          total_amount: obj.plan?.amount || 0,
          currency: obj.currency || 'usd',
          interval: obj.plan?.interval || 'month',
          payment_method: paymentMethodDisplay,
          valid_until: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          current_period_start: obj.current_period_start ? 
            new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
          current_period_end: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          canceled_at: obj.canceled_at ? 
            new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
        };
      });
  
      // Format booking data to match structure
      const processed_bookings = bookings.map(booking => ({
        ...booking,
        currency: 'usd',
        status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        valid_until: booking.date // For bookings, valid until is the booking date
      }));
  
      // Combine and sort all invoices by create_at date
      const all_invoices = [...processed_bookings, ...processed_subscriptions].sort((a, b) => {
        const dateComparison = new Date(b.create_at) - new Date(a.create_at);
        return sort === 'desc' ? dateComparison : -dateComparison;
      });
  
      // Calculate totals
      const total_spent = all_invoices.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      const active_subscriptions = processed_subscriptions.filter(sub => 
        sub.status === 'active'
      );
  
      // Group invoices by type
      const invoices_by_type = all_invoices.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: all_invoices,
        invoices_by_type,
        total_spent: total_spent / 100,
        active_subscriptions: active_subscriptions,
        total_invoices: all_invoices.length,
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(_base + "/billing/invoices/:reservation_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
        const {
          reservation_id
        } = req.params;
        const {
          invoice_type
        } = req.query;

       sdk.setTable("reservation");
       const reservation = await sdk.get({ id: reservation_id });
       if(!reservation.length){
        throw new Error("Reservation not found");
       }

       const booking_id = reservation[0].booking_id;

       if(!booking_id){
        throw new Error("Booking not found");
       }
       
  
      // Get one-time payments from bookings
      let bookings_sql = `
        SELECT 
          b.id,
          b.price as amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.receipt_id,
          b.payment_intent,
          b.last_4,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0) AS total_amount
        FROM 
          courtmatchup_booking b
        WHERE 
          b.user_id = ${req.user_id}
          AND b.payment_intent IS NOT NULL
          AND b.id = ${booking_id}
      `;
  

  
      let bookings = []
      

      bookings = await sdk.rawQuery(bookings_sql);
  
      // Format booking data to match structure
      const processed_bookings = bookings.map(booking => ({
        ...booking,
        currency: 'usd',
        status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : booking.status === BOOKING_STATUSES.PENDING ? 'pending' : 'failed',
        valid_until: booking.date // For bookings, valid until is the booking date
      }));
  
      // Calculate totals
      const total_spent = processed_bookings.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      // Group invoices by type
      const invoices_by_type = processed_bookings.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: processed_bookings,
        invoices_by_type,
        total_spent: total_spent / 100,
        total_invoices: processed_bookings.length,
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(_base + "/default-payment-method", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

        const {
          payment_method
        } = req.body;

        sdk.setTable("user");
        const user = await sdk.get({ id: req.user_id });
        if(!user.length){
          throw new Error("User not found");
        }

        await sdk.update({ default_payment_method: payment_method }, req.user_id);



  
      return res.status(200).json({
        error: false,
        message: "Default payment method updated successfully",
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(_base + "/default-payment-method", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

        sdk.setTable("user");
        const user = await sdk.get({ id: req.user_id });
        if(!user.length){
          throw new Error("User not found");
        }


        const default_payment_method = user[0].default_payment_method;



  
      return res.status(200).json({
        error: false,
        default_payment_method,
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
  ];
};
