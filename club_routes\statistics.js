const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query, generateAvailabilityForNextMonth, generate_statistics_queries } = require("../utils/util.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "club" }),
];

const base = "/v3/api/custom/courtmatchup/club/statistics";

module.exports = function (app) {
  // get statistics for club
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { start_date, end_date, start_time, end_time } = req.query;

      if (!start_date || !end_date || !start_time || !end_time) {
        throw new Error("Missing required parameters: start_date, end_date, start_time, end_time");
      }

      const { 
        clinicReservations,
        coachReservations,
        totalReservations,
        revenueHeatMap,
        revenueByDateRange,
        revenueByModule,
        buddyStatistics,
        totalExpenses,
        totalRevenue,
        totalProfit,
        courtUtilization,
        lessonReservations,
        revenueByDay
      } = generate_statistics_queries({start_date, end_date, start_time, end_time, filter:{club_id: req.club_id}});

      const results = await Promise.all([
        sdk.rawQuery(clinicReservations),
        sdk.rawQuery(coachReservations),
        sdk.rawQuery(totalReservations),
        sdk.rawQuery(revenueHeatMap),
        sdk.rawQuery(revenueByDateRange),
        sdk.rawQuery(revenueByModule),
        sdk.rawQuery(buddyStatistics),
        sdk.rawQuery(totalExpenses),
        sdk.rawQuery(totalRevenue),
        sdk.rawQuery(totalProfit),
        sdk.rawQuery(courtUtilization),
        sdk.rawQuery(lessonReservations),
        sdk.rawQuery(revenueByDay)
      ]);
      
      // Map the results to an object
      const model = {
        clinicReservations: results[0],
        coachReservations: results[1],
        totalReservations: results[2],
        revenueHeatMap: results[3],
        revenueByDateRange: results[4],
        revenueByModule: results[5],
        buddyStatistics: results[6],
        totalExpenses: results[7],
        totalRevenue: results[8],
        totalProfit: results[9],
        courtUtilization: results[10],
        lessonReservations: results[11],
        revenueByDay: results[12]
      };

      // Format revenueByDay data for the bar chart
      const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      const days = [...new Set(model.revenueByDay.map(item => item.day))].sort(
        (a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b)
      );
      
      const formattedRevenueByDay = {
        days: days,
        data: {
          clinic: days.map(day => {
            const item = model.revenueByDay.find(i => i.day === day && i.module === 'clinic');
            return item ? parseFloat(item.revenue) : 0;
          }),
          coach: days.map(day => {
            const item = model.revenueByDay.find(i => i.day === day && i.module === 'coach');
            return item ? parseFloat(item.revenue) : 0;
          })
        }
      };
      
      model.revenueBarChart = formattedRevenueByDay;

      return res.status(200).json({
        error: false,
        model
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
