[{"id": "_getCoachStatistics", "name": "Get Coach Statistics", "description": "Retrieve various statistics for a coach, including reservations and revenue metrics.", "method": "GET", "route": "/v3/api/custom/courtmatchup/coach/statistics", "inputs": [{"name": "start_date", "dataType": "String", "type": "query", "rules": "optional"}, {"name": "end_date", "dataType": "String", "type": "query", "rules": "optional"}, {"name": "start_time", "dataType": "String", "type": "query", "rules": "optional"}, {"name": "end_time", "dataType": "String", "type": "query", "rules": "optional"}, {"name": "coach_id", "dataType": "Integer", "type": "query", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "valueType": "Object", "parent": ""}], "response_model": [{"clinicReservations": "Array", "coachReservations": "Array", "totalReservations": "Integer", "revenueHeatMap": "Object", "revenueByDateRange": "Object", "revenueByModule": "Object"}], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/coach/statistics', middlewares, async function (req, res) { /* implementation */ });", "doc": "Provides reservation and revenue statistics for a coach within specified date and time ranges.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getCoachBookings", "name": "Get Coach Bookings", "description": "Retrieve bookings associated with a specific coach.", "method": "GET", "route": "/v3/api/custom/courtmatchup/coach/bookings", "inputs": [{"name": "coach_id", "dataType": "Integer", "type": "query", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "list", "key": "list", "valueType": "Array", "parent": ""}], "response_model": [{"list": "Array"}], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/coach/bookings', middlewares, async function (req, res) { /* implementation */ });", "doc": "Retrieve bookings for a coach.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getCoachClinics", "name": "Get Coach Clinics", "description": "Retrieve clinics associated with a specific coach.", "method": "GET", "route": "/v3/api/custom/courtmatchup/coach/clinics", "inputs": [{"name": "coach_id", "dataType": "Integer", "type": "query", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "list", "key": "list", "valueType": "Array", "parent": ""}], "response_model": [{"list": "Array"}], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/coach/clinics', middlewares, async function (req, res) { /* implementation */ });", "doc": "Retrieve clinics for a coach.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getCoachClub", "name": "Get Coach Club Data", "description": "Retrieve club information for a specific club ID.", "method": "POST", "route": "/v3/api/custom/courtmatchup/coach/club/:club_id", "inputs": [{"name": "club_id", "dataType": "Integer", "type": "path", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "valueType": "Object", "parent": ""}], "response_model": [{"model": "Object"}], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/coach/club/:club_id', middlewares, async function (req, res) { /* implementation */ });", "doc": "Retrieve club information by club ID.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getCoachProfile", "name": "Get Coach Profile", "description": "Retrieve the profile information for a coach.", "method": "GET", "route": "/v3/api/custom/courtmatchup/coach/profile", "inputs": [], "response": [{"id": "role", "key": "role", "valueType": "String", "parent": ""}, {"id": "profileObject", "key": "profileObject", "valueType": "Object", "parent": ""}], "response_model": [{"role": "String", "profileObject": "Object"}], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/coach/profile', middlewares, async function (req, res) { /* implementation */ });", "doc": "Retrieve profile information for a coach.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_updateCoachPassword", "name": "Update Coach Password", "description": "Update the password for a coach after validation.", "method": "POST", "route": "/v3/api/custom/courtmatchup/coach/update-password", "inputs": [{"name": "current_password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "valueType": "String", "parent": ""}], "response_model": [{"message": "Updated Password"}], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/coach/update-password', middlewares, async function (req, res) { /* implementation */ });", "doc": "Allows coaches to change their password.", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_coachregister", "name": "Register Coach", "description": "API for registering a coach.", "method": "POST", "route": "/v3/api/custom/courtmatchup/coach/register", "inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "first_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "last_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "photo", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "phone", "dataType": "String", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "text", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/coach/register', middlewares, async (req, res) => { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": [{"id": "workflow1", "name": "API_Route", "configurations": {"General": {"name": "Register Coach", "description": "API for registering a coach.", "route": "/v3/api/custom/courtmatchup/coach/register", "method": "POST", "id": "_coachregister"}, "Authentication": {"protected": true, "authorizedRoles": ["coach"]}, "Api_Input": {"inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "first_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "last_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "photo", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "phone", "dataType": "String", "type": "body", "rules": "optional"}], "response_model": []}}}, {"id": "workflow2", "name": "success_response", "status_code": "200 - OK", "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "text", "valueType": "String", "parent": ""}], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_coachlogin", "name": "Login Coach", "description": "API for logging in a coach.", "method": "POST", "route": "/v3/api/custom/courtmatchup/coach/login", "inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "is_refresh", "dataType": "Boolean", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "text", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/coach/login', middlewares, async (req, res) => { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["coach"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": [{"id": "workflow1", "name": "API_Route", "configurations": {"General": {"name": "Login Coach", "description": "API for logging in a coach.", "route": "/v3/api/custom/courtmatchup/coach/login", "method": "POST", "id": "_coachlogin"}, "Authentication": {"protected": true, "authorizedRoles": ["coach"]}, "Api_Input": {"inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "is_refresh", "dataType": "Boolean", "type": "body", "rules": "optional"}], "response_model": []}}}, {"id": "workflow2", "name": "success_response", "status_code": "200 - OK", "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "text", "valueType": "String", "parent": ""}], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_userregister", "name": "User Registration", "description": "API to register a new user", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/register", "inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "verify", "dataType": "Boolean", "type": "body", "rules": "optional"}, {"name": "is_refresh", "dataType": "Boolean", "type": "body", "rules": "optional"}, {"name": "first_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "last_name", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "photo", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "phone", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "address", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "city", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "state", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "country", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "gender", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "zip_code", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "house_no", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "date_of_birth", "dataType": "String", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "text", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "JWT token", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "Refresh token", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "expiry time", "valueType": "Integer", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "User ID", "valueType": "Integer", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/register', [ ...middlewares ] , async (req, res) => { /* code */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["club", "admin"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": [{"id": "workflow_register", "name": "API_Route", "configurations": {"General": {"name": "User Registration", "description": "API to register a new user", "route": "/v3/api/custom/courtmatchup/user/register", "method": "POST", "id": "_userregister"}, "Authentication": {"protected": true, "authorizedRoles": ["user"]}, "Api_Input": {"inputs": [], "response_model": []}}}, {"id": "workflow_success_response", "name": "success_response", "status_code": "200 - OK", "response": [], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_userlogin", "name": "User Login", "description": "API to log in a user", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/login", "inputs": [{"name": "email", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "password", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "role", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "is_refresh", "dataType": "Boolean", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "user role", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "JWT token", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "Refresh token", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "expiry time", "valueType": "Integer", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "User ID", "valueType": "Integer", "parent": ""}, {"id": "first_name", "key": "first_name", "value": "User's first name", "valueType": "String", "parent": ""}, {"id": "last_name", "key": "last_name", "value": "User's last name", "valueType": "String", "parent": ""}, {"id": "photo", "key": "photo", "value": "User photo URL", "valueType": "String", "parent": ""}, {"id": "two_factor_enabled", "key": "two_factor_enabled", "value": "Two-factor authentication status", "valueType": "Boolean", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/login', [ ...middlewares ] , async (req, res) => { /* code */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": [{"id": "workflow_login", "name": "API_Route", "configurations": {"General": {"name": "User Login", "description": "API to log in a user", "route": "/v3/api/custom/courtmatchup/user/login", "method": "POST", "id": "_userlogin"}, "Authentication": {"protected": true, "authorizedRoles": ["club", "admin"]}, "Api_Input": {"inputs": [], "response_model": []}}}, {"id": "workflow_success_response", "name": "success_response", "status_code": "200 - OK", "response": [], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_view_my_requests", "name": "View My Requests", "description": "Retrieve a list of the user's buddy requests", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/buddy/my-requests", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "list", "key": "list", "value": "array", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.get(base + '/my-requests', middlewares, async function (req, res) {\n    try {\n        let sdk = req.sdk;\n        sdk.getDatabase();\n        sdk.setProjectId(req.projectId);\n\n        const requests = await sdk.join('buddy_request', 'buddy', 'buddy_id', 'id', '*', { user_id: req.user_id });\n\n        return res.status(200).json({\n            error: false,\n            list: requests\n        });\n    } catch (err) {\n        res.status(403).json({\n            error: true,\n            message: err.message\n        });\n    }\n});", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_view_all_requests", "name": "View All Requests", "description": "Retrieve a list of all buddy requests", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/buddy/all-requests", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "list", "key": "list", "value": "array", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.get(base + '/all-requests', middlewares, async function (req, res) {\n    try {\n        let sdk = req.sdk;\n        sdk.getDatabase();\n        sdk.setProjectId(req.projectId);\n\n        const requests = await sdk.join('buddy_request', 'buddy', 'buddy_id', 'id', '*', {});\n\n        return res.status(200).json({\n            error: false,\n            list: requests\n        });\n    } catch (err) {\n        res.status(403).json({\n            error: true,\n            message: err.message\n        });\n    }\n});", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_join_team", "name": "Join Team", "description": "Send a request to join a buddy team", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/buddy/join-team", "inputs": [{"name": "buddy_id", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "ntrp", "dataType": "Float", "type": "body", "rules": "is_required"}, {"name": "player_ids", "dataType": "Array", "type": "body", "rules": "is_required"}, {"name": "num_players", "dataType": "Integer", "type": "body", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Request sent successfully", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post(base + '/join-team', middlewares, async function (req, res) {\n    try {\n        // Relevant business logic here\n\n        return res.status(200).json({\n            error: false,\n            message: 'Request sent successfully'\n        });\n    } catch (err) {\n        res.status(403).json({\n            error: true,\n            message: err.message\n        });\n    }\n});", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_update_request", "name": "Update Request", "description": "Accept or reject a buddy request", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/buddy/update-request", "inputs": [{"name": "request_id", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "status", "dataType": "Integer", "type": "body", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Request accepted successfully", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post(base + '/update-request', middlewares, async function (req, res) {\n    try {\n        // Relevant business logic here\n\n        return res.status(200).json({\n            error: false,\n            message: 'Request accepted successfully'\n        });\n    } catch (err) {\n        res.status(403).json({\n            error: true,\n            message: err.message\n        });\n    }\n});", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_create_request", "name": "Create Request", "description": "Create a new buddy request", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/buddy/create-request", "inputs": [{"name": "sport_id", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "slots", "dataType": "Array", "type": "body", "rules": "optional"}, {"name": "ntrp", "dataType": "Float", "type": "body", "rules": "is_required"}, {"name": "num_players", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "num_needed", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "type", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "need_coach", "dataType": "Boolean", "type": "body", "rules": "optional"}, {"name": "notes", "dataType": "String", "type": "body", "rules": "optional"}, {"name": "date", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "start_time", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "end_time", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "player_ids", "dataType": "Array", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Request created successfully", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post(base + '/create-request', middlewares, async function (req, res) {\n    try {\n        // Relevant business logic here\n\n        return res.status(200).json({\n            error: false,\n            message: 'Request created successfully'\n        });\n    } catch (err) {\n        res.status(403).json({\n            error: true,\n            message: err.message\n        });\n    }\n});", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_bookclinic", "name": "Book Clinic", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/clinics/book-clinic", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Booking created successfully", "valueType": "String", "parent": ""}, {"id": "booking_id", "key": "booking_id", "value": "booking_id", "valueType": "Integer", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/clinics/book-clinic', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getclubdata", "name": "Get Club Data", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/club/:club_id", "inputs": [{"name": "club_id", "dataType": "Integer", "type": "params", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "club[0] || {}", "valueType": "Object", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/club/:club_id', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_createreservation", "name": "Create Reservation", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/reservations", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "reservation_id", "key": "reservation_id", "value": "reservation_id", "valueType": "Integer", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/reservations', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_checkcourtavailability", "name": "Check Court Availability", "description": "", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/reservations/availability/:court_id", "inputs": [{"name": "court_id", "dataType": "Integer", "type": "params", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "availability", "key": "availability", "value": "availability", "valueType": "Array", "parent": ""}, {"id": "slots_for_next_month", "key": "slots_for_next_month", "value": "slots_for_next_month", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/user/reservations/availability/:court_id', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_createpaymentintent", "name": "Create Payment Intent", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/reservations/payment-intent/create", "inputs": [{"name": "amount", "dataType": "Float", "type": "body", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "client_secret", "key": "client_secret", "value": "paymentIntent.client_secret", "valueType": "String", "parent": ""}, {"id": "payment_intent", "key": "payment_intent", "value": "paymentIntent.id", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/reservations/payment-intent/create', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_verifypaymentstatus", "name": "Verify Payment Status", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/reservations/payment/verify", "inputs": [{"name": "status", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "booking_id", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "payment_intent", "dataType": "String", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/reservations/payment/verify', middlewares, async function (req, res) { /* logic */ });", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_book_coach", "name": "Book Coach", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/coach/book-coach", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Booking created successfully", "valueType": "String", "parent": ""}, {"id": "booking_id", "key": "booking_id", "value": "Booking ID", "valueType": "String", "parent": ""}], "response_model": [], "logic": "", "code": "app.post('/v3/api/custom/courtmatchup/user/coach/book-coach', middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "status_code": "200 - OK", "workflows": [{"id": "workflow_1", "name": "API_Route", "configurations": {"General": {"name": "Book Coach", "description": "", "route": "/v3/api/custom/courtmatchup/user/coach/book-coach", "method": "POST", "id": "_book_coach"}, "Authentication": {"protected": true, "authorizedRoles": ["user"]}}}, {"id": "workflow_2", "name": "success_response", "status_code": "200 - OK", "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Booking created successfully", "valueType": "String", "parent": ""}, {"id": "booking_id", "key": "booking_id", "value": "Booking ID", "valueType": "String", "parent": ""}], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_get_availability", "name": "Get Coach Availability", "description": "", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/coach/availability/:coach_id", "inputs": [{"name": "coach_id", "dataType": "Integer", "type": "path", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "availability", "key": "availability", "value": "availability details", "valueType": "Array", "parent": ""}, {"id": "slots_for_next_month", "key": "slots_for_next_month", "value": "Slots for next month", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.get('/v3/api/custom/courtmatchup/user/coach/availability/:coach_id', middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "status_code": "200 - OK", "workflows": [{"id": "workflow_1", "name": "API_Route", "configurations": {"General": {"name": "Get Coach Availability", "description": "", "route": "/v3/api/custom/courtmatchup/user/coach/availability/:coach_id", "method": "GET", "id": "_get_availability"}, "Authentication": {"protected": true, "authorizedRoles": ["user"]}}}, {"id": "workflow_2", "name": "success_response", "status_code": "200 - OK", "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "availability", "key": "availability", "value": "availability details", "valueType": "Array", "parent": ""}, {"id": "slots_for_next_month", "key": "slots_for_next_month", "value": "Slots for next month", "valueType": "Array", "parent": ""}], "columns": {}, "type": "Responses", "response_model": []}]}, {"id": "_searchTimeSlots", "name": "Find Coaches by Time Slots", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/coach/search-time-slots", "inputs": [{"name": "sport_id", "dataType": "Integer", "type": "body", "rules": "is_required"}, {"name": "type", "dataType": "Integer", "type": "body", "rules": "optional"}, {"name": "date", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "start_time", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "end_time", "dataType": "String", "type": "body", "rules": "is_required"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "list", "key": "list", "value": "coaches", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.post(base + '/search-time-slots', middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_createGroup", "name": "Get Club Data", "description": "", "method": "POST", "route": "/v3/api/custom/courtmatchup/user/groups/create", "inputs": [{"name": "name", "dataType": "String", "type": "body", "rules": "is_required"}, {"name": "members", "dataType": "Array", "type": "body", "rules": "optional"}, {"name": "type", "dataType": "Integer", "type": "body", "rules": "optional"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "club[0]", "valueType": "Object", "parent": ""}], "response_model": [], "logic": "", "code": "app.post(base + '/create', middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_getUserGroups", "name": "Get User Groups", "description": "", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/groups", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "groups", "key": "groups", "value": "groups", "valueType": "Array", "parent": ""}], "response_model": [], "logic": "", "code": "app.get(base, middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_joinGroup", "name": "Join Group", "description": "", "method": "GET", "route": "/v3/api/custom/courtmatchup/user/groups/join-group", "inputs": [], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "{}", "valueType": "Object", "parent": ""}], "response_model": [], "logic": "", "code": "app.get(base, middlewares, async function (req, res) { ... })", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["user"], "imports": "", "type": "custom", "group": "custom", "columns": {}, "status_code": "200 - OK", "workflows": []}, {"id": "_checkLambda", "name": "Lambda Check", "description": "", "method": "POST", "route": "/v2/api/lambda/check", "inputs": [{"name": "role", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "OK", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "check"}, {"id": "_twofa<PERSON>ogin", "name": "Two FA Login", "description": "Handles the 2FA login process", "method": "POST", "route": "/v2/api/lambda/2fa/login", "inputs": [{"name": "email", "type": "body", "rules": "required", "dataType": "String"}, {"name": "password", "type": "body", "rules": "required", "dataType": "String"}, {"name": "role", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "qr_code", "key": "qr_code", "value": "", "valueType": "String", "parent": ""}, {"id": "one_time_token", "key": "one_time_token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": 60, "valueType": "Number", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaSignin", "name": "Two FA Signin", "description": "Handles the 2FA signin process", "method": "POST", "route": "/v2/api/lambda/2fa/signin", "inputs": [{"name": "email", "type": "body", "rules": "required", "dataType": "String"}, {"name": "password", "type": "body", "rules": "required", "dataType": "String"}, {"name": "role", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "qr_code", "key": "qr_code", "value": "", "valueType": "String", "parent": ""}, {"id": "access_token", "key": "access_token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaAuthorize", "name": "Two FA Authorize", "description": "Authorizes the user with 2FA", "method": "POST", "route": "/v2/api/lambda/2fa/authorize", "inputs": [{"name": "user_id", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "qr_code", "key": "qr_code", "value": "", "valueType": "String", "parent": ""}, {"id": "type", "key": "type", "value": "qr", "valueType": "String", "parent": ""}, {"id": "access_token", "key": "access_token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaEnable", "name": "Two FA Enable", "description": "Enables 2FA for the user", "method": "POST", "route": "/v2/api/lambda/2fa/enable", "inputs": [{"name": "user_id", "type": "body", "rules": "required", "dataType": "String"}, {"name": "type", "type": "body", "rules": "", "dataType": "String"}, {"name": "phone", "type": "body", "rules": "", "dataType": "String"}, {"name": "token", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "qr_code", "key": "qr_code", "value": "", "valueType": "String", "parent": ""}, {"id": "access_token", "key": "access_token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaDisable", "name": "Two FA Disable", "description": "Disables 2FA for the user", "method": "POST", "route": "/v2/api/lambda/2fa/disable", "inputs": [{"name": "user_id", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaVerify", "name": "Two FA Verify", "description": "Verifies the 2FA token", "method": "POST", "route": "/v2/api/lambda/2fa/verify", "inputs": [{"name": "token", "type": "body", "rules": "required", "dataType": "String"}, {"name": "access_token", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "valid", "key": "valid", "value": "true", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Verified Successfully", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_twofaAuth", "name": "Two FA Auth", "description": "Performs 2FA authentication", "method": "POST", "route": "/v2/api/lambda/2fa/auth", "inputs": [{"name": "code", "type": "body", "rules": "required", "dataType": "String"}, {"name": "token", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "twofa"}, {"id": "_analyticsLog", "name": "Analytics Log", "description": "Endpoint for logging analytics data", "method": "POST", "route": "/v2/api/lambda/analytics/", "inputs": [{"name": "user_id", "type": "body", "rules": "required", "dataType": "String"}, {"name": "session_id", "type": "body", "rules": "", "dataType": "String"}, {"name": "status", "type": "body", "rules": "required", "dataType": "String"}, {"name": "user_agent", "type": "body", "rules": "", "dataType": "String"}, {"name": "application", "type": "body", "rules": "", "dataType": "String"}, {"name": "document", "type": "body", "rules": "", "dataType": "String"}, {"name": "url", "type": "body", "rules": "", "dataType": "Array"}, {"name": "link_clicks", "type": "body", "rules": "", "dataType": "Number"}, {"name": "clicked_buttons", "type": "body", "rules": "", "dataType": "Array"}, {"name": "client_ip", "type": "body", "rules": "", "dataType": "String"}, {"name": "events", "type": "body", "rules": "", "dataType": "Array"}, {"name": "total_time", "type": "body", "rules": "", "dataType": "Number"}, {"name": "data", "type": "body", "rules": "", "dataType": "Object"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Added successfully", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_analyticsEndpoint", "name": "Get Analytics", "description": "Endpoint for getting analytics data", "method": "GET", "route": "/v2/api/lambda/analytics/data", "inputs": [], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "user_analytics", "key": "user_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "guest_analytics", "key": "guest_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "click_analytics", "key": "click_analytics", "value": "", "valueType": "Object", "parent": "model"}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_heatmapAnalytics", "name": "Log Heatmap Analytics", "description": "Endpoint for logging heatmap data", "method": "POST", "route": "/v2/api/lambda/heatmap", "inputs": [{"name": "user_id", "type": "body", "rules": "required", "dataType": "String"}, {"name": "session_id", "type": "body", "rules": "", "dataType": "String"}, {"name": "user_agent", "type": "body", "rules": "", "dataType": "String"}, {"name": "scroll_position", "type": "body", "rules": "required", "dataType": "Object"}, {"name": "coordinates", "type": "body", "rules": "required", "dataType": "Object"}, {"name": "status", "type": "body", "rules": "", "dataType": "String"}, {"name": "client_ip", "type": "body", "rules": "", "dataType": "String"}, {"name": "screen_size", "type": "body", "rules": "", "dataType": "Number"}, {"name": "screen_width", "type": "body", "rules": "", "dataType": "Number"}, {"name": "page", "type": "body", "rules": "", "dataType": "String"}, {"name": "screen_height", "type": "body", "rules": "", "dataType": "Number"}, {"name": "snapshot", "type": "body", "rules": "", "dataType": "String"}, {"name": "total_time", "type": "body", "rules": "", "dataType": "Number"}, {"name": "data", "type": "body", "rules": "", "dataType": "Object"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Added successfully", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_heatmapDataEndpoint", "name": "Get Heatmap Data ", "description": "Endpoint for retrieving heatmap data", "method": "GET", "route": "/v2/api/lambda/heatmap/data", "inputs": [{"name": "custom_date", "type": "query", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "false", "valueType": "Object", "parent": ""}, {"id": "user_analytics", "key": "user_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "guest_analytics", "key": "guest_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "heat_map_data", "key": "heat_map_data", "value": "", "valueType": "Array", "parent": "model"}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_userSessionsDataEndpoint", "name": "User Sessions Data", "description": "Endpoint for retrieving user session data", "method": "GET", "route": "/v2/api/lambda/user-sessions/data", "inputs": [{"name": "custom_date", "type": "query", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "false", "valueType": "Object", "parent": ""}, {"id": "user_analytics", "key": "user_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "guest_analytics", "key": "guest_analytics", "value": "", "valueType": "Object", "parent": "model"}, {"id": "heat_map_data", "key": "heat_map_data", "value": "", "valueType": "Array", "parent": "model"}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_userSessionsAnalytics", "name": "Create User Sessions Analytics", "description": "Endpoint for creating user sessions analytics", "method": "POST", "route": "/v2/api/lambda/analytics/user-sessions/", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer"}, {"name": "session_id", "type": "body", "rules": "", "dataType": "Integer"}, {"name": "status", "type": "body", "rules": "", "dataType": "String"}, {"name": "events", "type": "body", "rules": "", "dataType": "Array"}, {"name": "screen_width", "type": "body", "rules": "", "dataType": "Integer"}, {"name": "screen_height", "type": "body", "rules": "", "dataType": "Integer"}, {"name": "screen_size", "type": "body", "rules": "", "dataType": "String"}, {"name": "start_time", "type": "body", "rules": "", "dataType": "String"}, {"name": "end_time", "type": "body", "rules": "", "dataType": "String"}, {"name": "html_copy", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "User session created successfully", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "analytics"}, {"id": "_appleLoginMobileEndpoint", "name": "Apple Login Mobile Endpoint", "description": "Endpoint for login via iOS app", "method": "POST", "route": "/v2/api/lambda/apple/login/mobile", "inputs": [{"name": "first_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "last_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "identityToken", "type": "body", "rules": "", "dataType": "String"}, {"name": "apple_id", "type": "body", "rules": "", "dataType": "String"}, {"name": "role", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "", "valueType": "String", "parent": ""}, {"id": "access_token", "key": "access_token", "value": "", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "apple_login"}, {"id": "_appleLogin", "name": "Apple Login", "description": "Endpoint for Apple login", "method": "GET", "route": "/v2/api/lambda/apple/login", "inputs": [], "response": [{"id": "error", "key": "error", "value": "true", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "apple_login"}, {"id": "_appleAuthCode", "name": "Apple Auth Code ", "description": "Endpoint for handling Apple authorization code", "method": "POST", "route": "/v2/api/lambda/apple/code", "inputs": [{"id": "state", "name": "state", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "code", "name": "code", "type": "body", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "true", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "apple_login"}, {"id": "_googleCode", "name": "Google Code", "method": "GET", "description": "", "route": "/v2/api/lambda/google/code", "inputs": [{"id": "state", "name": "state", "value": "", "datType": "String", "rules": "optional", "type": "query"}], "response": [], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "google"}, {"id": "_googleCodeMobile", "name": "Google Code Mobile", "method": "GET", "description": "Default function for the mobile app Google login endpoint with code exchange.", "route": "/v2/api/lambda/google/code/mobile", "inputs": [{"id": "role", "name": "role", "type": "query", "rules": "optional", "dataType": "String", "value": "user"}, {"id": "is_refresh", "name": "is_refresh", "type": "query", "rules": "optional", "dataType": "Boolean", "value": "false"}, {"id": "code", "name": "code", "type": "query", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "role", "key": "role", "value": "", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "String", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "Number", "parent": ""}, {"id": "refrsh_token", "key": "refresh_token", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "google_login"}, {"id": "_googleLogin", "name": "Google Login", "method": "GET", "description": "Default function for generating Google login URL for user registration.", "route": "/v2/api/lambda/google/login", "inputs": [{"id": "role", "name": "role", "type": "query", "rules": "required", "dataType": "String", "value": ""}, {"id": "company_id", "name": "company_id", "type": "query", "rules": "optional", "dataType": "String", "value": ""}, {"id": "is_refresh", "name": "is_refresh", "type": "query", "rules": "optional", "dataType": "Boolean", "value": ""}], "response": [], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "google_login"}, {"id": "_blogAll", "name": "Blog All", "description": "Endpoint for retrieving all blog posts", "method": "GET", "route": "/v2/api/lambda/blog/all", "inputs": [{"name": "limit", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "offset", "type": "query", "rules": "", "dataType": "Integer"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Array", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Integer", "parent": ""}, {"id": "offset", "key": "offset", "value": "", "valueType": "Integer", "parent": ""}, {"id": "count", "key": "count", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogSimilar", "name": "Blog Similar", "description": "Endpoint for retrieving similar blog posts", "method": "GET", "route": "/v2/api/lambda/blog/similar/:id", "inputs": [{"name": "top", "type": "query", "rules": "", "dataType": "Integer"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogFilter", "name": "Blog Filter", "description": "Endpoint for filtering blog posts", "method": "GET", "route": "/v2/api/lambda/blog/filter", "inputs": [{"name": "categories", "type": "query", "rules": "", "dataType": "Array"}, {"name": "tags", "type": "query", "rules": "", "dataType": "Array"}, {"name": "rule", "type": "query", "rules": "", "dataType": "String"}, {"name": "search", "type": "query", "rules": "", "dataType": "String"}, {"name": "limit", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "page", "type": "query", "rules": "", "dataType": "Integer"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogCreate", "name": "Blog Create", "description": "Endpoint for creating a new blog post", "method": "POST", "route": "/v2/api/lambda/blog/create", "inputs": [{"name": "title", "type": "body", "rules": "", "dataType": "String"}, {"name": "body", "type": "body", "rules": "", "dataType": "String"}, {"name": "meta", "type": "body", "rules": "", "dataType": "Object"}, {"name": "tags", "type": "body", "rules": "", "dataType": "Array"}, {"name": "categories", "type": "body", "rules": "", "dataType": "Array"}, {"name": "content", "type": "body", "rules": "", "dataType": "String"}, {"name": "description", "type": "body", "rules": "", "dataType": "String"}, {"name": "thumbnail", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "Blog Created.", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogEdit", "name": "Blog Edit", "description": "Endpoint for editing an existing blog post", "method": "POST", "route": "/v2/api/lambda/blog/edit/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}, {"name": "title", "type": "body", "rules": "", "dataType": "String"}, {"name": "content", "type": "body", "rules": "", "dataType": "String"}, {"name": "description", "type": "body", "rules": "", "dataType": "String"}, {"name": "meta", "type": "body", "rules": "", "dataType": "Object"}, {"name": "tags", "type": "body", "rules": "", "dataType": "Array"}, {"name": "categories", "type": "body", "rules": "", "dataType": "Array"}, {"name": "thumbnail", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "Blog Edited.", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogDelete", "name": "Blog Delete", "description": "Endpoint for deleting an existing blog post", "method": "DELETE", "route": "/v2/api/lambda/blog/delete/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "Blog Deleted.", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogSingle", "name": "Blog Single", "description": "Endpoint for retrieving a single blog post", "method": "GET", "route": "/v2/api/lambda/blog/single/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Object", "parent": ""}, {"id": "data_id", "key": "data__id", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_title", "key": "data__title", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_description", "key": "data__description", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_content", "key": "data__content", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_thumbnail", "key": "data__thumbnail", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_author", "key": "data__author", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_meta", "key": "data__meta", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_create_at", "key": "data__create_at", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_update_at", "key": "data__update_at", "value": "", "valueType": "String", "parent": "data"}, {"id": "data_tags", "key": "data__tags", "value": "", "valueType": "Array", "parent": "data"}, {"id": "data_tags_name", "key": "data__tags_name", "value": "", "valueType": "String", "parent": "data_tags"}, {"id": "data_tags_id", "key": "data_tags__id", "value": "", "valueType": "String", "parent": "data_tags"}, {"id": "data_categories", "key": "data__categories", "value": "", "valueType": "Array", "parent": "data"}, {"id": "data_categories_name", "key": "data_categories__name", "value": "", "valueType": "String", "parent": "data_categories"}, {"id": "data_categories_id", "key": "data_categories__id", "value": "", "valueType": "String", "parent": "data_categories"}, {"id": "data_views", "key": "data__views", "value": 0, "valueType": "Number", "parent": "data"}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogTags", "name": "Blog Tags", "description": "Endpoint for retrieving a blog tag or creating if it does not exist ", "method": "POST", "route": "/v2/api/lambda/blog/tags", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "tag_id", "key": "tag_id", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogTagsUpdate", "name": "Blog Tags Update", "description": "Endpoint for updating a blog tag by ID", "method": "POST", "route": "/v2/api/lambda/blog/tags/:id", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Updated!", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogTagsRetrieve", "name": "Blog Tags Retrieve", "description": "Endpoint for retrieving blog tags", "method": "GET", "route": "/v2/api/lambda/blog/tags", "inputs": [{"name": "limit", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "page", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "name", "type": "query", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": [], "valueType": "Array", "parent": ""}, {"id": "limit", "key": "limit", "value": 10, "valueType": "Integer", "parent": ""}, {"id": "page", "key": "page", "value": 1, "valueType": "Integer", "parent": ""}, {"id": "total", "key": "total", "value": 0, "valueType": "Integer", "parent": ""}, {"id": "num_pages", "key": "num_pages", "value": 0, "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogTagsDelete", "name": "Blog Tags Delete by ID", "description": "Endpoint for deleting a blog tag by ID", "method": "DELETE", "route": "/v2/api/lambda/blog/tags/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Tag Deleted", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": ["admin"], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogCategoryCreate", "name": "Create Blog Category", "description": "Endpoint for creating a new blog category", "method": "POST", "route": "/v2/api/lambda/blog/category", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String"}, {"name": "parent_id", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogCategoryUpdate", "name": "Update Blog Category", "description": "Endpoint for updating a blog category", "method": "POST", "route": "/v2/api/lambda/blog/category/:id", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String"}, {"name": "parent_id", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": false, "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "Updated", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogCategoryGet", "name": "Get Blog Category", "description": "Endpoint for retrieving blog categories", "method": "GET", "route": "/v2/api/lambda/blog/category", "inputs": [{"name": "limit", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "page", "type": "query", "rules": "", "dataType": "Integer"}, {"name": "name", "type": "query", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Array", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Integer", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Integer", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Integer", "parent": ""}, {"id": "num_pages", "key": "num_pages", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogSubcategoryGet", "name": "Get Blog Subcategory", "description": "Endpoint for retrieving subcategories of a blog category", "method": "GET", "route": "/v2/api/lambda/blog/subcategory/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_blogDeleteCategory", "name": "Delete Blog Category", "description": "Endpoint for deleting a blog category", "method": "DELETE", "route": "/v2/api/lambda/blog/category/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "data", "key": "data", "value": "Deleted", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "blog"}, {"id": "_captchaTest", "name": "<PERSON><PERSON> <PERSON>", "description": "Endpoint for generating a captcha test", "method": "GET", "route": "/v2/api/lambda/test/:width?/:height?/", "inputs": [{"name": "width", "type": "path", "rules": "", "dataType": "Integer"}, {"name": "height", "type": "path", "rules": "", "dataType": "Integer"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "<img class=\"generated-captcha\" src=\"{image}\">", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "<PERSON><PERSON>a"}, {"id": "_captchaGenerate", "name": "Captcha Generate", "description": "Endpoint for generating a captcha", "method": "GET", "route": "/v2/api/lambda/captcha/:width?/:height?/", "inputs": [{"name": "width", "type": "path", "rules": "", "dataType": "Integer"}, {"name": "height", "type": "path", "rules": "", "dataType": "Integer"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "image", "key": "image", "value": "", "valueType": "String", "parent": "model"}, {"id": "text", "key": "text", "value": "", "valueType": "String", "parent": "model"}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "<PERSON><PERSON>a"}, {"id": "_googleCaptchaVerify", "name": "Google Captcha Verify", "description": "Endpoint for verifying Google reCAPTCHA token", "method": "POST", "route": "/v2/api/lambda/google-captcha/", "inputs": [{"name": "formData", "type": "body", "rules": "", "dataType": "Object"}, {"name": "captchaToken", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "<PERSON><PERSON>a"}, {"id": "_createCmsLambda", "name": "Create CMS Lambda", "method": "POST", "description": "Create new CMS record", "route": "/v2/api/lambda/cms", "inputs": [{"name": "page", "type": "body", "rules": "required", "dataType": "String"}, {"name": "key", "type": "body", "rules": "required", "dataType": "String"}, {"name": "type", "type": "body", "rules": "required", "dataType": "String"}, {"name": "value", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "insertResult", "valueType": "String", "parent": ""}], "code": "app.post(\"/v2/api/lambda/cms\", middlewares, async function (req, res) {\n          try {\n            let client = req.app.get(\"subscriber\");\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            if (!req.body.page) {\n              return res.status(403).json({\n                error: true,\n                message: \"Page Missing\",\n                validation: { page: \"page missing\" },\n              });\n            }\n      \n            if (!req.body.key) {\n              return res.status(403).json({\n                error: true,\n                message: \"Key Missing\",\n                validation: { key: \"key missing\" },\n              });\n            }\n      \n            if (!req.body.type) {\n              return res.status(403).json({\n                error: true,\n                message: \"Type Missing\",\n                validation: { type: \"type missing\" },\n              });\n            }\n      \n            if (!req.body.value) {\n              return res.status(403).json({\n                error: true,\n                message: \"Value Missing\",\n                validation: { value: \"value missing\" },\n              });\n            }\n      \n            const insertResult = await sdk.insert({\n              page: req.body.page,\n              content_type: req.body.type,\n              content_key: req.body.key,\n              content_value: req.body.value,\n              create_at: sqlDateFormat(new Date()),\n              update_at: sqlDateTimeFormat(new Date()),\n            });\n      \n            const getAllResult = await sdk.get({});\n      \n            client.set(\"cms-\" + req.projectId, JSON.stringify(getAllResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            const getAllPageResult = await sdk.get({\n              page: req.body.page,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.body.page, JSON.stringify(getAllPageResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            const getAllPageKeyResult = await sdk.get({\n              page: req.body.page,\n              content_key: req.body.key,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.body.page + \"-\" + req.body.key, JSON.stringify(getAllPageKeyResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            return res.status(200).json({\n              error: false,\n              message: insertResult,\n            });\n          } catch (error) {\n            console.log(error);\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "const TokenMiddleware = require(\"../../../middleware/TokenMiddleware\");", "type": "default", "group": "cms"}, {"id": "_updateCmsLambda", "name": "Update CMS Lambda", "method": "PUT", "description": "Update a CMS record", "route": "/v2/api/lambda/cms/:id", "inputs": [{"name": "id", "type": "path", "rules": "required", "dataType": "String"}, {"name": "page", "type": "body", "rules": "required", "dataType": "String"}, {"name": "key", "type": "body", "rules": "required", "dataType": "String"}, {"name": "type", "type": "body", "rules": "required", "dataType": "String"}, {"name": "value", "type": "body", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "updateResult", "valueType": "String", "parent": ""}], "code": "app.put(\"/v2/api/lambda/cms/:id\", middlewares, async function (req, res) {\n          try {\n            let client = req.app.get(\"subscriber\");\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            if (!req.body.page) {\n              return res.status(403).json({\n                error: true,\n                message: \"Page Missing\",\n                validation: { page: \"page missing\" },\n              });\n            }\n      \n            if (!req.body.key) {\n              return res.status(403).json({\n                error: true,\n                message: \"Key Missing\",\n                validation: { key: \"key missing\" },\n              });\n            }\n      \n            if (!req.body.type) {\n              return res.status(403).json({\n                error: true,\n                message: \"Type Missing\",\n                validation: { type: \"type missing\" },\n              });\n            }\n      \n            if (!req.body.value) {\n              return res.status(403).json({\n                error: true,\n                message: \"Value Missing\",\n                validation: { value: \"value missing\" },\n              });\n            }\n      \n            const updateResult = await sdk.update(\n              {\n                page: req.body.page,\n                content_type: req.body.type,\n                content_key: req.body.key,\n                content_value: req.body.value,\n                update_at: sqlDateTimeFormat(new Date()),\n              },\n              req.params.id\n            );\n      \n            const getAllResult = await sdk.get({});\n      \n            client.set(\"cms-\" + req.projectId, JSON.stringify(getAllResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            const getAllPageResult = await sdk.get({\n              page: req.body.page,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.body.page, JSON.stringify(getAllPageResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            const getAllPageKeyResult = await sdk.get({\n              page: req.body.page,\n              content_key: req.body.key,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.body.page + \"-\" + req.body.key, JSON.stringify(getAllPageKeyResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            return res.status(200).json({\n              error: false,\n              message: updateResult,\n            });\n          } catch (error) {\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_deleteCmsLambda", "name": "Delete CMS Lambda", "method": "DELETE", "description": "Delete a CMS record", "route": "/v2/api/lambda/cms/:id", "inputs": [{"name": "id", "type": "path", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "deleted", "valueType": "String", "parent": ""}], "code": "app.delete(\"/v2/api/lambda/cms/:id\", middlewares, async function (req, res) {\n          try {\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            if (!req.params.id) {\n              return res.status(403).json({\n                error: true,\n                message: \"ID Missing\",\n                validation: { id: \"id missing\" },\n              });\n            }\n      \n            await sdk.delete({}, req.params.id);\n      \n            return res.status(200).json({\n              error: false,\n              message: \"deleted\",\n            });\n          } catch (error) {\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_getCmsByIdLambda", "name": "Get CMS by ID Lambda", "method": "GET", "description": "Get A CMS Record by ID", "route": "/v2/api/lambda/cms/id/:id", "inputs": [{"name": "id", "type": "path", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "model", "value": "result[0]", "valueType": "Object", "parent": ""}], "code": "app.get(\"/v2/api/lambda/cms/id/:id\", publicMiddlewares, async function (req, res) {\n          try {\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            if (!req.params.id) {\n              return res.status(403).json({\n                error: true,\n                message: \"ID Missing\",\n                validation: { id: \"id missing\" },\n              });\n            }\n      \n            const result = await sdk.get({ id: req.params.id });\n      \n            if (result.length > 0) {\n              return res.status(200).json({\n                error: false,\n                model: result[0],\n              });\n            } else {\n              return res.status(200).json({\n                error: false,\n                model: null,\n              });\n            }\n          } catch (error) {\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_getCmsByPageAndKeyLambda", "name": "Get CMS by <PERSON> and Key Lambda", "method": "GET", "description": "Get CMS by Page and Key", "route": "/v2/api/lambda/cms/page/:page/:key", "inputs": [{"name": "page", "type": "path", "rules": "required", "dataType": "String"}, {"name": "key", "type": "path", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}], "code": "app.get(\"/v2/api/lambda/cms/page/:page/:key\", publicMiddlewares, async function (req, res) {\n          try {\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            let client = req.app.get(\"subscriber\");\n            const getall = await client.get(\"cms-\" + req.projectId + \"-\" + req.params.page + \"-\" + req.params.key);\n      \n            if (getall) {\n              return res.status(200).send(getall);\n            }\n      \n            const getAllResult = await sdk.get({\n              page: req.params.page,\n              content_key: req.params.key,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.params.page + \"-\" + req.params.key, JSON.stringify(getAllResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            return res.status(200).send(getAllResult);\n          } catch (error) {\n            console.log(\"ERROR: \", error);\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_getCmsByPageLambda", "name": "Get CMS by <PERSON>", "method": "GET", "description": "Get All CMS records for a particular page.", "route": "/v2/api/lambda/cms/page/:page", "inputs": [{"name": "page", "type": "path", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}], "code": "app.get(\"/v2/api/lambda/cms/page/:page\", publicMiddlewares, async function (req, res) {\n          try {\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            let client = req.app.get(\"subscriber\");\n            const getall = await client.get(\"cms-\" + req.projectId + \"-\" + req.params.page);\n            if (getall) {\n              return res.status(200).send(getall);\n            }\n      \n            const getAllResult = await sdk.get({\n              page: req.params.page,\n            });\n      \n            client.set(\"cms-\" + req.projectId + \"-\" + req.params.page, JSON.stringify(getAllResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            return res.status(200).send(getAllResult);\n          } catch (error) {\n            console.log(\"ERROR: \", error);\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_getAllCmsLambda", "name": "Get All CMS Lambda", "method": "GET", "description": "Get all CMS records", "route": "/v2/api/lambda/cms/all", "inputs": [], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}], "code": "app.get(\"/v2/api/lambda/cms/all\", publicMiddlewares, async function (req, res) {\n          try {\n            let sdk = req.sdk;\n            sdk.setProjectId(req.projectId);\n            sdk.setTable(\"cms\");\n      \n            let client = req.app.get(\"subscriber\");\n            const getall = await client.get(\"cms-\" + req.projectId);\n            if (getall) {\n              return res.status(200).send(getall);\n            }\n      \n            const getAllResult = await sdk.get({});\n      \n            client.set(\"cms-\" + req.projectId, JSON.stringify(getAllResult), \"EX\", 60 * 60, function (data) {\n              console.log(\"SET\", data);\n            });\n      \n            return res.status(200).send(getAllResult);\n          } catch (error) {\n            console.log(\"ERROR: \", error);\n            return res.status(403).json({\n              error: true,\n              message: \"Something went wrong\",\n            });\n          }\n        });", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "cms"}, {"id": "_registerLambda", "name": "Register Lambda", "method": "POST", "description": "", "route": "/v2/api/lambda/register", "inputs": [{"name": "email", "type": "body", "rules": "required", "dataType": "String"}, {"name": "role", "type": "body", "rules": "required", "dataType": "String"}, {"name": "verify", "type": "body", "rules": "", "dataType": "Boolean"}, {"name": "is_refresh", "type": "body", "rules": "", "dataType": "Boolean"}, {"name": "password", "type": "body", "rules": "required", "dataType": "String"}, {"name": "first_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "last_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "photo", "type": "body", "rules": "", "dataType": "String"}, {"name": "phone", "type": "body", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "req.body.role", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "refreshToken", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "Number", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "result", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "const TokenMiddleware = require(\"../../../middleware/TokenMiddleware\");", "type": "default", "group": "register"}, {"id": "_loginLambda", "name": "<PERSON>gin Lambda", "method": "POST", "description": "", "route": "/v2/api/lambda/login", "inputs": [{"name": "email", "type": "body", "rules": "required", "dataType": "String"}, {"name": "password", "type": "body", "rules": "required", "dataType": "String"}, {"name": "role", "type": "body", "rules": "required", "dataType": "String"}, {"name": "is_refresh", "type": "body", "rules": "", "dataType": "Boolean"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "role", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "refreshToken", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "", "valueType": "Number", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "", "valueType": "Int", "parent": ""}, {"id": "first_name", "key": "first_name", "value": "", "valueType": "String", "parent": ""}, {"id": "last_name", "key": "last_name", "value": " ", "valueType": "String", "parent": ""}, {"id": "photo", "key": "photo", "value": "", "valueType": "String", "parent": ""}, {"id": "two_factor_enabled", "key": "two_factor_enabled", "value": "", "valueType": "Boolean", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "login"}, {"id": "_marketingLoginLambda", "name": "Marketing Login Lambda", "method": "POST", "description": "", "route": "/v2/api/lambda/marketing-login", "inputs": [{"name": "email", "type": "body", "rules": "required", "dataType": "String"}, {"name": "password", "type": "body", "rules": "required", "dataType": "String"}, {"name": "role", "type": "body", "rules": "required", "dataType": "String"}, {"name": "is_refresh", "type": "body", "rules": "", "dataType": "Boolean"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "role", "key": "role", "value": "role", "valueType": "String", "parent": ""}, {"id": "token", "key": "token", "value": "JwtService.createAccessToken({ user_id: result.id, role }, config.jwt_expire, config.jwt_key)", "valueType": "String", "parent": ""}, {"id": "refresh_token", "key": "refresh_token", "value": "refreshToken", "valueType": "String", "parent": ""}, {"id": "expire_at", "key": "expire_at", "value": "config.jwt_expire", "valueType": "Number", "parent": ""}, {"id": "user_id", "key": "user_id", "value": "result.id", "valueType": "String", "parent": ""}, {"id": "two_factor_enabled", "key": "two_factor_enabled", "value": "result.two_factor_authentication === 1 ? true : false", "valueType": "Boolean", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "login"}, {"id": "_profile", "name": "profile", "method": "GET", "description": "", "route": "/v2/api/lambda/profile", "inputs": [], "response": [{"id": "id", "key": "id", "value": "result[0].id", "valueType": "String", "parent": ""}, {"id": "first_name", "key": "first_name", "value": "result[0].first_name", "valueType": "String", "parent": ""}, {"id": "email", "key": "email", "value": "result[0].email", "valueType": "String", "parent": ""}, {"id": "role", "key": "role", "value": "result[0].role", "valueType": "String", "parent": ""}, {"id": "last_name", "key": "last_name", "value": "result[0].last_name", "valueType": "String", "parent": ""}, {"id": "phone", "key": "phone", "value": "result[0].phone ?? \"\"", "valueType": "String", "parent": ""}, {"id": "photo", "key": "photo", "value": "result[0].photo ?? \"\"", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "profile"}, {"id": "_profileUpdate", "name": "Profile Update", "method": "POST", "description": "", "route": "/v2/api/lambda/profile", "inputs": [{"name": "first_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "last_name", "type": "body", "rules": "", "dataType": "String"}, {"name": "photo", "type": "body", "rules": "", "dataType": "String", "defaultValue": "null"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "Updated", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "profile"}, {"id": "_uploadImageLocalDefault", "name": "Upload Image Local Default", "method": "POST", "description": "", "route": "/v2/api/lambda/upload", "inputs": [{"name": "file", "type": "multipart", "rules": "", "dataType": "String"}], "response": [{"id": "id", "key": "id", "value": "", "valueType": "String", "parent": ""}, {"id": "url", "key": "url", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "\n          const imageMiddlewares = require(\"../../../middleware/imageMiddlewares\");\n          const sizeOf = require(\"image-size\");\n          const fs = require(\"fs\");\n          const logService = require(\"../../../services/logService\");\n          const { sqlDateFormat, sqlDateTimeFormat } = require(\"../../../utils/dateUtils\");\n        ", "type": "default", "group": "upload"}, {"id": "_uploadImageS3", "name": "UploadImageS3", "method": "POST", "description": "", "route": "/v2/api/lambda/s3/upload", "inputs": [{"name": "file", "type": "multipart", "rules": "", "dataType": "String"}], "response": [{"id": "id", "key": "id", "value": "", "valueType": "String", "parent": ""}, {"id": "url", "key": "url", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "upload"}, {"id": "_preferenceFetch", "name": "Preference Fetch", "method": "GET", "description": "", "route": "/v2/api/lambda/preference", "inputs": [], "response": [{"id": "user_id", "key": "user_id", "value": "", "valueType": "Number", "parent": ""}, {"id": "fcm_token", "key": "fcm_token", "value": "", "valueType": "String", "parent": ""}, {"id": "first_name", "key": "first_name", "value": "", "valueType": "String", "parent": ""}, {"id": "last_name", "key": "last_name", "value": "", "valueType": "String", "parent": ""}, {"id": "email", "key": "email", "value": "", "valueType": "String", "parent": ""}, {"id": "role", "key": "role", "value": "", "valueType": "String", "parent": ""}, {"id": "phone", "key": "phone", "value": "", "valueType": "String", "parent": ""}, {"id": "photo", "key": "photo", "value": "", "valueType": "String", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "preference"}, {"id": "_preferenceUpdate", "name": "Preference Update", "method": "POST", "description": "", "route": "/v2/api/lambda/preference", "inputs": [{"name": "payload", "type": "body", "rules": "required", "dataType": "Object"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "preference"}, {"id": "_treeSow", "name": "Get Sow Tree", "method": "GET", "description": "", "route": "/v4/api/records/sow", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "preference"}, {"id": "_appAlertsList", "name": "App Alerts List", "method": "GET", "description": "", "route": "/v4/api/records/alerts", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "preference"}, {"id": "_appAlertsUpdate", "name": "App Alerts Update", "method": "PUT", "description": "", "route": "/v4/api/records/alerts/:id", "inputs": [{"name": "id", "type": "path", "rules": "required", "dataType": "String"}, {"name": "is_read", "type": "body", "rules": "required", "dataType": "Int"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "preference"}, {"id": "_ecomProductDefault", "name": "Retrieve Product Default", "method": "POST", "description": "Default function for retrieving e-commerce products with pagination.", "route": "/v2/api/lambda/ecom/product/", "inputs": [{"id": "page", "name": "page", "type": "body", "rules": "required", "dataType": "Number", "value": ""}, {"id": "limit", "name": "limit", "type": "body", "rules": "required", "dataType": "Number", "value": ""}, {"id": "sortId", "name": "sortId", "type": "body", "rules": "optional", "dataType": "String", "value": ""}, {"id": "direction", "name": "direction", "type": "body", "rules": "optional", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "num_pages", "key": "num_pages", "value": "", "valueType": "Number", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomProductByIdDefault", "name": "Ecom Product by ID Default", "method": "GET", "description": "Default function for retrieving a specific e-commerce product by ID or slug.", "route": "/v2/api/lambda/ecom/product/:product_identifier", "inputs": [{"id": "product_identifier", "name": "product_identifier", "type": "params", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "model", "key": "message", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomProductAddLambda", "name": "Add Ecom Product Lambda", "method": "POST", "description": "Create Product.", "route": "/v3/api/custom/lambda/ecom/product/add", "inputs": [{"id": "slug", "name": "slug", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "category_id", "name": "category_id", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "type", "name": "type", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "quantity", "name": "quantity", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "data", "name": "data", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "name", "name": "name", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "is_taxable", "name": "is_taxable", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "is_shipping", "name": "is_shipping", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "is_sticky", "name": "is_sticky", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "is_featured", "name": "is_featured", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "is_downloadable", "name": "is_downloadable", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "download_limit", "name": "download_limit", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "is_backorder", "name": "is_backorder", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "sold_single", "name": "sold_single", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "manage_stock", "name": "manage_stock", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "thumbnail_image", "name": "thumbnail_image", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "featured_image", "name": "featured_image", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}, {"id": "image", "name": "image", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "sku", "name": "sku", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "weight", "name": "weight", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "height", "name": "height", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "length", "name": "length", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "weight_unit", "name": "weight_unit", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "height_unit", "name": "height_unit", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "length_unit", "name": "length_unit", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "avg_review", "name": "avg_review", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "sale_price", "name": "sale_price", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "shipping_price", "name": "shipping_price", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "regular_price", "name": "regular_price", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "position", "name": "position", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "download_expire_at", "name": "download_expire_at", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "schedule_sale_at", "name": "schedule_sale_at", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "schedule_sale_end", "name": "schedule_sale_end", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "description", "name": "description", "type": "body", "rules": "", "dataType": "String", "value": ""}, {"id": "is_virtual", "name": "is_virtual", "type": "body", "rules": "", "dataType": "Boolean", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomProductEditLambda", "name": "Edit Ecom Product Lambda", "method": "PUT", "description": "Edit Product.", "route": "/v2/api/lambda/ecom/product/:id", "inputs": [{"id": "id", "name": "id", "type": "path", "rules": "required", "dataType": "Number", "value": ""}, {"id": "payload", "name": "payload", "type": "body", "rules": "required", "dataType": "Object", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomProductDeleteLambda", "name": "Delete Ecom Product Lambda", "method": "DELETE", "description": "Delete Product.", "route": "/v2/api/lambda/ecom/product/:id", "inputs": [{"id": "id", "name": "id", "type": "path", "rules": "required", "dataType": "Number", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_getCartItems", "name": "Get Cart Items", "method": "GET", "description": "Get cart.", "route": "/v2/api/lambda/ecom/cart", "columns": [], "inputs": [{"id": "user_id", "name": "user_id", "type": "query", "rules": "required", "dataType": "Int?", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": [], "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomAddCart", "name": "Ecom Add Cart", "method": "POST", "description": "Add Product to cart.", "route": "/v2/api/lambda/ecom/cart/item", "inputs": [{"id": "user_id", "name": "user_id", "type": "body", "rules": "required", "dataType": "Int?", "value": ""}, {"id": "productId", "name": "productId", "type": "body", "rules": "required", "dataType": "Int", "value": ""}, {"id": "quantity", "name": "quantity", "type": "body", "rules": "required", "dataType": "Int", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomDeleteCartItem", "name": "Ecom delete Cart item", "method": "POST", "description": "Delete Product from cart.", "route": "/v2/api/lambda/ecom/cart/update", "inputs": [{"id": "user_id", "name": "user_id", "type": "query", "rules": "required", "dataType": "Int?", "value": ""}, {"id": "data", "name": "data", "type": "query", "rules": "required", "dataType": [], "value": "Array"}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomGetReviews", "name": "Ecom get product review", "method": "POST", "description": "Get Product Review.", "route": "/v2/api/lambda/ecom/product/review", "inputs": [{"id": "productId", "name": "productId", "type": "query", "rules": "required", "dataType": "Int", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": [], "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_ecomAddReviews", "name": "Ecom add product review", "method": "POST", "description": "Add Product Review.", "route": "/v2/api/lambda/ecom/product/review/add", "inputs": [{"id": "review", "name": "review", "type": "query", "rules": "required", "dataType": "String", "value": ""}, {"id": "productId", "name": "productId", "type": "query", "rules": "required", "dataType": "Int", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "ecom"}, {"id": "_forgotPassword", "name": "Forgot Password", "method": "POST", "description": "", "route": "/v2/api/lambda/forgot", "inputs": [{"id": "email", "name": "email", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "role", "name": "role", "type": "body", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "forgot"}, {"id": "_forgotPasswordMobile", "name": "Forgot Password Mobile", "method": "POSt", "description": "", "route": "/v2/api/lambda/mobile/forgot", "inputs": [{"id": "email", "name": "email", "type": "body", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "forgot"}, {"id": "_resetPassword", "name": "Reset Password", "method": "POST", "description": "", "route": "/v2/api/lambda/reset", "inputs": [{"id": "token", "name": "token", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "code", "name": "code", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "password", "name": "password", "type": "body", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "validation", "key": "validation", "value": "", "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "reset"}, {"id": "_resetPasswordMobile", "name": "Reset Password Mobile", "method": "POST", "description": "", "route": "/v2/api/lambda/mobile/reset", "inputs": [{"id": "code", "name": "code", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "password", "name": "password", "type": "body", "rules": "required", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "validation", "key": "validation", "value": "", "valueType": "Array", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "reset"}, {"id": "_getStripeData", "name": "Get Stripe Data", "method": "POST", "description": "Default function for generating stripe data for checkout.", "route": "/v2/api/lambda/stripe/mobile/intent/", "inputs": [{"id": "user_id", "name": "user_id", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "amount", "name": "amount", "type": "body", "rules": "required", "dataType": "String", "value": ""}, {"id": "currency", "name": "currency", "type": "body", "rules": "optional", "dataType": "String", "value": ""}], "response": [{"id": "error", "key": "error", "value": "false", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "OK", "valueType": "String", "parent": ""}, {"id": "paymentIntent", "key": "paymentIntent", "value": "result[0].paymentIntent ?? ''", "valueType": "String", "parent": ""}, {"id": "ephemeralKeyRaw", "key": "ephemeralKeyRaw", "value": "result[0].ephemeralKeyRaw ?? ''", "valueType": "String", "parent": ""}, {"id": "customer", "key": "customer", "value": "result[0].customer ?? ''", "valueType": "String", "parent": ""}, {"id": "publishableKey", "key": "publishableKey", "value": "result[0].publishableKey ?? ''", "valueType": "String", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": false, "authorizedRoles": [], "imports": "", "type": "default", "group": "stripe"}, {"id": "_projectsOneTree", "name": "Get One Projects", "method": "GET", "description": "", "route": "/v4/api/records/projects/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userOneTree", "name": "Get One User", "method": "GET", "description": "", "route": "/v4/api/records/user/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsOneTree", "name": "Get One Events", "method": "GET", "description": "", "route": "/v4/api/records/events/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionOneTree", "name": "Get One Permission", "method": "GET", "description": "", "route": "/v4/api/records/permission/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomOneTree", "name": "Get One Room", "method": "GET", "description": "", "route": "/v4/api/records/room/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profileOneTree", "name": "Get One Profile", "method": "GET", "description": "", "route": "/v4/api/records/profile/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesOneTree", "name": "Get One Trigger Rules", "method": "GET", "description": "", "route": "/v4/api/records/trigger_rules/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingOneTree", "name": "Get One Custom Scheduling", "method": "GET", "description": "", "route": "/v4/api/records/custom_scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailOneTree", "name": "Get One Email", "method": "GET", "description": "", "route": "/v4/api/records/email/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingOneTree", "name": "Get One Scheduling", "method": "GET", "description": "", "route": "/v4/api/records/scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenOneTree", "name": "Get One Token", "method": "GET", "description": "", "route": "/v4/api/records/token/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogOneTree", "name": "Get One Analytic Log", "method": "GET", "description": "", "route": "/v4/api/records/analytic_log/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentOneTree", "name": "Get One Deployment", "method": "GET", "description": "", "route": "/v4/api/records/deployment/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsOneTree", "name": "Get One User Sessions", "method": "GET", "description": "", "route": "/v4/api/records/user_sessions/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingOneTree", "name": "Get One Booking", "method": "GET", "description": "", "route": "/v4/api/records/booking/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoOneTree", "name": "Get One Photo", "method": "GET", "description": "", "route": "/v4/api/records/photo/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypeOneTree", "name": "Get One Trigger Type", "method": "GET", "description": "", "route": "/v4/api/records/trigger_type/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodeOneTree", "name": "Get One Customcode", "method": "GET", "description": "", "route": "/v4/api/records/customcode/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatOneTree", "name": "Get One Chat", "method": "GET", "description": "", "route": "/v4/api/records/chat/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsOneTree", "name": "Get One Cms", "method": "GET", "description": "", "route": "/v4/api/records/cms/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobOneTree", "name": "Get One Job", "method": "GET", "description": "", "route": "/v4/api/records/job/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "model", "key": "model", "value": "", "valueType": "Object", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}, {"id": "_projectsListTree", "name": "Get Projects List", "method": "GET", "description": "", "route": "/v4/api/records/projects", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userListTree", "name": "Get User List", "method": "GET", "description": "", "route": "/v4/api/records/user", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsListTree", "name": "Get Events List", "method": "GET", "description": "", "route": "/v4/api/records/events", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionListTree", "name": "Get Permission List", "method": "GET", "description": "", "route": "/v4/api/records/permission", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomListTree", "name": "Get Room List", "method": "GET", "description": "", "route": "/v4/api/records/room", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profileListTree", "name": "Get Profile List", "method": "GET", "description": "", "route": "/v4/api/records/profile", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesListTree", "name": "Get Trigger Rules List", "method": "GET", "description": "", "route": "/v4/api/records/trigger_rules", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingListTree", "name": "Get Custom Scheduling List", "method": "GET", "description": "", "route": "/v4/api/records/custom_scheduling", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailListTree", "name": "Get Email List", "method": "GET", "description": "", "route": "/v4/api/records/email", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingListTree", "name": "Get Scheduling List", "method": "GET", "description": "", "route": "/v4/api/records/scheduling", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenListTree", "name": "Get Token List", "method": "GET", "description": "", "route": "/v4/api/records/token", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogListTree", "name": "Get Analytic Log List", "method": "GET", "description": "", "route": "/v4/api/records/analytic_log", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentListTree", "name": "Get Deployment List", "method": "GET", "description": "", "route": "/v4/api/records/deployment", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsListTree", "name": "Get User Sessions List", "method": "GET", "description": "", "route": "/v4/api/records/user_sessions", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingListTree", "name": "Get Booking List", "method": "GET", "description": "", "route": "/v4/api/records/booking", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoListTree", "name": "Get Photo List", "method": "GET", "description": "", "route": "/v4/api/records/photo", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypeListTree", "name": "Get Trigger Type List", "method": "GET", "description": "", "route": "/v4/api/records/trigger_type", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodeListTree", "name": "Get Customcode List", "method": "GET", "description": "", "route": "/v4/api/records/customcode", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatListTree", "name": "Get Chat List", "method": "GET", "description": "", "route": "/v4/api/records/chat", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsListTree", "name": "Get Cms List", "method": "GET", "description": "", "route": "/v4/api/records/cms", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobListTree", "name": "Get Job List", "method": "GET", "description": "", "route": "/v4/api/records/job", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "size", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}, {"id": "_projectsPaginatedTree", "name": "Get Projects Paginated", "method": "GET", "description": "", "route": "/v4/api/records/projects", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userPaginatedTree", "name": "Get User Paginated", "method": "GET", "description": "", "route": "/v4/api/records/user", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsPaginatedTree", "name": "Get Events Paginated", "method": "GET", "description": "", "route": "/v4/api/records/events", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionPaginatedTree", "name": "Get Permission Paginated", "method": "GET", "description": "", "route": "/v4/api/records/permission", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomPaginatedTree", "name": "Get Room Paginated", "method": "GET", "description": "", "route": "/v4/api/records/room", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profilePaginatedTree", "name": "Get Profile Paginated", "method": "GET", "description": "", "route": "/v4/api/records/profile", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesPaginatedTree", "name": "Get Trigger Rules Paginated", "method": "GET", "description": "", "route": "/v4/api/records/trigger_rules", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingPaginatedTree", "name": "Get Custom Scheduling Paginated", "method": "GET", "description": "", "route": "/v4/api/records/custom_scheduling", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailPaginatedTree", "name": "Get Email Paginated", "method": "GET", "description": "", "route": "/v4/api/records/email", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingPaginatedTree", "name": "<PERSON> Scheduling Paginated", "method": "GET", "description": "", "route": "/v4/api/records/scheduling", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenPaginatedTree", "name": "Get Token Paginated", "method": "GET", "description": "", "route": "/v4/api/records/token", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogPaginatedTree", "name": "Get Analytic Log Paginated", "method": "GET", "description": "", "route": "/v4/api/records/analytic_log", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentPaginatedTree", "name": "Get Deployment Paginated", "method": "GET", "description": "", "route": "/v4/api/records/deployment", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsPaginatedTree", "name": "Get User Sessions Paginated", "method": "GET", "description": "", "route": "/v4/api/records/user_sessions", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingPaginatedTree", "name": "Get Booking Paginated", "method": "GET", "description": "", "route": "/v4/api/records/booking", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoPaginatedTree", "name": "Get Photo Paginated", "method": "GET", "description": "", "route": "/v4/api/records/photo", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypePaginatedTree", "name": "Get Trigger Type Paginated", "method": "GET", "description": "", "route": "/v4/api/records/trigger_type", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodePaginatedTree", "name": "Get Customcode Paginated", "method": "GET", "description": "", "route": "/v4/api/records/customcode", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatPaginatedTree", "name": "Get Chat Paginated", "method": "GET", "description": "", "route": "/v4/api/records/chat", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsPaginatedTree", "name": "Get Cms Paginated", "method": "GET", "description": "", "route": "/v4/api/records/cms", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobPaginatedTree", "name": "Get Job Paginated", "method": "GET", "description": "", "route": "/v4/api/records/job", "inputs": [{"name": "order", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "page", "type": "query", "rules": "required", "dataType": "String?"}, {"name": "filter", "type": "query", "rules": "", "dataType": "String?"}, {"name": "join", "type": "query", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "list", "key": "list", "value": "", "valueType": "Array", "parent": ""}, {"id": "page", "key": "page", "value": "", "valueType": "Number", "parent": ""}, {"id": "limit", "key": "limit", "value": "", "valueType": "Number", "parent": ""}, {"id": "total", "key": "total", "value": "", "valueType": "Number", "parent": ""}, {"id": "mapping", "key": "mapping", "value": "", "valueType": "Object", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}, {"id": "_projectsCreateTree", "name": "Create Projects", "method": "POST", "description": "", "route": "/v4/api/records/projects", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "project_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "hostname", "type": "body", "rules": "", "dataType": "String?"}, {"name": "slug", "type": "body", "rules": "", "dataType": "String?"}, {"name": "secret", "type": "body", "rules": "", "dataType": "String?"}, {"name": "configuration", "type": "body", "rules": "", "dataType": "String?"}, {"name": "validation", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userCreateTree", "name": "Create User", "method": "POST", "description": "", "route": "/v4/api/records/user", "inputs": [{"name": "o<PERSON>h", "type": "body", "rules": "", "dataType": "String?"}, {"name": "role", "type": "body", "rules": "", "dataType": "String?"}, {"name": "first_name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "last_name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "email", "type": "body", "rules": "", "dataType": "String?"}, {"name": "password", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "verify", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "phone", "type": "body", "rules": "", "dataType": "String?"}, {"name": "photo", "type": "body", "rules": "", "dataType": "String?"}, {"name": "refer", "type": "body", "rules": "", "dataType": "String?"}, {"name": "stripe_uid", "type": "body", "rules": "", "dataType": "String?"}, {"name": "paypal_uid", "type": "body", "rules": "", "dataType": "String?"}, {"name": "two_factor_authentication", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsCreateTree", "name": "Create Events", "method": "POST", "description": "", "route": "/v4/api/records/events", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionCreateTree", "name": "Create Permission", "method": "POST", "description": "", "route": "/v4/api/records/permission", "inputs": [{"name": "role", "type": "body", "rules": "", "dataType": "String?"}, {"name": "permission", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomCreateTree", "name": "Create Room", "method": "POST", "description": "", "route": "/v4/api/records/room", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "other_user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "chat_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "unread", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "user_update_at", "type": "body", "rules": "", "dataType": "String?"}, {"name": "other_user_update_at", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profileCreateTree", "name": "Create Profile", "method": "POST", "description": "", "route": "/v4/api/records/profile", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "meeting_link", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_pass", "type": "body", "rules": "", "dataType": "String?"}, {"name": "interval", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesCreateTree", "name": "Create Trigger Rules", "method": "POST", "description": "", "route": "/v4/api/records/trigger_rules", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "event_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "trigger_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "condition", "type": "body", "rules": "", "dataType": "String?"}, {"name": "condition_payload", "type": "body", "rules": "", "dataType": "String?"}, {"name": "result_true", "type": "body", "rules": "", "dataType": "String?"}, {"name": "result_payload", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingCreateTree", "name": "Create Custom Scheduling", "method": "POST", "description": "", "route": "/v4/api/records/custom_scheduling", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "date", "type": "body", "rules": "", "dataType": "String?"}, {"name": "slots", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailCreateTree", "name": "Create Email", "method": "POST", "description": "", "route": "/v4/api/records/email", "inputs": [{"name": "slug", "type": "body", "rules": "", "dataType": "String?"}, {"name": "subject", "type": "body", "rules": "", "dataType": "String?"}, {"name": "tag", "type": "body", "rules": "", "dataType": "String?"}, {"name": "html", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingCreateTree", "name": "Create Sc<PERSON>uling", "method": "POST", "description": "", "route": "/v4/api/records/scheduling", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "sat", "type": "body", "rules": "", "dataType": "String?"}, {"name": "sun", "type": "body", "rules": "", "dataType": "String?"}, {"name": "mon", "type": "body", "rules": "", "dataType": "String?"}, {"name": "tue", "type": "body", "rules": "", "dataType": "String?"}, {"name": "wed", "type": "body", "rules": "", "dataType": "String?"}, {"name": "thu", "type": "body", "rules": "", "dataType": "String?"}, {"name": "fri", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenCreateTree", "name": "Create Token", "method": "POST", "description": "", "route": "/v4/api/records/token", "inputs": [{"name": "token", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "data", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "expire_at", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogCreateTree", "name": "Create Analytic Log", "method": "POST", "description": "", "route": "/v4/api/records/analytic_log", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "url", "type": "body", "rules": "", "dataType": "String?"}, {"name": "path", "type": "body", "rules": "", "dataType": "String?"}, {"name": "hostname", "type": "body", "rules": "", "dataType": "String?"}, {"name": "ip", "type": "body", "rules": "", "dataType": "String?"}, {"name": "browser", "type": "body", "rules": "", "dataType": "String?"}, {"name": "country", "type": "body", "rules": "", "dataType": "String?"}, {"name": "role", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentCreateTree", "name": "Create Deployment", "method": "POST", "description": "", "route": "/v4/api/records/deployment", "inputs": [{"name": "project_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "has_fe_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_be_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_android_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_ios_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_domain", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_fe_job", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_be_job", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "domain_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "branch", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "fe_deployed", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "be_deployed", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsCreateTree", "name": "Create User Sessions", "method": "POST", "description": "", "route": "/v4/api/records/user_sessions", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "session_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "screen_size", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "screen_width", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "screen_height", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "events", "type": "body", "rules": "", "dataType": "String?"}, {"name": "html_copy", "type": "body", "rules": "", "dataType": "String?"}, {"name": "client_ip", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_agent", "type": "body", "rules": "", "dataType": "String?"}, {"name": "start_time", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "end_time", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingCreateTree", "name": "Create Booking", "method": "POST", "description": "", "route": "/v4/api/records/booking", "inputs": [{"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "meeting_start", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_end", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_details", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "cancellation_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "cancellation_reason", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoCreateTree", "name": "Create Photo", "method": "POST", "description": "", "route": "/v4/api/records/photo", "inputs": [{"name": "url", "type": "body", "rules": "", "dataType": "String?"}, {"name": "caption", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "width", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "height", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypeCreateTree", "name": "Create Trigger Type", "method": "POST", "description": "", "route": "/v4/api/records/trigger_type", "inputs": [{"name": "name", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodeCreateTree", "name": "Create Customcode", "method": "POST", "description": "", "route": "/v4/api/records/customcode", "inputs": [{"name": "route", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "path", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatCreateTree", "name": "Create Chat", "method": "POST", "description": "", "route": "/v4/api/records/chat", "inputs": [{"name": "room_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "unread", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "chat", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsCreateTree", "name": "Create Cms", "method": "POST", "description": "", "route": "/v4/api/records/cms", "inputs": [{"name": "page", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_key", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_value", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobCreateTree", "name": "Create Job", "method": "POST", "description": "", "route": "/v4/api/records/job", "inputs": [{"name": "task", "type": "body", "rules": "", "dataType": "String?"}, {"name": "arguments", "type": "body", "rules": "", "dataType": "String?"}, {"name": "time_interval", "type": "body", "rules": "", "dataType": "String?"}, {"name": "identifier", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "String?"}, {"name": "last_run", "type": "body", "rules": "", "dataType": "String?"}, {"name": "retries", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "retries_count", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}, {"id": "_projectsUpdateTree", "name": "Update Projects", "method": "PUT", "description": "", "route": "/v4/api/records/projects/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "project_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "hostname", "type": "body", "rules": "", "dataType": "String?"}, {"name": "slug", "type": "body", "rules": "", "dataType": "String?"}, {"name": "secret", "type": "body", "rules": "", "dataType": "String?"}, {"name": "configuration", "type": "body", "rules": "", "dataType": "String?"}, {"name": "validation", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userUpdateTree", "name": "Update User", "method": "PUT", "description": "", "route": "/v4/api/records/user/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "o<PERSON>h", "type": "body", "rules": "", "dataType": "String?"}, {"name": "role", "type": "body", "rules": "", "dataType": "String?"}, {"name": "first_name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "last_name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "email", "type": "body", "rules": "", "dataType": "String?"}, {"name": "password", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "verify", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "phone", "type": "body", "rules": "", "dataType": "String?"}, {"name": "photo", "type": "body", "rules": "", "dataType": "String?"}, {"name": "refer", "type": "body", "rules": "", "dataType": "String?"}, {"name": "stripe_uid", "type": "body", "rules": "", "dataType": "String?"}, {"name": "paypal_uid", "type": "body", "rules": "", "dataType": "String?"}, {"name": "two_factor_authentication", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsUpdateTree", "name": "Update Events", "method": "PUT", "description": "", "route": "/v4/api/records/events/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "name", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionUpdateTree", "name": "Update Permission", "method": "PUT", "description": "", "route": "/v4/api/records/permission/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "role", "type": "body", "rules": "", "dataType": "String?"}, {"name": "permission", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomUpdateTree", "name": "Update Room", "method": "PUT", "description": "", "route": "/v4/api/records/room/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "other_user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "chat_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "unread", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "user_update_at", "type": "body", "rules": "", "dataType": "String?"}, {"name": "other_user_update_at", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profileUpdateTree", "name": "Update Profile", "method": "PUT", "description": "", "route": "/v4/api/records/profile/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "meeting_link", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_pass", "type": "body", "rules": "", "dataType": "String?"}, {"name": "interval", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesUpdateTree", "name": "Update Trigger Rules", "method": "PUT", "description": "", "route": "/v4/api/records/trigger_rules/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "name", "type": "body", "rules": "", "dataType": "String?"}, {"name": "event_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "trigger_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "condition", "type": "body", "rules": "", "dataType": "String?"}, {"name": "condition_payload", "type": "body", "rules": "", "dataType": "String?"}, {"name": "result_true", "type": "body", "rules": "", "dataType": "String?"}, {"name": "result_payload", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingUpdateTree", "name": "Update Custom Scheduling", "method": "PUT", "description": "", "route": "/v4/api/records/custom_scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "date", "type": "body", "rules": "", "dataType": "String?"}, {"name": "slots", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailUpdateTree", "name": "Update Email", "method": "PUT", "description": "", "route": "/v4/api/records/email/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "slug", "type": "body", "rules": "", "dataType": "String?"}, {"name": "subject", "type": "body", "rules": "", "dataType": "String?"}, {"name": "tag", "type": "body", "rules": "", "dataType": "String?"}, {"name": "html", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingUpdateTree", "name": "Update Scheduling", "method": "PUT", "description": "", "route": "/v4/api/records/scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "sat", "type": "body", "rules": "", "dataType": "String?"}, {"name": "sun", "type": "body", "rules": "", "dataType": "String?"}, {"name": "mon", "type": "body", "rules": "", "dataType": "String?"}, {"name": "tue", "type": "body", "rules": "", "dataType": "String?"}, {"name": "wed", "type": "body", "rules": "", "dataType": "String?"}, {"name": "thu", "type": "body", "rules": "", "dataType": "String?"}, {"name": "fri", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenUpdateTree", "name": "Update Token", "method": "PUT", "description": "", "route": "/v4/api/records/token/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "token", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "data", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "expire_at", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogUpdateTree", "name": "Update Analytic Log", "method": "PUT", "description": "", "route": "/v4/api/records/analytic_log/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "url", "type": "body", "rules": "", "dataType": "String?"}, {"name": "path", "type": "body", "rules": "", "dataType": "String?"}, {"name": "hostname", "type": "body", "rules": "", "dataType": "String?"}, {"name": "ip", "type": "body", "rules": "", "dataType": "String?"}, {"name": "browser", "type": "body", "rules": "", "dataType": "String?"}, {"name": "country", "type": "body", "rules": "", "dataType": "String?"}, {"name": "role", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentUpdateTree", "name": "Update Deployment", "method": "PUT", "description": "", "route": "/v4/api/records/deployment/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "project_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "has_fe_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_be_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_android_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_ios_repo", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_domain", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_fe_job", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "has_be_job", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "domain_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "branch", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "fe_deployed", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "be_deployed", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsUpdateTree", "name": "Update User Sessions", "method": "PUT", "description": "", "route": "/v4/api/records/user_sessions/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "session_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "screen_size", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "screen_width", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "screen_height", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "events", "type": "body", "rules": "", "dataType": "String?"}, {"name": "html_copy", "type": "body", "rules": "", "dataType": "String?"}, {"name": "client_ip", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_agent", "type": "body", "rules": "", "dataType": "String?"}, {"name": "start_time", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "end_time", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingUpdateTree", "name": "Update Booking", "method": "PUT", "description": "", "route": "/v4/api/records/booking/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "meeting_start", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_end", "type": "body", "rules": "", "dataType": "String?"}, {"name": "meeting_details", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "cancellation_id", "type": "body", "rules": "", "dataType": "String?"}, {"name": "cancellation_reason", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoUpdateTree", "name": "Update Photo", "method": "PUT", "description": "", "route": "/v4/api/records/photo/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "url", "type": "body", "rules": "", "dataType": "String?"}, {"name": "caption", "type": "body", "rules": "", "dataType": "String?"}, {"name": "user_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "width", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "height", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "type", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypeUpdateTree", "name": "Update Trigger Type", "method": "PUT", "description": "", "route": "/v4/api/records/trigger_type/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "name", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodeUpdateTree", "name": "Update Customcode", "method": "PUT", "description": "", "route": "/v4/api/records/customcode/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "route", "type": "body", "rules": "", "dataType": "String?"}, {"name": "type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "path", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatUpdateTree", "name": "Update Chat", "method": "PUT", "description": "", "route": "/v4/api/records/chat/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "room_id", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "unread", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "chat", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsUpdateTree", "name": "Update Cms", "method": "PUT", "description": "", "route": "/v4/api/records/cms/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "page", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_key", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_type", "type": "body", "rules": "", "dataType": "String?"}, {"name": "content_value", "type": "body", "rules": "", "dataType": "String?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobUpdateTree", "name": "Update Job", "method": "PUT", "description": "", "route": "/v4/api/records/job/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}, {"name": "task", "type": "body", "rules": "", "dataType": "String?"}, {"name": "arguments", "type": "body", "rules": "", "dataType": "String?"}, {"name": "time_interval", "type": "body", "rules": "", "dataType": "String?"}, {"name": "identifier", "type": "body", "rules": "", "dataType": "String?"}, {"name": "status", "type": "body", "rules": "", "dataType": "String?"}, {"name": "last_run", "type": "body", "rules": "", "dataType": "String?"}, {"name": "retries", "type": "body", "rules": "", "dataType": "Integer?"}, {"name": "retries_count", "type": "body", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}, {"id": "_projectsDeleteTree", "name": "Delete Projects", "method": "DELETE", "description": "", "route": "/v4/api/records/projects/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "<PERSON><PERSON><PERSON>", "project_id": "<PERSON><PERSON><PERSON>", "hostname": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>", "configuration": "longtext", "validation": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_userDeleteTree", "name": "Delete User", "method": "DELETE", "description": "", "route": "/v4/api/records/user/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "oauth": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "type": "int", "verify": "int", "phone": "<PERSON><PERSON><PERSON>", "photo": "text", "refer": "<PERSON><PERSON><PERSON>", "stripe_uid": "<PERSON><PERSON><PERSON>", "paypal_uid": "<PERSON><PERSON><PERSON>", "two_factor_authentication": "int", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_eventsDeleteTree", "name": "Delete Events", "method": "DELETE", "description": "", "route": "/v4/api/records/events/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_permissionDeleteTree", "name": "Delete Permission", "method": "DELETE", "description": "", "route": "/v4/api/records/permission/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "role": "<PERSON><PERSON><PERSON>", "permission": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_roomDeleteTree", "name": "Delete Room", "method": "DELETE", "description": "", "route": "/v4/api/records/room/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "other_user_id": "int", "chat_id": "int", "unread": "int", "create_at": "date", "update_at": "datetime", "user_update_at": "datetime", "other_user_update_at": "datetime"}}, {"id": "_profileDeleteTree", "name": "Delete Profile", "method": "DELETE", "description": "", "route": "/v4/api/records/profile/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "create_at": "date", "update_at": "datetime", "meeting_link": "<PERSON><PERSON><PERSON>", "meeting_pass": "<PERSON><PERSON><PERSON>", "interval": "int"}}, {"id": "_triggerRulesDeleteTree", "name": "Delete Trigger Rules", "method": "DELETE", "description": "", "route": "/v4/api/records/trigger_rules/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "event_type": "<PERSON><PERSON><PERSON>", "trigger_type": "<PERSON><PERSON><PERSON>", "condition": "text", "condition_payload": "text", "result_true": "text", "result_payload": "text", "status": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_customSchedulingDeleteTree", "name": "Delete Custom Scheduling", "method": "DELETE", "description": "", "route": "/v4/api/records/custom_scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "date": "date", "slots": "longtext"}}, {"id": "_emailDeleteTree", "name": "Delete Email", "method": "DELETE", "description": "", "route": "/v4/api/records/email/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "slug": "<PERSON><PERSON><PERSON>", "subject": "text", "tag": "text", "html": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_schedulingDeleteTree", "name": "Delete Scheduling", "method": "DELETE", "description": "", "route": "/v4/api/records/scheduling/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "sat": "longtext", "sun": "longtext", "mon": "longtext", "tue": "longtext", "wed": "longtext", "thu": "longtext", "fri": "longtext"}}, {"id": "_tokenDeleteTree", "name": "Delete Token", "method": "DELETE", "description": "", "route": "/v4/api/records/token/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "token": "text", "type": "int", "data": "text", "user_id": "int", "status": "int", "create_at": "date", "update_at": "datetime", "expire_at": "datetime"}}, {"id": "_analyticLogDeleteTree", "name": "Delete Analytic Log", "method": "DELETE", "description": "", "route": "/v4/api/records/analytic_log/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "bigint", "user_id": "int", "url": "text", "path": "text", "hostname": "text", "ip": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "create_at": "date", "update_at": "datetime"}}, {"id": "_deploymentDeleteTree", "name": "Delete Deployment", "method": "DELETE", "description": "", "route": "/v4/api/records/deployment/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "project_id": "<PERSON><PERSON><PERSON>", "has_fe_repo": "int", "has_be_repo": "int", "has_android_repo": "int", "has_ios_repo": "int", "has_domain": "int", "has_fe_job": "int", "has_be_job": "int", "domain_id": "<PERSON><PERSON><PERSON>", "branch": "<PERSON><PERSON><PERSON>", "status": "int", "fe_deployed": "int", "be_deployed": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_userSessionsDeleteTree", "name": "Delete User Sessions", "method": "DELETE", "description": "", "route": "/v4/api/records/user_sessions/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "user_id": "int", "session_id": "<PERSON><PERSON><PERSON>", "screen_size": "int", "screen_width": "int", "screen_height": "int", "events": "longtext", "html_copy": "longtext", "client_ip": "text", "status": "<PERSON><PERSON><PERSON>", "user_agent": "text", "start_time": "bigint", "end_time": "bigint", "create_at": "date", "update_at": "datetime"}}, {"id": "_bookingDeleteTree", "name": "Delete Booking", "method": "DELETE", "description": "", "route": "/v4/api/records/booking/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "user_id": "int", "meeting_start": "datetime", "meeting_end": "datetime", "meeting_details": "longtext", "status": "tinyint", "cancellation_id": "<PERSON><PERSON><PERSON>", "cancellation_reason": "<PERSON><PERSON><PERSON>"}}, {"id": "_photoDeleteTree", "name": "Delete Photo", "method": "DELETE", "description": "", "route": "/v4/api/records/photo/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "url": "text", "caption": "text", "user_id": "int", "width": "int", "height": "int", "type": "int", "create_at": "date", "update_at": "datetime"}}, {"id": "_triggerTypeDeleteTree", "name": "Delete Trigger Type", "method": "DELETE", "description": "", "route": "/v4/api/records/trigger_type/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "name": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_customcodeDeleteTree", "name": "Delete Customcode", "method": "DELETE", "description": "", "route": "/v4/api/records/customcode/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "route": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "path": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_chatDeleteTree", "name": "Delete Chat", "method": "DELETE", "description": "", "route": "/v4/api/records/chat/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "room_id": "int", "unread": "int", "chat": "longtext", "create_at": "date", "update_at": "datetime"}}, {"id": "_cmsDeleteTree", "name": "Delete Cms", "method": "DELETE", "description": "", "route": "/v4/api/records/cms/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "page": "<PERSON><PERSON><PERSON>", "content_key": "<PERSON><PERSON><PERSON>", "content_type": "<PERSON><PERSON><PERSON>", "content_value": "text", "create_at": "date", "update_at": "datetime"}}, {"id": "_jobDeleteTree", "name": "Delete Job", "method": "DELETE", "description": "", "route": "/v4/api/records/job/:id", "inputs": [{"name": "id", "type": "path", "rules": "", "dataType": "Integer?"}], "response": [{"id": "error", "key": "error", "value": "", "valueType": "Boolean", "parent": ""}, {"id": "message", "key": "message", "value": "", "valueType": "String", "parent": ""}, {"id": "data", "key": "data", "value": "", "valueType": "Integer", "parent": ""}], "code": "", "doc": "", "unit": "", "protected": true, "authorizedRoles": [], "imports": "", "type": "default", "group": "treeql", "columns": {"id": "int", "create_at": "date", "update_at": "datetime", "task": "text", "arguments": "<PERSON><PERSON><PERSON>", "time_interval": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "last_run": "datetime", "retries": "int", "retries_count": "int"}}]