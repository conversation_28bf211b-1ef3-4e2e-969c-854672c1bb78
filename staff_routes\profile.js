const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const { validate_account, check_staff_permission } = require("../utils/util");

  const middlewares = [
    ProjectMiddleware,
    UrlMiddleware,
    HostMiddleware,
    TokenMiddleware({
      role: "staff",
    }),
  ];

  const adminMiddlewares = [
    ProjectMiddleware,
    UrlMiddleware,
    HostMiddleware,
    TokenMiddleware({ role: "admin|admin_staff" }),
  ];
module.exports = function (app) {
  // Get staff profile
  app.get("/v3/api/custom/courtmatchup/staff/profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found",
        });
      }
      
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      
      sdk.setTable("staff");
      const profile = await sdk.get({
        user_id: req.user_id,
      });
      
      if (!profile.length) {
        return res.status(403).json({
          error: true,
          message: "Staff profile not found",
        });
      }

      // Get permissions for this staff
      const staffData = profile[0];
      let permissions = {};
      
      if (staffData.permission_level) {
        try {
          permissions = JSON.parse(staffData.permission_level);
        } catch (e) {
          console.error("Error parsing permission_level:", e);
        }
      } else {
        // Check club permissions
        sdk.setTable("club_permissions");
        const clubPermissions = await sdk.get({
          club_id: staffData.club_id,
          role: "staff"
        });
        
        if (clubPermissions.length > 0 && clubPermissions[0].permission) {
          try {
            const permData = JSON.parse(clubPermissions[0].permission);
            permissions = permData.staff || {};
          } catch (e) {
            console.error("Error parsing club permissions:", e);
          }
        }
      }

      // Get club details
      sdk.setTable("clubs");
      const club = await sdk.get({
        id: staffData.club_id
      });

      const profileObject = { ...staffData };
      
      return res.status(200).json({
        role: result[0].role,
        ...profileObject,
        photo: result[0].photo || "",
        email: result[0].email,
        first_name: result[0].first_name,
        last_name: result[0].last_name,
        phone: result[0].phone || "",
        permissions: permissions,
        club: club.length ? club[0] : null
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  // Update staff password
  app.post("/v3/api/custom/courtmatchup/staff/update-password", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found"
        });
      }
      
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }
      
      const hash = result[0].password;
      const passwordValid = await PasswordService.compareHash(req.body.current_password, hash);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Password"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated Password"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Update staff profile
  app.post("/v3/api/custom/courtmatchup/staff/profile-edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found",
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      // Update user info
      sdk.setTable("user");
      const updateResult = await sdk.update(filterEmptyFields({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo || null,
        phone: req.body.phone || null,
        // completed: req.body.completed || null,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.user_id);

      // Get or create staff profile
      sdk.setTable("staff");
      const staff = (await sdk.get({
        user_id: req.user_id,
      }))[0];

      if(!staff) {
        const {
          club_id,
          completed = 0,
          account_details,
          not_paid_through_platform = 0,
          permission_level
        } = req.body;

        if (account_details) validate_account(account_details);
        
        sdk.setTable('staff');
        const staff_id = await sdk.insert(filterEmptyFields({
          user_id: req.user_id,
          club_id,
          not_paid_through_platform,
          completed: completed,
          permission_level: permission_level ? JSON.stringify(permission_level) : null,
          account_details: account_details ? JSON.stringify(account_details) : null,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }));
      } else {
        const {
          club_id,
          account_details,
          not_paid_through_platform,
          permission_level,
          completed = null
        } = req.body;
        
        sdk.setTable('staff');
        await sdk.update(filterEmptyFields({
          club_id,
          completed: completed,
          not_paid_through_platform,
          permission_level: permission_level ? JSON.stringify(permission_level) : null,
          account_details: account_details ? JSON.stringify(account_details) : null,
          update_at: sqlDateTimeFormat(new Date())
        }), staff.id);
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  // Get admin staff profile
  app.get("/v3/api/custom/courtmatchup/admin-staff/profile", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found",
        });
      }
      
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      
      sdk.setTable("admin_staff");
      let profile = await sdk.get({
        user_id: req.user_id,
      });
      
      if (!profile.length) {
        // create staff profile
        sdk.setTable("admin_staff");
        const staff_id = await sdk.insert(filterEmptyFields({
          user_id: req.user_id,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }));
        profile = await sdk.get({
          user_id: req.user_id,
        });
      }

      // Get permissions for this staff
      const staffData = profile[0] || {};

      const profileObject = { ...staffData };
      
      return res.status(200).json({
        role: result[0].role,
        ...profileObject,
        photo: result[0].photo || "",
        email: result[0].email,
        first_name: result[0].first_name,
        last_name: result[0].last_name,
        phone: result[0].phone || "",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  // Update admin staff password
  app.post("/v3/api/custom/courtmatchup/admin-staff/update-password", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found"
        });
      }
      
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }
      
      const hash = result[0].password;
      const passwordValid = await PasswordService.compareHash(req.body.current_password, hash);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Password"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated Password"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Update admin staff profile
  app.post("/v3/api/custom/courtmatchup/admin-staff/profile-edit", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "User not found",
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      // Update user info
      sdk.setTable("user");
      const updateResult = await sdk.update(filterEmptyFields({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo || null,
        phone: req.body.phone || null,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.user_id);

      // Get or create staff profile
      sdk.setTable("admin_staff");
      const staff = (await sdk.get({
        user_id: req.user_id,
      }))[0];

      if(!staff) {
        const {
          completed = 0,
          account_details,
          not_paid_through_platform = 0,
          permission_level
        } = req.body;

        if (account_details) validate_account(account_details);
        
        sdk.setTable('admin_staff');
        const staff_id = await sdk.insert(filterEmptyFields({
          user_id: req.user_id,
          not_paid_through_platform,
          completed: completed,
          permission_level: permission_level ? JSON.stringify(permission_level) : null,
          account_details: account_details ? JSON.stringify(account_details) : null,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }));
      } else {
        const {
          account_details,
          not_paid_through_platform,
          permission_level,
          completed = null
        } = req.body;
        
        sdk.setTable('admin_staff');
        await sdk.update(filterEmptyFields({
          completed: completed,
          not_paid_through_platform,
          permission_level: permission_level ? JSON.stringify(permission_level) : null,
          account_details: account_details ? JSON.stringify(account_details) : null,
          update_at: sqlDateTimeFormat(new Date())
        }), staff.id);
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  
  return [
    {
      method: "GET",
      name: "Staff Profile API",
      url: "/v3/api/custom/courtmatchup/staff/profile",
      successPayload: '{"error":false,"role":"staff","first_name":"John","last_name":"Doe"}',
      needToken: true
    }
  ];
};
