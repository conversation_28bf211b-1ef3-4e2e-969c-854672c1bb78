const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const ValidationService = require("../../../services/ValidationService");
const RateLimitMiddleware = require("../../../middleware/RateLimitMiddleware");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  RateLimitMiddleware,
];

let logService = new DevLogService();

module.exports = function (app) {
  app.post("/v3/api/cedric/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password } = req.body;
      let subscribed = false;
      let free_trial_ended = false;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          // role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          // role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);
      // if(role === "professional"){
      //   //check for existing subscription using customer id
      //   //check if created_at < 24h for free trial end period
      // }
      logService.log(req.projectId, email, password);
      let result = []
      result = await service.login(req.sdk, req.projectId, email, password, "customer");

      if (typeof result == "string") {
        result = await service.login(req.sdk, req.projectId, email, password, "professional");
        if (typeof result == "string") {
          return res.status(403).json({
            error: true,
            message: result
          });
        }
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is inactive"
        });
      }
      if (result.status == 2) {
        return res.status(403).json({
          error: true,
          message: "Your account is Suspend"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            role: result.role
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role: result.role
          },
          config.jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: result.id,
        role: result.role,
        complete_c: result.complete_c,
        complete_p: result.complete_p,
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/cedric/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password, role } = req.body;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);

      logService.log(req.projectId, email, password);
      const result = await service.login(req.sdk, req.projectId, email, password, role);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            role: role
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        role,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role
          },
          config.jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: result.id,
        first_name: result.first_name ?? "",
        last_name: result.last_name ?? "",
        complete: result.complete === 0 ? false : true,
        photo: result.photo ?? "",
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Regular Login API",
      url: "/v2/api/lambda/login",
      successBody: '{ "email": "<EMAIL>",    "password": "123456",    "role": "admin", "is_refresh": false}',
      successPayload: '{"error": false,"role": "admin","token": "token","refresh_token": "only if is_refresh true","expire_at": 60,"user_id": 1}',
      errors: [
        {
          name: "403",
          body: '{"password": "123456", "role": "admin"}',
          response: '{"error": true,"message": "Invalid Credentials","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>",  "role": "admin"}',
          response: '{"error": true,"message": "Invalid Credentials","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>", "password": "123456"}',
          response: '{"error": true,"message": "Invalid Credentials","validation": [{ "field": "role", "message": "Role missing" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>", "password": "123456",    "role": "admin"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    }
  ];
};
