const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/data";

module.exports = function (app) {

  app.get(base + "/family-members", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      console.log(req.user_id,'<<<>>>>>>>>');

      // Get all family groups where user is either owner or member
      const sql = `
        SELECT 
         first_name,
         last_name,
         email,
         photo,
         guardian,
         family_role
      FROM 
        courtmatchup_user u
      WHERE 
          (
              u.guardian = ${req.user_id}
          )
      `;

      let groups = await sdk.rawQuery(sql);
      

      return res.status(200).json({
        error: false,
        family_members: groups
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  // POST /v3/api/custom/courtmatchup/user/groups/switch-family-member { family_member_id }
  // GET /v3/api/custom/courtmatchup/user/groups/switch-back
  // GET /v3/api/custom/courtmatchup/user/groups/family-members

  app.get(base + "/switch-back", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      // Get the original user ID from the token
      const originalUserId = req.original_user_id;
      
      if (!originalUserId) {
        throw new Error("Not in family member switch mode");
      }

      // Get the original user's information
      sdk.setTable("user");
      const originalUser = await sdk.get({
        id: originalUserId
      });

      if (!originalUser.length) {
        throw new Error("Original user not found");
      }

      // Generate a token for the original user
      const token = JwtService.createAccessToken(
        {
          user_id: originalUserId,
          role: "user",
          family_role: originalUser[0].family_role
        },
        config.jwt_expire,
        config.jwt_key
      );

      return res.status(200).json({
        error: false,
        token,
        user: {
          id: originalUser[0].id,
          first_name: originalUser[0].first_name,
          last_name: originalUser[0].last_name,
          email: originalUser[0].email,
          family_role: originalUser[0].family_role
        }
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
