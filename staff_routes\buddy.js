const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const ValidationService = require("../../../services/ValidationService.js");
const PaginationService = require("../../../services/PaginationService.js");
const { saveCount } = require("../services/logs");
const { emitEvent, getCount } = require("../services/EmitEventService");
const { sendNotification } = require("../services/PushNotificaionService");
const MkdEventService = require("../services/MkdEventService");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const { ERROR_MESSAGES } = require("../utils/constants.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/buddy";

module.exports = function (app) {
  // View my requests
  app.get(base + "/my-requests", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const requests = await sdk.join("buddy_request", "buddy", "buddy_id", "id", "*", { user_id: req.user_id });

      
      return res.status(200).json({
        error: false,
        list: requests
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // View all requests
  app.get(base + "/all-requests", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const requests = await sdk.join("buddy_request", "buddy", "buddy_id", "id", "*", {});
      
      return res.status(200).json({
        error: false,
        list: requests
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // join request
  app.post(base + "/join-team", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { buddy_id, ntrp, player_ids=[], num_players } = req.body;

      if (!ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);

      if (!buddy_id) throw new Error(ERROR_MESSAGES.BUDDY_ID_NOT_FOUND);

      sdk.setTable("buddy");
      const buddy = (await sdk.get({ id: buddy_id }))[0];
      if (!buddy) throw new Error(ERROR_MESSAGES.BUDDY_NOT_FOUND);
      const {num_needed, ntrp: buddy_ntrp} = buddy;

      if (num_players > num_needed) throw new Error(ERROR_MESSAGES.NUM_PLAYERS_EXCEEDED);

      if (player_ids.length < num_players) throw new Error(ERROR_MESSAGES.PLAYER_IDS_NOT_FOUND);

      // check if player has joined before
      sdk.setTable("buddy_request");
      for (let i = 0; i < player_ids.length; i++) {
        const player_id = player_ids[i];
        const player = (await sdk.get({ user_id: player_id, buddy_id }))[0];
        if (!player) throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
        if (player.buddy_id) throw new Error(ERROR_MESSAGES.PLAYER_ALREADY_JOINED);
      }


      sdk.setTable("buddy_request");
      await sdk.insert({
        user_id: req.user_id,
        other_ids: JSON.stringify(player_ids),
        buddy_id: buddy_id,
        ntrp,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });
      
      return res.status(200).json({
        error: false,
        message: "Request sent successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // accept or reject request
  app.post(base + "/update-request", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { request_id, status } = req.body;

      if (!request_id) throw new Error(ERROR_MESSAGES.REQUEST_ID_NOT_FOUND);

      if (!status) throw new Error(ERROR_MESSAGES.STATUS_NOT_FOUND);

      sdk.setTable("buddy_request");
      const request = (await sdk.get({ id: request_id, status:0 }))[0];
      if (!request) throw new Error(ERROR_MESSAGES.REQUEST_NOT_FOUND);
      const new_ids = JSON.parse(request.other_ids) || [];

      if (status === 2) {
        await sdk.update({
          status,
          update_at: sqlDateTimeFormat(new Date()),
        }, request_id);
        return res.status(200).json({
          error: false,
          message: "Request rejected successfully"
        })
      }

      // update buddy group - check if reservation exists - update reservation team and booking ids

      sdk.setTable("buddy");
      const buddy = (await sdk.get({ id: request.buddy_id }))[0];
      if (!buddy) throw new Error(ERROR_MESSAGES.BUDDY_NOT_FOUND);

      const reservation_id = buddy.reservation_id;
      const old_ids = JSON.parse(buddy.player_ids) || [];
      const player_ids = old_ids.concat(new_ids);
      if (reservation_id) {
        sdk.setTable("reservation");
        const reservation = (await sdk.get({ id: reservation_id }))[0];
        if (!reservation) throw new Error(ERROR_MESSAGES.RESERVATION_NOT_FOUND);
        const booking_id = reservation.booking_id;
        if (!booking_id) throw new Error(ERROR_MESSAGES.BOOKING_ID_NOT_FOUND);
        sdk.setTable("booking");
        const booking = (await sdk.get({ id: booking_id }))[0];
        if (!booking) throw new Error(ERROR_MESSAGES.BOOKING_NOT_FOUND);
        const old_booking_ids = JSON.parse(booking.player_ids) || [];
        const new_booking_ids = old_booking_ids.concat(new_ids);;
        await sdk.updateWhere({
          player_ids: JSON.stringify(new_booking_ids),
          update_at: sqlDateTimeFormat(new Date()),
        }, { id: booking_id });

        for (let i = 0; i < new_ids.length; i++) {
          const player_id = new_ids[i];
          sdk.setTable("reservation_team")
          await sdk.insert({
            booking_id,
            reservation_id,
            user_id: player_id,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date()),
          });
        }
      }

      const {num_needed, ntrp: buddy_ntrp} = buddy;
      await sdk.updateWhere({
        num_needed: num_needed - 1,
        ntrp: buddy_ntrp,
        update_at: sqlDateTimeFormat(new Date()),
      }, {
        id: request.buddy_id
      })

      

      sdk.setTable("buddy_request");
      await sdk.updateWhere({
        status,
        update_at: sqlDateTimeFormat(new Date()),
      }, { id: request_id });

      
      return res.status(200).json({
        error: false,
        message: "Request accepted successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Create request
  app.post(base + "/create-request",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, slots, ntrp, num_players, num_needed, type=0, need_coach = 0, notes = "", date, start_time, end_time, player_ids=[] } = req.body;

      if (!ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!num_players) throw new Error(ERROR_MESSAGES.NUM_PLAYERS_NOT_FOUND);
      if (!num_needed) throw new Error(ERROR_MESSAGES.NUM_NEEDED_NOT_FOUND);
      if (!type) throw new Error(ERROR_MESSAGES.TYPE_NOT_FOUND);
      if(!slots && !slots.length) {
        if (!start_time) throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND);
        if (!end_time) throw new Error(ERROR_MESSAGES.END_TIME_NOT_FOUND);
      }else if (slots && !slots.length) throw new Error(ERROR_MESSAGES.SLOTS_NOT_FOUND)
      else {
        throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND);
      }
      if (!date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);

      let total = 0;

      if (start_time && end_time){
        const [startHours, startMinutes] = start_time.split(':').map(Number);
        const [endHours, endMinutes] = end_time.split(':').map(Number);
        const duration = (endHours - startHours) * 60 + (endMinutes - startMinutes);
        const hour_duration = duration / 60;
        total += hour_duration;
      }
      // slots = [{
      //   start_time: "11:10",
      //   end_time: "13:20"
      // }]

      if (slots && slots.length) {
        for (let i = 0; i < slots.length; i++) {
          const slot = slots[i];
          const {start_time, end_time} = slot;
          const [startHours, startMinutes] = start_time.split(':').map(Number);
          const [endHours, endMinutes] = end_time.split(':').map(Number);
          const duration = (endHours - startHours) * 60 + (endMinutes - startMinutes);
          const hour_duration = duration / 60;
          total += hour_duration;
        }
      }

      if (await reservation_hours_left(sdk, req.user.id) < total) throw new Error(ERROR_MESSAGES.WEEKLY_RESERVATION_LIMIT_REACHED);
      // await log_reservation(sdk, user_id, reservation_id);
      
      sdk.setTable("buddy");
      const buddy_id = await sdk.insert({
        sport_id,
        type,
        ntrp,
        // reservation_id,
        num_players,
        num_needed,
        slots: JSON.stringify(slots) || "[]",
        notes,
        need_coach,
        date: sqlDateFormat(new Date(date)),
        player_ids: JSON.stringify(player_ids),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      
      return res.status(200).json({
        error: false,
        message: "Request created successfully",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // subscribe to date range
  app.post(base + "/subscribe",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, min_ntrp, max_ntrp, days ,type=0, need_coach = 0, notes = "", start_date, end_date, start_time, end_time } = req.body;

      if (!sport_id) throw new Error(ERROR_MESSAGES.SPORT_ID_NOT_FOUND);
      if (!start_date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);
      if (!end_date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);
      if (!min_ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!max_ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!type) throw new Error(ERROR_MESSAGES.TYPE_NOT_FOUND);
      if (!start_time) throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND);
      if (!end_time) throw new Error(ERROR_MESSAGES.END_TIME_NOT_FOUND);
      
      sdk.setTable("buddy_subscription");
      const subscription_id = await sdk.insert({
        sport_id,
        user_id: req.user_id,
        type,
        min_ntrp,
        max_ntrp,
        days,
        start_date: sqlDateFormat(new Date(start_date)),
        end_date: sqlDateFormat(new Date(end_date)),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      
      return res.status(200).json({
        error: false,
        message: "Subscribed to date range",
        subscription_id
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/view-subscriptions",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      sdk.setTable("buddy_subscription");
      const subscriptions = await sdk.get({
        user_id: req.user_id
      });

      
      return res.status(200).json({
        error: false,
        list: subscriptions
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });



  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
