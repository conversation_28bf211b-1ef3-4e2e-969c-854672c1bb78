const AuthService = require("../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const _config = require("../utils/config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const jwt = require("jsonwebtoken");
const appleSignin = require("apple-signin-auth");
const PasswordService = require("../../../services/PasswordService");
const ValidationService = require("../../../services/ValidationService");
const NodeGoogleLogin = require("node-google-login");
const BackendSDK = require("../../../core/BackendSDK");
const {
  sqlDateFormat,
  sqlDateTimeFormat,
} = require("../../../services/UtilService");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const MkdEventService = require("../../../services/MkdEventService");
const SyncStripeWebhook = require("../../../middleware/SyncStripeWebhook");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware
];

const _middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  SyncStripeWebhook
];

let logService = new DevLogService();

module.exports = function (app) {
  const stripe = new StripeService();
  function jsonExtractor(object, property) {
    return `json_unquote(json_extract(${object}, '$.${property}'))`;
  }
  app.post("/v3/api/custom/courtmatchup/users/register", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      let verify = req.body.verify ? req.body.verify : true;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;
      if (!req.body.email) {
        return res.status(403).json({
          error: true,
          message: "Email Missing",
          validation: [{ field: "email", message: "Email missing" }]
        });
      }
      if (!req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Role Missing",
          validation: [{ field: "role", message: "Role missing" }]
        });
      }
      if (req.body.role === "admin") {
        verify = true;
        const userData = JwtService.verifyAccessToken(JwtService.getToken(req), config.jwt_key);
        if (!userData || userData.role != "admin") {
          return res.status(403).json({
            error: true,
            message: "Admin can't be registered using this API",
            validation: [{ field: "role", message: "Role (admin) is not allowed" }]
          });
        }
      }
      if (!req.body.password && !req.body.password_login === 0) {
        return res.status(403).json({
          error: true,
          message: "Password Missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }
      let password = req.body.password;
      let email = req.body.email;

      if(req.body.password_login === 0){
        password = "a123456"
      }

      sdk.setTable("clubs");
      const club = await sdk.get({
        id: req.body.club_id
      })

      if (!club[0]) {
        return res.status(403).json({
          error: true,
          message: "Invalid Club",
          validation: [{ field: "club_id", message: "Invalid Club" }]
        });
      }

      let service = new AuthService();
      if (req.body.phone) {
        sdk.setTable("user");
        const user = await sdk.get({
          phone: req.body.phone
        });
        if (user.length > 0) {
          return res.status(403).json({
            error: true,
            message: "Phone number already in use",
            validation: [{ field: "phone", message: "Phone number already in use" }]
          });
        }
      }

      sdk.setTable("user")
      const user = await sdk.get({
        first_name: req.body.first_name,
        last_name: req.body.last_name
      })

      if (user.length > 0) {
        return res.status(403).json({
          error: true,
          message: "First and last name combination is already taken",
          validation: [{ field: "first_name", message: "First and last name combination already taken" }]
        });
      }

      logService.log(req.projectId, req.body.email, password, req.body.role);
      const body = { ...req.body };
      const result = await service.register(
        sdk,
        req.projectId,
        email,
        password,
        req.body.role,
        verify,
        req.body.first_name,
        // req.body.username,
        req.body.last_name,
        req.body.photo,
        req.body.phone ?? null
      );

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      } else {
        const accepted = [
          "password",
          "email",
          "password",
          "role",
          "verify",
          "first_name",
          "username",
          "family_role",
          "guardian",
          "last_name",
          "photo",
          "phone",
          "verify",
          "address",
          "city",
          "state",
          "country",
          "gender",
          "password_login",
          "zip_code",
          "ntrp",
          "house_no",
          "date_of_birth",
          "is_refresh"
        ]

        accepted.forEach((field) => {
          if (body[field]) delete body[field];
        });

        const sdk = req.sdk
        sdk.setProjectId(req.projectId);
        sdk.setTable('profile');
        await sdk.updateWhere(filterEmptyFields({
          address: req.body.address,
          city: req.body.city,
          state: req.body.state,
          country: req.body.country,
          gender: req.body.gender,
          zip_code: req.body.zip_code,
          ntrp: req.body.ntrp,
          house_no: req.body.house_no,
          date_of_birth: req.body.date_of_birth,
          fields: JSON.stringify(body || {}),
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }), {
          user_id:result
        })
        sdk.setTable('user');
        await sdk.update(filterEmptyFields({
          bio: req.body.bio,
          club_id: req.body.club_id,
          password_login: req.body.password_login,
          guardian: req.body.guardian,
          family_role: req.body.family_role,
          alternative_phone: req.body.alternative_phone,
          age_group: req.body.age_group,
          family_role: req.body.family_role,
          update_at: sqlDateTimeFormat(new Date())
        }), result)
        if (needRefreshToken) {
          refreshToken = JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role
            },
            3600 * 24 * 30,
            config.jwt_key
          );
          let expireDate = new Date();
          expireDate.setSeconds(expireDate.getSeconds() + (3600 * 24 * 30));
          await service.saveRefreshToken(req.sdk, req.projectId, result, refreshToken, expireDate);
        }

        if (req.body.password_login === 1){ 

          const activationToken = JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role,
              club_id: req.body.club_id,
              activation_token: true
            },
            config.jwt_expire,
            config.jwt_key
          );

          
          const MKDSDK = new ManaKnightSDK();
          let project = {};
    
          if (config.env == "production") {
            project = require("../../../project");
          } else {
            MKDSDK.setProjectId("manaknight");
            MKDSDK.setTable("projects");
    
            project = (
              await MKDSDK.get({
                project_id: req.projectId
              })
            )[0];
          }
    
          const eventService = new MkdEventService(sdk, req.projectId, req.headers);
          const mailResult = await eventService.sendMail(
            {
              email: config.mail_user,
              to: req.body.email,
              from: config.from_mail,
              link: `https://${project.hostname}/${req.body.role}/verify-email?token=${activationToken}`
            },
            "signup-verification"
          );
        }

        // check if email exists in group invites
        sdk.setTable("group_invite");
        const pendingInvites = await sdk.rawQuery(`
          SELECT * FROM courtmatchup_group_invite 
          WHERE email = '${req.body.email}' 
          AND status = 'pending'
        `);

        if (pendingInvites.length > 0) {
          // Update all pending invites with the new user_id
          for (const invite of pendingInvites) {
            await sdk.update({
              user_id: result, // result is the new user's ID
              update_at: sqlDateTimeFormat(new Date())
            }, invite.id);
            // get the group creator 
            // sdk.setTable("user_groups");
            // const group = await sdk.get({id: invite.group_id});
            // if(group.length > 0) {
            //   const creator = group[0].user_id;
            //   sdk.setTable("user");
            //   await sdk.update({
            //     guardian: creator,
            //     update_at: sqlDateTimeFormat(new Date())
            //   }, result);
            // }


            // If auto-accept is desired, you can automatically accept the invites here
            // Otherwise, the user will need to explicitly accept them later
            /*
            await sdk.update({
              status: "accepted",
              update_at: sqlDateTimeFormat(new Date())
            }, invite.id);

            // If auto-accepting, also add to group members
            sdk.setTable("user_groups");
            const group = await sdk.get({id: invite.group_id});
            if(group.length > 0) {
              const existing_members = JSON.parse(group[0].members || "[]");
              existing_members.push(result);
              await sdk.update({
                members: JSON.stringify(existing_members),
                update_at: sqlDateTimeFormat(new Date())
              }, invite.group_id);
            }
            */
          }
        }

        return res.status(200).json({
          error: false,
          role: req.body.role,
          token: JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role,
              club_id: req.body.club_id,
            },
            config.jwt_expire,
            config.jwt_key
          ),
          club_id: req.body.club_id,
          refresh_token: refreshToken,
          expire_at: needRefreshToken ? (3600 * 24 * 30) : config.jwt_expire,
          user_id: result
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // custom login
  app.post("/v3/api/custom/courtmatchup/users/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password, role } = req.body;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          role: "required"
        },
        {
          email: "Email address could not be found. Please check your login details and try again.",
          password: "password is missing",
          role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);

      logService.log(req.projectId, email, password);
      const result = await service.login(req.sdk, req.projectId, email, password, role);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result === "User does not exist" ? "Email address could not be found. Please check your login details and try again.": result === "Invalid Password" ? "Incorrect password. Please check your login details and try again." : result
        });
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }
      let sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      sdk.setTable("user")
      const user = (await sdk.get({
        id:result.id,
      }))[0]
      sdk.setTable("clubs")
      const club = (await sdk.get({
        id:user?.club_id
      }))[0]

      // if (!club) throw new Error(ERROR_MESSAGES.CLUB_NOT_FOUND)
      
      const club_id = club?.id ?? null;

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            club_id,
            role: role
          },
          3600 * 24 * 30,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + (3600 * 24 * 30));
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        role,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role,
            club_id
          },
          config.jwt_expire,
          config.jwt_key
        ),
        club: club ?? null,
        refresh_token: refreshToken,
        expire_at: needRefreshToken ? (3600 * 24 * 30) : config.jwt_expire,
        user_id: result.id,
        first_name: result.first_name ?? null,
        last_name: result.last_name ?? null,
        photo: result.photo ?? null,
        email: result.email ?? null,
        club_id:club_id || null,
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v3/api/custom/courtmatchup/users/stripe/subscriptions", _middlewares, TokenMiddleware(), async function (req, res) {
    try {
      /**
       * get subscriptions
       */
      const sdk = req.sdk;
      let { limit, page, family_members, customer_email, plan_name, sub_status, plan_type, currentPeriodStart, currentPeriodEnd, order_by, direction } = req.query;

      sdk.setProjectId(req.projectId);

      const db = sdk.getDatabase();
      const subscriptionTable = `courtmatchup_stripe_subscription`;
      const priceTable = `courtmatchup_stripe_price`;
      const userTable = `courtmatchup_user`;
      const user_id = req.user_id;

      let query = filterEmptyFields({
        // user_id,
        family_members: family_members === "true" ? family_members : null,
        customer_email,
        plan_name,
        sub_status,
        plan_type,
        currentPeriodStart,
        currentPeriodEnd
      });

      let where = [];

      if(family_members && (family_members.toLowerCase() !== "true" && family_members !== true)) {
        where.push(`sub.user_id = ${user_id} `);
      };

      // Object.entries(query)?.forEach(async ([key, value]) => {
      //   console.log(key, value);
      //   switch (key) {
      //     // case "user_id": {
      //     //   where.push(`sub.user_id = ${+value} `);
      //     //   break;
      //     // }
      //     case "family_members": {
      //       sdk.setTable("user");
      //       const family_members = await sdk.get({
      //         guardian: user_id
      //       });
      //       console.log(family_members);
      //       where.push(`sub.user_id IN (${family_members.map(member => member.id).join(",")}) `);
      //       break;
      //     }
      //     case "customer_email": {
      //       where.push(`user.email LIKE '%${value}%' `);
      //       break;
      //     }
      //     case "plan_name": {
      //       where.push(`price.name LIKE '%${value}%' `);
      //       break;
      //     }
      //     case "sub_status": {
      //       where.push(`sub.status = '${value}' `);
      //       break;
      //     }
      //     case "plan_type": {
      //       where.push(`price.type = '${value}' `);
      //       break;
      //     }
      //     case "currentPeriodStart": {
      //       where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_start")})) = DATE(FROM_UNIXTIME(${value}))`);
      //       break;
      //     }
      //     case "currentPeriodEnd": {
      //       where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_end")})) = DATE(FROM_UNIXTIME(${value}))`);
      //       break;
      //     }
      //   }
      // });

      for (const key of Object.keys(query)) {
        const value = query[key];
        switch (key) {
          case "family_members": {
            sdk.setTable("user");
            const family_members = await sdk.get({
              guardian: user_id
            });
            console.log(family_members);
            where.push(`sub.user_id IN (${family_members.map(member => member.id).join(",")}) `);
            break;
          }
          case "customer_email": {
            where.push(`user.email LIKE '%${value}%' `);
            break;
          }
          case "plan_name": {
            where.push(`price.name LIKE '%${value}%' `);
            break;
          }
          case "sub_status": {
            where.push(`sub.status = '${value}' `);
            break;
          }
          case "plan_type": {
            where.push(`price.type = '${value}' `);
            break;
          }
          case "currentPeriodStart": {
            where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_start")})) = DATE(FROM_UNIXTIME(${value}))`);
            break;
          }
          case "currentPeriodEnd": {
            where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_end")})) = DATE(FROM_UNIXTIME(${value}))`);
            break;
          }
        }
      }

      console.log(where);

      let sqlQuery = `
          SELECT sub.id as subId, ${jsonExtractor("sub.object", "created")} as createdAt,
            ${jsonExtractor("sub.object", "current_period_start")} as currentPeriodStart,            
            ${jsonExtractor("sub.object", "current_period_end")} as currentPeriodEnd, sub.status as status,
            price.is_usage_metered as isMetered, price.name as planName, price.type as planType, price.amount as planAmount, price.trial_days as trialDays,
            user.email as userEmail, sub.user_id as user_id, user.first_name as first_name , user.last_name as last_name, sub.object as details
          from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1}`;

      if (limit === "all") {
        const [...resource] = await sdk.rawQuery(sqlQuery);
        return res.status(200).json({ error: false, list: resource });
      }

      const [{ count: total }] = await sdk.rawQuery(
        `SELECT COUNT(*) as count from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id        
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1} `
      );
      const [...resource] = await sdk.rawQuery(
        `${sqlQuery}
          ${limit ? `LIMIT ${(+page - 1) * +limit}, ${+limit} ` : ""}
          `
      );

      const num_pages = Math.ceil(+total / +limit);
      for (let x of resource) {
        try {
          details = JSON.parse(x.details);
          x.details = details;
          delete x.details;
          x.interval = details?.plan?.interval ? details.plan.interval : "";
        } catch (e) {
          console.log(e);
          delete x.details;
          x.interval = "";
        }
      }
      res.status(200).json({ error: false, list: resource, total, limit, num_pages, page });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message ?? "Something went wrong"
      };
      res.status(500).json(payload);
    }
  });
  app.get("/v3/api/custom/courtmatchup/users/stripe/subscriptions/:id", _middlewares, TokenMiddleware(), async function (req, res) {
    try {
      /**
       * get subscriptions
       */
      const sdk = req.sdk;
      const { id } = req.params;
      let { limit, page, family_members, customer_email, plan_name, sub_status, plan_type, currentPeriodStart, currentPeriodEnd, order_by, direction } = req.query;

      sdk.setProjectId(req.projectId);

      const db = sdk.getDatabase();
      const subscriptionTable = `courtmatchup_stripe_subscription`;
      const priceTable = `courtmatchup_stripe_price`;
      const userTable = `courtmatchup_user`;
      const user_id = id;

      // check if the user is the owner of the family member
      sdk.setTable("user");
      const family_member = await sdk.get({ id, guardian: req.user_id });
      if (!family_member[0] && req.user_id !== id) {
        return res.status(401).json({ error: true, message: "User is not a family member" });
      }

      req.user_id = id;
      

      let query = filterEmptyFields({
        // user_id,
        // family_members: family_members === "true" ? family_members : null,
        customer_email,
        plan_name,
        sub_status,
        plan_type,
        currentPeriodStart,
        currentPeriodEnd
      });

      let where = [];

        // if(family_members && (family_members.toLowerCase() !== "true" && family_members !== true)) {
        // };
      where.push(`sub.user_id = ${user_id} `);

      // Object.entries(query)?.forEach(async ([key, value]) => {
      //   console.log(key, value);
      //   switch (key) {
      //     // case "user_id": {
      //     //   where.push(`sub.user_id = ${+value} `);
      //     //   break;
      //     // }
      //     case "family_members": {
      //       sdk.setTable("user");
      //       const family_members = await sdk.get({
      //         guardian: user_id
      //       });
      //       console.log(family_members);
      //       where.push(`sub.user_id IN (${family_members.map(member => member.id).join(",")}) `);
      //       break;
      //     }
      //     case "customer_email": {
      //       where.push(`user.email LIKE '%${value}%' `);
      //       break;
      //     }
      //     case "plan_name": {
      //       where.push(`price.name LIKE '%${value}%' `);
      //       break;
      //     }
      //     case "sub_status": {
      //       where.push(`sub.status = '${value}' `);
      //       break;
      //     }
      //     case "plan_type": {
      //       where.push(`price.type = '${value}' `);
      //       break;
      //     }
      //     case "currentPeriodStart": {
      //       where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_start")})) = DATE(FROM_UNIXTIME(${value}))`);
      //       break;
      //     }
      //     case "currentPeriodEnd": {
      //       where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_end")})) = DATE(FROM_UNIXTIME(${value}))`);
      //       break;
      //     }
      //   }
      // });

      for (const key of Object.keys(query)) {
        const value = query[key];
        switch (key) {
          // case "family_members": {
          //   sdk.setTable("user");
          //   const family_members = await sdk.get({
          //     guardian: user_id
          //   });
          //   console.log(family_members);
          //   where.push(`sub.user_id IN (${family_members.map(member => member.id).join(",")}) `);
          //   break;
          // }
          case "customer_email": {
            where.push(`user.email LIKE '%${value}%' `);
            break;
          }
          case "plan_name": {
            where.push(`price.name LIKE '%${value}%' `);
            break;
          }
          case "sub_status": {
            where.push(`sub.status = '${value}' `);
            break;
          }
          case "plan_type": {
            where.push(`price.type = '${value}' `);
            break;
          }
          case "currentPeriodStart": {
            where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_start")})) = DATE(FROM_UNIXTIME(${value}))`);
            break;
          }
          case "currentPeriodEnd": {
            where.push(`DATE(FROM_UNIXTIME(${jsonExtractor("sub.object", "current_period_end")})) = DATE(FROM_UNIXTIME(${value}))`);
            break;
          }
        }
      }

      console.log(where);

      let sqlQuery = `
          SELECT sub.id as subId, ${jsonExtractor("sub.object", "created")} as createdAt,
            ${jsonExtractor("sub.object", "current_period_start")} as currentPeriodStart,            
            ${jsonExtractor("sub.object", "current_period_end")} as currentPeriodEnd, sub.status as status,
            price.is_usage_metered as isMetered, price.name as planName, price.type as planType, price.amount as planAmount, price.trial_days as trialDays,
            user.email as userEmail, sub.user_id as user_id, user.first_name as first_name , user.last_name as last_name, sub.object as details
          from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1}`;

      if (limit === "all") {
        const [...resource] = await sdk.rawQuery(sqlQuery);
        return res.status(200).json({ error: false, list: resource });
      }

      const [{ count: total }] = await sdk.rawQuery(
        `SELECT COUNT(*) as count from ${subscriptionTable} as sub
          LEFT JOIN ${priceTable} as price ON sub.price_id = price.id        
          LEFT JOIN ${userTable} as user ON sub.user_id = user.id
          WHERE ${where.length ? where.join(" AND ") : 1} `
      );
      const [...resource] = await sdk.rawQuery(
        `${sqlQuery}
          ${limit ? `LIMIT ${(+page - 1) * +limit}, ${+limit} ` : ""}
          `
      );

      const num_pages = Math.ceil(+total / +limit);
      for (let x of resource) {
        try {
          details = JSON.parse(x.details);
          x.details = details;
          delete x.details;
          x.interval = details?.plan?.interval ? details.plan.interval : "";
        } catch (e) {
          console.log(e);
          delete x.details;
          x.interval = "";
        }
      }
      res.status(200).json({ error: false, list: resource, total, limit, num_pages, page });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message ?? "Something went wrong"
      };
      res.status(500).json(payload);
    }
  });
  app.post("/v3/api/custom/courtmatchup/user/stripe/subscription", _middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      const { planId, user_id } = req.body;
      const validationResult = await ValidationService.validateObject(
        {
          planId: "required"
        },
        { planId }
      );

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }

      sdk.setProjectId(req.projectId);

      const db = sdk.getDatabase();
      const userId = user_id || req.user_id;
      const userTable = `${sdk.getProjectId()}_user`;
      const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
      const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;


      if (user_id) {
        // check for family members
        sdk.setTable("user");
        const is_family_member = await sdk.get({
          id: user_id,
          guardian: req.user_id
        });

        if(!is_family_member[0]) {
          return res.status(401).json({ error: true, message: "User is not a family member" });
        }
      }

      const customer = await sdk.rawQuery(`
        SELECT u.*, s.id as subId, s.stripe_id as subStripeId,
        ${jsonExtractor("s.object", "items.data[0].id")} as subItemId, p.id AS planId, p.type as planType
        FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') 
        LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
        WHERE u.id = ${userId} ;
      `);

      if (!customer[0]) {
        return res.status(404).json({ error: true, message: "Customer not found" });
      }

      if (customer[0].subId) {
        return res.status(401).json({ error: true, message: "Customer already has an active subscription" });
      }

      if (customer[0].stripe_uid === null) return res.status(404).json({ error: true, message: "Customer Stripe ID not found, please add a card first." });

      const stripeCustomer = await stripe.retrieveStripeCustomer({ customerId: customer[0].stripe_uid });
      console.log(stripeCustomer);
      if (!stripeCustomer.default_source && !stripeCustomer.sources?.data?.length && !stripeCustomer.invoice_settings?.default_payment_method) {
        return res.status(403).json({ error: true, message: "You don't have a valid card attached, please add one and try again" });
      }

      sdk.setTable("stripe_price");
      const metadata = {
        projectId: sdk.getProjectId()
      };

      const plan = await sdk.get({ id: planId });
      if (!plan[0]) {
        return res.status(404).json({ error: true, message: "Plan not found" });
      }
      await stripe.createStripeSubscription({
        customerId: customer[0].stripe_uid,
        priceId: plan[0].stripe_id,
        default_payment_method: stripeCustomer.default_source || stripeCustomer.sources?.data[0]?.id || stripeCustomer.invoice_settings?.default_payment_method,
        trial_from_plan: true,
        metadata
      });

      // await stripe.updateInvoice(subscription.latest_invoice, { projectId: req.projectId });
      res.status(200).json({ error: false, message: "User subscribed successfully" });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });
  app.delete("/v3/api/custom/courtmatchup/user/stripe/subscription/:id", _middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      /**
       * cancel subscription
       */
      const { id } = req.params;
      const { cancel_type, user_id: family_member_id } = req.body;
      const user_id = family_member_id || req.user_id;
      // can delete for family members

      if(user_id !== req.user_id) {
        sdk.setTable("user");
        const is_family_member = await sdk.get({
          id: user_id,
          guardian: req.user_id
        });
        if(!is_family_member[0]){
          return res.status(401).json({ error: true, message: "User is not a family member" });
        }
      }

      if(user_id !== req.user_id) {
        req.user_id = user_id;
      }

      sdk.setProjectId(req.projectId);
      sdk.setTable("stripe_subscription");

      const priceTable = `${sdk.getProjectId()}_stripe_price`;
      const db = sdk.getDatabase();
      const sub = await sdk.rawQuery(
        `select sub.*, price.type as planType from ${sdk.getTable()} as sub left join ${priceTable} as price on sub.price_id = price.id where sub.status != 'canceled' and sub.id = ${+id} and sub.user_id = ${user_id}`
      );

      if (!sub[0]) {
        return res.status(404).json({ error: true, message: "Subscription not found" });
      }

      if (sub[0].planType === "lifetime") {
        sdk.setTable("stripe_subscription");
        await sdk.updateWhere(
          {
            status: "canceled",
            update_at: sqlDateTimeFormat(new Date())
          },
          {
            stripe_id: sub[0].stripe_id
          }
        );
      } else {
        if (cancel_type == "at_period_end") {
          await stripe.cancelStripeSubscriptionAtPeriodEnd({ subscriptionId: sub[0].stripe_id });
        } else {
          await stripe.cancelStripeSubscription({ subscriptionId: sub[0].stripe_id });
        }
      }

      res.status(200).json({ error: false, message: "Subscription is canceled successfully" });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message ?? "Something went wrong"
      };
      res.status(500).json(payload);
    }
  });
  app.post("/v3/api/custom/courtmatchup/user/stripe/subscription/update", _middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      const { planId, subId, proration, user_id } = req.body;
      const validationResult = await ValidationService.validateObject(
        {
          planId: "required"
        },
        { planId }
      );

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }

      sdk.setProjectId(req.projectId);

      const db = sdk.getDatabase();
      const userId = user_id || req.user_id;
      const userTable = `${sdk.getProjectId()}_user`;
      const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
      const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;

      if (user_id) {
        // check for family members
        sdk.setTable("user");
        const is_family_member = await sdk.get({
          id: user_id,
          guardian: req.user_id
        });

        if(!is_family_member[0]) {
          return res.status(401).json({ error: true, message: "User is not a family member" });
        }
      }

      const customer = await sdk.rawQuery(`
        SELECT u.*, s.id as subId, s.stripe_id as subStripeId,
        ${jsonExtractor("s.object", "items.data[0].id")} as subItemId, p.id AS planId, p.type as planType
        FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') 
        LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
        WHERE u.id = ${userId} ;
      `);

      if (!customer[0]) {
        return res.status(404).json({ error: true, message: "Customer not found" });
      }

      // if (customer[0].subId) {
      //   return res.status(401).json({ error: true, message: "Customer already has an active subscription" });
      // }

      if (customer[0].stripe_uid === null) return res.status(404).json({ error: true, message: "Customer Stripe ID not found, please add a card first." });

      const stripeCustomer = await stripe.retrieveStripeCustomer({ customerId: customer[0].stripe_uid });
      console.log(stripeCustomer);
      if (!stripeCustomer.default_source && !stripeCustomer.sources?.data?.length && !stripeCustomer.invoice_settings?.default_payment_method) {
        return res.status(403).json({ error: true, message: "You don't have a valid card attached, please add one and try again" });
      }

      sdk.setTable("stripe_price");
      const metadata = {
        projectId: sdk.getProjectId()
      };

      const [plan] = await sdk.get({ id: planId });
      if (!plan) {
        return res.status(404).json({ error: true, message: "Plan not found" });
      }
      await stripe.updateStripeSubscription({
        subscriptionId: subId ?? customer[0]?.subStripeId,
        priceId: plan?.stripe_id,
        proration
      });

      // await stripe.updateInvoice(subscription.latest_invoice, { projectId: req.projectId });
      res.status(200).json({ error: false, message: "User subscription upgraded successfully" });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });
  app.get("/v3/api/custom/courtmatchup/users/google/code/mobile", middlewares, async function (req, res) {

    const projectId = req.projectId;
    const role = req.query.role ?? "user";
    const needRefreshToken = req.query.is_refresh ?? false;
    let refreshToken = undefined;

    const googleConfig = {
      clientID: _config.google.client_id,
      clientSecret: _config.google.client_secret,
      redirectURL: "",
      defaultScope: ["https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"]
    };

    let sdk = req.sdk;

    const googleLogin = new NodeGoogleLogin(googleConfig);

    try {
      const userProfile = await googleLogin.getUserProfile(req.query.code);
      let service = new AuthService();
      logService.log(userProfile);

      let id = await service.googleLogin(sdk, projectId, userProfile.user, userProfile.tokens, role);

      if (typeof id == "string") {
        return res.status(403).json({
          error: true,
          message: id
        });
      }
      sdk.setTable("user")
      const user = (await sdk.get({
        id:id,
      }))[0]
      sdk.setTable("clubs")
      const club = (await sdk.get({
        id:user.club_id,
      }))[0]

      // if (!club) throw new Error(ERROR_MESSAGES.CLUB_NOT_FOUND)
      
      const club_id = club?.id ?? null;

      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            club_id,
            role: role
          },
          3600 * 24 * 30,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + (3600 * 24 * 30));
        await service.saveRefreshToken(sdk, req.projectId, id, refreshToken, expireDate);
      }

      return res.status(200).json({
        error: false,
        role: role,
        token: JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
            club_id
          },
          config.jwt_expire,
          config.jwt_key
        ),
        club: club ?? null,
        club_id,
        expire_at: needRefreshToken ? (3600 * 24 * 30) : config.jwt_expire,
        user_id: id,
        refresh_token: refreshToken
      });
    } catch (error) {
      console.log(error);
      return res.status(403).json({
        error: true,
        message: "Invalid Credentials",
        trace: error
      });
    }
  });

  app.post("/v3/api/courtmatchup/user/apple/code", async function (req, res) {
    let project = { hostname: "mkdlabs.com" };

    try {
      let { code, state } = req.body;
      console.log(req.body);

      // Part: Sample Req Body
      // req.body = {
      //   state: "ZXJnbzprNWdvNGU5MTh4MnVsanV2OHJxcXAyYXM=~customer",
      //   code: "cf6ebfa17f96737efea8dc55a.0.rrsuw.YZcHfL15sPkt_bXw6QtXSg",
      //   user: '{"name":{"firstName":"ABC","lastName":"DEF"},"email":"<EMAIL>"}'
      // };

      const projectId = "courtmatchup";
      const role = "user";
      const database = `baas_${projectId}`;

      let sdk = new BackendSDK();
      sdk.setDatabase(database);
      sdk.setProjectId(projectId);

      let manaknightSDK = new ManaKnightSDK();

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      // Remark: Fetching Project
      // if (config.env == "production") {
      //   project = require("../project");
      // } else {
      // sdk.setProjectId("manaknight");
      // sdk.setTable("projects");

      // project = (
      //   await manaknightSDK.get({
      //     project_id: projectId
      //   })
      // )[0];
      // }

      const clientSecret = appleSignin.getClientSecret({
        clientID: _config.apple.client_id, // Apple Client ID
        teamID: _config.apple.team_id, // Apple Developer Team ID.
        privateKey: _config.apple.private_key, // private key associated with your client ID. -- Or provide a `privateKeyPath` property instead.
        keyIdentifier: _config.apple.key_id // identifier of the private key.
      });

      const options = {
        clientID: _config.apple.client_id, // Apple Client ID
        redirectUri: _config.apple.redirect_url, // use the same value which you passed to authorisation URL.
        clientSecret: clientSecret
      };

      const tokenResponse = await appleSignin.getAuthorizationToken(code, options);
      console.log(code,tokenResponse);

      const identityToken = tokenResponse.id_token;

      const data = jwt.decode(identityToken, { complete: true });

      if (!data) {
        throw new Error("Invalid_grant");
      }

      const kid = data.header.kid;

      const appleSigningKey = await JwtService.getAppleSigningKeys(kid);

      const payload = await JwtService.verifyAppleLogin(identityToken, appleSigningKey);
      // Part: Sample Payload Res
      // payload = {
      //   iss: "https://appleid.apple.com",
      //   aud: "mkd.baas.serviceid",
      //   exp: 1695751588,
      //   iat: 1695665188,
      //   sub: "001246.76ff24dgh1a8ce0f9ba236.1806",
      //   at_hash: "xN29677PtPM-wycU2S8GQ",
      //   email: "<EMAIL>",
      //   email_verified: "true",
      //   is_private_email: "true",
      //   auth_time: 1695665186,
      //   nonce_supported: true
      // };

      const user_details = {
        first_name: " ",
        last_name: " ",
        email: payload.email
      };

      if (req.body.user) {
        const { name, email } = JSON.parse(req.body.user);

        // Part: Sample Parsed User Info
        // parsedUser = {
        //   name: { firstName: "ABC", lastName: "DEF" },
        //   email: "<EMAIL>"
        // };

        user_details.first_name = name.firstName;
        user_details.last_name = name.lastName;
      }

      const service = new AuthService();
      if (!user_details.email) {
        throw new Error("Could not access email address");
      }

      let apple_login_res;
      apple_login_res = await service.appleLogin(sdk, "courtmatchup", user_details, identityToken, "user");

      const { id, is_newuser } = apple_login_res;
      let new_jwt = JwtService.createAccessToken(
        {
          user_id: id,
          role: "user"
        },
        config.jwt_expire,
        config.jwt_key
      );

      let refreshToken = JwtService.createAccessToken(
        {
          user_id: id,
          role: "user"
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );
      let expireDate = new Date();
      expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
      await service.saveRefreshToken(sdk, "courtmatchup", id, refreshToken, expireDate);

      const resData = JSON.stringify({
        error: false,
        role: "user",
        access_token: new_jwt,
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: id,
        state: state,
        is_newuser: is_newuser
      });

      const encodedURI = encodeURI(resData);

      // res.redirect(`https://${project.hostname}/login/oauth?data=${encodedURI}`);
      // redirect to mobile deeplink 
      res.redirect(`${_config.apple.redirect_url_mobile}?data=${encodedURI}`);
    } catch (err) {
      console.log(err);

      const data = JSON.stringify({
        error: true,
        message: err.message
      });

      const encodedURI = encodeURI(data);
      // res.redirect(base_url ? base_url + `/login/oauth?data=${encodedURI}` : `https://${project.hostname}/login/oauth?data=${encodedURI}`);
      res.redirect(`${_config.apple.redirect_url_mobile}?data=${encodedURI}`);
      // return res.status(403).json({
      //   error: true,
      //   message: err.message
      // });
    }
  });
  app.get("/v3/api/custom/courtmatchup/users/clubs", middlewares, async function (req, res) {

    let sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      sdk.setTable("clubs")

      // const clubs = await sdk.get({})
      const clubs = await sdk.rawQuery(`
        SELECT courtmatchup_clubs.*,
        GROUP_CONCAT(
          CONCAT(
            courtmatchup_sports.name, ';',
            courtmatchup_sports.cancel_hours_before, ';',
            courtmatchup_sports.allow_cancel_reservation
          ) SEPARATOR '||'
        ) as sports_info,
        GROUP_CONCAT(cst.name, ';', cst.subtype , ';', cst.id SEPARATOR '==') AS sport_types 
        FROM courtmatchup_clubs
        LEFT JOIN courtmatchup_club_sport_type cst ON cst.club_id = courtmatchup_clubs.id
        LEFT JOIN courtmatchup_sports ON courtmatchup_sports.club_id = courtmatchup_clubs.id
        GROUP BY courtmatchup_clubs.id
      `)

      // Process the sports information
      const processedClubs = clubs.map(club => {
        const sports = club.sports_info ? club.sports_info.split('||').map(sport => {
          const [name, cancelHoursBefore, allowCancelReservation] = sport.split(';');
          return {
            name,
            cancel_hours_before: cancelHoursBefore,
            allow_cancel_reservation: allowCancelReservation === '1'
          };
        }) : [];

        const sportTypes = club.sport_types ? club.sport_types.split('==').map(type => {
          const [name, subtype, id] = type.split(';');
          return { name, subtype, id };
        }) : [];

        return {
          ...club,
          sports,
          sport_types: sportTypes
        };
      });

      return res.status(200).json({
        error: false,
        clubs: processedClubs
      });
    } catch (error) {
      console.log(error);
      return res.status(403).json({
        error: true,
        message: "Invalid Credentials",
        trace: error
      });
    }
  });

  app.post("/v3/api/custom/courtmatchup/users/apple/login/mobile", async function (req, res) {
    let project = { hostname: "mkdlabs.com" };

    try {
      let { code, state } = req.body;
      console.log(req.body);

      // Part: Sample Req Body
      // req.body = {
      //   state: "ZXJnbzprNWdvNGU5MTh4MnVsanV2OHJxcXAyYXM=~customer",
      //   code: "cf6ebfa17f96737efea8dc55a.0.rrsuw.YZcHfL15sPkt_bXw6QtXSg",
      //   user: '{"name":{"firstName":"ABC","lastName":"DEF"},"email":"<EMAIL>"}'
      // };

      state = JSON.parse(state);
      const database =  `baas_courtmatchup`;

      let sdk = new BackendSDK();
      sdk.setDatabase(database);
      sdk.setProjectId("courtmatchup");

      let manaknightSDK = new ManaKnightSDK();

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId("courtmatchup");

      // Remark: Fetching Project
      if (config.env == "production") {
        project = require("../project");
      } else {
        sdk.setProjectId("manaknight");
        sdk.setTable("projects");

        project = (
          await manaknightSDK.get({
            project_id: "courtmatchup"
          })
        )[0];
      }
      

      const clientSecret = appleSignin.getClientSecret({
        clientID: _config.apple.client_id, // Apple Client ID
        teamID: _config.apple.team_id, // Apple Developer Team ID.
        privateKey: _config.apple.private_key, // private key associated with your client ID. -- Or provide a `privateKeyPath` property instead.
        keyIdentifier: _config.apple.key_id // identifier of the private key.
      });

      const options = {
        clientID: _config.apple.client_id, // Apple Client ID
        redirectUri: _config.apple.redirect_url, // use the same value which you passed to authorisation URL.
        clientSecret: clientSecret
      };

      const tokenResponse = await appleSignin.getAuthorizationToken(code, options);

      const identityToken = tokenResponse.id_token;

      const data = jwt.decode(identityToken, { complete: true });

      if (!data) {
        throw new Error("Invalid_grant");
      }

      const kid = data.header.kid;

      const appleSigningKey = await JwtService.getAppleSigningKeys(kid);

      const payload = await JwtService.verifyAppleLogin(identityToken, appleSigningKey);
      // Part: Sample Payload Res
      // payload = {
      //   iss: "https://appleid.apple.com",
      //   aud: "mkd.baas.serviceid",
      //   exp: 1695751588,
      //   iat: 1695665188,
      //   sub: "001246.76ff24dgh1a8ce0f9ba236.1806",
      //   at_hash: "xN29677PtPM-wycU2S8GQ",
      //   email: "<EMAIL>",
      //   email_verified: "true",
      //   is_private_email: "true",
      //   auth_time: 1695665186,
      //   nonce_supported: true
      // };

      const user_details = {
        first_name: " ",
        last_name: " ",
        email: payload.email
      };

      if (req.body.user) {
        const { name, email } = JSON.parse(req.body.user);

        // Part: Sample Parsed User Info
        // parsedUser = {
        //   name: { firstName: "ABC", lastName: "DEF" },
        //   email: "<EMAIL>"
        // };

        user_details.first_name = name.firstName;
        user_details.last_name = name.lastName;
      }

      const service = new AuthService();
      if (!user_details.email) {
        throw new Error("Could not access email address");
      }

      let apple_login_res;
      apple_login_res = await service.appleLogin(sdk, "courtmatchup", user_details, identityToken, "user");

      const { id, is_newuser } = apple_login_res;
      let new_jwt = JwtService.createAccessToken(
        {
          user_id: id,
          role: "user"
        },
        config.jwt_expire,
        config.jwt_key
      );

      let refreshToken = JwtService.createAccessToken(
        {
          user_id: id,
          role: "user"
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );
      let expireDate = new Date();
      expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
      await service.saveRefreshToken(sdk, "courtmatchup", id, refreshToken, expireDate);

      const resData = JSON.stringify({
        error: false,
        role: "user",
        access_token: new_jwt,
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: id,
        state: state,
        is_newuser: is_newuser
      });

      const encodedURI = encodeURI(resData);

      // res.redirect(`https://${project.hostname}/login/oauth?data=${encodedURI}`);
      // redirect to mobile deeplink 
      res.redirect(`${_config.apple.redirect_url_mobile}?data=${encodedURI}`);
    } catch (err) {
      console.log(err);

      const data = JSON.stringify({
        error: true,
        message: err.message
      });

      const encodedURI = encodeURI(data);
      // res.redirect(base_url ? base_url + `/login/oauth?data=${encodedURI}` : `https://${project.hostname}/login/oauth?data=${encodedURI}`);
      res.redirect(`${_config.apple.redirect_url_mobile}?data=${encodedURI}`);
      // return res.status(403).json({
      //   error: true,
      //   message: err.message
      // });
    }
  });


  app.post("/v3/api/custom/courtmatchup/users/profile-edit", [...middlewares,TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("user");

      const updateResult = await sdk.update({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo ?? null,
        phone: req.body.phone ?? null,
        update_at: sqlDateTimeFormat(new Date()),
      }, req.user_id);

      sdk.setTable("profile");
      const profile = (await sdk.get({
        user_id: req.user_id,
      }))[0];
      

      const {
        gender,
        address,
        city,
        date_of_birth,
        fields,
        first_time,
        ntrp,
        house_no,
        country,
        state,
        zip_code,
      } = req.body;
      
      sdk.setTable('profile');
      await sdk.update(filterEmptyFields({
        gender,
        address,
        city,
        date_of_birth,
        fields,
        first_time,
        ntrp,
        house_no,
        country,
        state,
        zip_code,
        update_at: sqlDateTimeFormat(new Date())
      }), profile.id);
      

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }


      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });



  return [
    {
      method: "POST",
      name: "Register API",
      url: "/v3/api/custom/courtmatchup/register",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
        {
          name: "403",
          body: '{"role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Role Missing","validation": [{ "field": "role", "message": "Role missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    }
  ];
};
