const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const config = require("../../../config");
const BackendSDK = require("../../../core/BackendSDK");
const stripe = require("stripe")(config.stripe.secret_key);

// Middleware configuration for club routes
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({
    role: "club|staff|coach|admin_staff",
  }),
];

// Middleware configuration for admin routes
const adminMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({
    role: "admin|admin_staff",
  }),
];

// Middleware configuration for open routes - no authentication required
const openMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
];

module.exports = function (app) {
  /**
   * @route POST /v3/api/custom/courtmatchup/club/stripe/onboarding
   * @desc Initiates the Stripe Connect onboarding process for a club
   * @access Private (club users only)
   */
  app.post("/v3/api/custom/courtmatchup/:role/stripe/onboarding", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const role = req.params.role;
      let id = null;
      let club = null;
      let name = null;
      if (role === "club") {
        id = req.user_id;
        // Get club information
        sdk.setTable("clubs");
        club = (await sdk.get({
          user_id: id,
        }))[0];
        name = club.name;
      } else {
        sdk.setTable(role === "staff" ? "staff" : role == "coach" ? "coach" : "user");
        const user = (await sdk.get({
          user_id: req.user_id
        }))[0];
        id = user.club_id;
        sdk.setTable("user");
        const _user = (await sdk.get({
          id: req.user_id
        }))[0];
        name = _user.first_name + " " + _user.last_name;
        // Get club information
        // sdk.setTable(role === "staff" ? "staff" : role == "coach" ? "coach" : "clubs");
        sdk.setTable("clubs");
        club = (await sdk.get({
          id: id,
        }))[0];
      }


      if (!club) {
        return res.status(404).json({
          error: true,
          message: "Club not found",
        });
      }

      // Check if club already has a Stripe account
      sdk.setTable("stripe_account");
      const existingAccount = await sdk.get({
        club_id: club.id,
        user_id: req.user_id
      });

      let accountId = null;

      // If the club already has a Stripe account and it's complete, return the existing account
      if (existingAccount.length > 0 && existingAccount[0].complete === 1) {
        return res.status(200).json({
          error: false,
          message: "Stripe account already set up",
          complete: true,
          account_id: existingAccount[0].account_id
        });
      }
      
      // If the club has a Stripe account but it's not complete
      if (existingAccount.length > 0) {
        accountId = existingAccount[0].account_id;
      } else {
        // Create a new Stripe Connect Express account
        const account = await stripe.accounts.create({
          type: 'express',
          country: req.body.country || 'CA',
          email: req.body.email,
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          business_type: 'company',
          business_profile: {
            name,
            url: req.body.website || config.base_url,
          },
          metadata: {
            club_id: club.id,
            user_id: req.user_id
          }
        });

        accountId = account.id;

        // Save the account ID to the database
        await sdk.insert({
          user_id: req.user_id,
          club_id: club.id,
          account_id: accountId,
          payout_date: req.body.payout_date || '20th',
          status: 1,
          complete: 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      }

      // Create an account link for onboarding
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/refresh?account_id=${accountId}`,
        return_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/callback?account_id=${accountId}`,
        type: 'account_onboarding',
      });

      return res.status(200).json({
        error: false,
        message: "Stripe onboarding initiated",
        account_id: accountId,
        url: accountLink.url,
        complete: false
      });
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route GET /v3/api/custom/courtmatchup/stripe/account/refresh
   * @desc Handles refreshing the Stripe Connect onboarding process
   * @access Public
   */
  app.get("/v3/api/custom/courtmatchup/stripe/account/refresh", async function (req, res) {
    try {
      let sdk = new BackendSDK();
      sdk.setProjectId('courtmatchup');
      sdk.setDatabase('baas_courtmatchup');

      const { account_id } = req.query;

      if (!account_id) {
        return res.status(400).json({
          error: true,
          message: "Account ID is required",
        });
      }

      // Get the account information
      sdk.setTable("stripe_account");
      const accounts = await sdk.get({
        account_id: account_id
      });

      if (accounts.length === 0) {
        return res.status(404).json({
          error: true,
          message: "Stripe account not found",
        });
      }

      const account = accounts[0];

      // Create a new account link for onboarding
      const accountLink = await stripe.accountLinks.create({
        account: account_id,
        refresh_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/refresh?account_id=${account_id}`,
        return_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/callback?account_id=${account_id}`,
        type: 'account_onboarding',
      });

      // Redirect to the new account link
      res.redirect(accountLink.url);
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route GET /v3/api/custom/courtmatchup/stripe/account/callback
   * @desc Handles the callback from Stripe when a user completes the onboarding process
   * @access Public
   */
  app.get("/v3/api/custom/courtmatchup/stripe/account/callback", async function (req, res) {
    try {
      let sdk = new BackendSDK();
      sdk.setProjectId('courtmatchup');
      sdk.setDatabase('baas_courtmatchup');

      const { account_id } = req.query;

      if (!account_id) {
        return res.status(400).json({
          error: true,
          message: "Account ID is required",
        });
      }

      // Get the Stripe account
      const account = await stripe.accounts.retrieve(account_id);

      // Check if the account is properly set up
      let payouts_enabled = account.payouts_enabled
      let charges_enabled = account.charges_enabled
      let details_submitted = account.details_submitted
      
      const isComplete = 
        account.details_submitted && 
        account.charges_enabled && 
        account.payouts_enabled;

      console.log(account)

      // Update the database with the current status
      sdk.setTable("stripe_account");
      await sdk.updateWhere({
        complete: isComplete ? 1 : 0,
        status: account.details_submitted ? 1 : 0,
        update_at: sqlDateTimeFormat(new Date())
      }, {
        account_id: account_id
      });

      // Redirect to the club dashboard or a success page
      res.redirect(`https://courtmatchup.manaknightdigital.com/stripe_onboarding?status=${isComplete ? 'success' : 'incomplete'}&payouts_enabled=${payouts_enabled}&charges_enabled=${charges_enabled}&details_submitted=${details_submitted}`);
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route POST /v3/api/custom/courtmatchup/club/stripe/account/verify
   * @desc Verifies that a club's Stripe Connect account is properly set up
   * @access Private (club users only)
   */
  app.post("/v3/api/custom/courtmatchup/:role/stripe/account/verify", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      // const { account_id } = req.body;
      const role = req.params.role;
      sdk.setTable("stripe_account")
      const account = (await sdk.get({
        user_id: req.user_id
      }))[0];

      if (!account) {
        return res.status(400).json({
          error: true,
          message: "No Stripe account found",
        });
      }

      // Get the Stripe account
      const stripeAccount = await stripe.accounts.retrieve(account.account_id);

      // Check if the account is properly set up
      const isComplete = 
        stripeAccount.details_submitted && 
        stripeAccount.charges_enabled && 
        stripeAccount.payouts_enabled;

      // Update the database with the current status
      sdk.setTable("stripe_account");
      await sdk.updateWhere({
        complete: isComplete ? 1 : 0,
        status: stripeAccount.details_submitted ? 1 : 0,
        update_at: sqlDateTimeFormat(new Date())
      }, {
        account_id: account.account_id
      });

      return res.status(200).json({
        error: false,
        account_id: account.account_id,
        complete: isComplete,
        details_submitted: stripeAccount.details_submitted,
        charges_enabled: stripeAccount.charges_enabled,
        payouts_enabled: account.payouts_enabled
      });
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route POST /v3/api/custom/courtmatchup/club/stripe/transfer
   * @desc Creates a transfer to a club's Stripe Connect account
   * @access Private (Club only)
   */
  app.post("/v3/api/custom/courtmatchup/:role/stripe/transfer", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const role = req.params.role;

      if (role !== "club" && role !== "admin") {
        return res.status(400).json({
          error: true,
          message: "Invalid role",
        });
      }
      const { club_id, amount, description } = req.body;

      if (!club_id || !amount) {
        return res.status(400).json({
          error: true,
          message: "Club ID and amount are required",
        });
      }

      // Get the club's Stripe account
      sdk.setTable("stripe_account");
      const accounts = await sdk.get({
        club_id: club_id,
        complete: 1
      });

      if (accounts.length === 0) {
        return res.status(404).json({
          error: true,
          message: "No Stripe account found for this club or account setup is incomplete",
        });
      }

      const account = accounts[0];

      // Create a transfer to the connected account
      const transfer = await stripe.transfers.create({
        amount: Math.round(amount * 100), // Stripe requires amount in cents
        currency: config.stripe.currency || 'usd',
        destination: account.account_id,
        description: description || `Transfer to club ${club_id}`,
      });

      // Record the transfer in our database if needed
      // This would be a good place to add transaction logging

      return res.status(200).json({
        error: false,
        message: "Transfer successful",
        transfer_id: transfer.id,
        amount: amount,
        account_id: account.account_id
      });
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route GET /v3/api/custom/courtmatchup/club/stripe/account
   * @desc Gets information about a club's Stripe Connect account
   * @access Private (club users only)
   */
  app.get("/v3/api/custom/courtmatchup/:role/stripe/account", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const role = req.params.role;
      let id = null;
      let club = null;
      let name = null;
      if (role === "club") {
        id = req.user_id;
        // Get club information
        sdk.setTable("clubs");
        club = (await sdk.get({
          user_id: id,
        }))[0];

      if (!club) {
        return res.status(404).json({
          error: true,
          message: "Club not found",
        });
      }
      } else {
        sdk.setTable("user");
        const user = (await sdk.get({
          id: req.user_id
        }))[0];
        id = user.club_id;
        sdk.setTable("clubs");
        club = (await sdk.get({
          id: id,
        }))[0];
      }

      // Check if club has a Stripe account
      sdk.setTable("stripe_account");
      const accounts = await sdk.get({
        club_id: club.id,
        user_id: req.user_id
      });

      if (accounts.length === 0) {
        return res.status(200).json({
          error: false,
          has_account: false,
          message: "No Stripe account found"
        });
      }

      const account = accounts[0];

      // If the account exists but is not complete, create a new account link
      if (account.complete !== 1) {
        try {
          const accountLink = await stripe.accountLinks.create({
            account: account.account_id,
            refresh_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/refresh?account_id=${account.account_id}`,
            return_url: `${config.base_url}/v3/api/custom/courtmatchup/stripe/account/callback?account_id=${account.account_id}`,
            type: 'account_onboarding',
          });

          return res.status(200).json({
            error: false,
            has_account: true,
            complete: false,
            account_id: account.account_id,
            onboarding_url: accountLink.url,
            payout_date: account.payout_date
          });
        } catch (error) {
          console.log(error);
          return res.status(200).json({
            error: false,
            has_account: true,
            complete: false,
            account_id: account.account_id,
            payout_date: account.payout_date,
            message: "Unable to create onboarding link"
          });
        }
      }

      // If the account is complete, return account information
      return res.status(200).json({
        error: false,
        has_account: true,
        complete: account.complete === 1,
        account_id: account.account_id,
        payout_date: account.payout_date
      });
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  /**
   * @route GET /v3/api/custom/courtmatchup/admin/stripe/accounts
   * @desc Gets information about all club Stripe Connect accounts
   * @access Private (admin only)
   */
  app.get("/v3/api/custom/courtmatchup/admin/stripe/accounts", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      // Get all Stripe accounts
      sdk.setTable("stripe_account");
      const accounts = await sdk.get({});

      // Get club details for all accounts
      const accountsWithClubInfo = await Promise.all(
        accounts.map(async (account) => {
          sdk.setTable("clubs");
          const club = (await sdk.get({
            id: account.club_id,
          }))[0];

          return {
            ...account,
            club_name: club ? club.name : 'Unknown'
          };
        })
      );

      return res.status(200).json({
        error: false,
        accounts: accountsWithClubInfo
      });
    } catch (err) {
      console.log(err);
      res.status(500).json({
        error: true,
        message: err.message,
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Stripe Connect Onboarding",
      url: "/v3/api/custom/courtmatchup/club/stripe/onboarding",
      successBody: '{}',
      successPayload: '{"error":false,"message":"Stripe onboarding initiated","account_id":"acct_123","url":"https://stripe.com/onboarding/link","complete":false}',
      errors: [],
      needToken: true
    },
    {
      method: "POST",
      name: "Verify Stripe Account",
      url: "/v3/api/custom/courtmatchup/club/stripe/account/verify",
      successBody: '{"account_id":"acct_123"}',
      successPayload: '{"error":false,"account_id":"acct_123","complete":true,"details_submitted":true,"charges_enabled":true,"payouts_enabled":true}',
      errors: [],
      needToken: true
    },
    {
      method: "POST",
      name: "Stripe Transfer",
      url: "/v3/api/custom/courtmatchup/admin/stripe/transfer",
      successBody: '{"club_id":1,"amount":100,"description":"Monthly payout"}',
      successPayload: '{"error":false,"message":"Transfer successful","transfer_id":"tr_123","amount":100,"account_id":"acct_123"}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Get Stripe Account",
      url: "/v3/api/custom/courtmatchup/club/stripe/account",
      successPayload: '{"error":false,"has_account":true,"complete":true,"account_id":"acct_123","payout_date":"20th"}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Stripe Account Callback",
      url: "/v3/api/custom/courtmatchup/stripe/account/callback",
      successPayload: 'Redirects to club dashboard with success/incomplete status',
      errors: [],
      needToken: false
    },
    {
      method: "GET",
      name: "Stripe Account Refresh",
      url: "/v3/api/custom/courtmatchup/stripe/account/refresh",
      successPayload: 'Redirects to Stripe onboarding',
      errors: [],
      needToken: false
    },
    {
      method: "GET",
      name: "Get All Stripe Accounts (Admin)",
      url: "/v3/api/custom/courtmatchup/admin/stripe/accounts",
      successPayload: '{"error":false,"accounts":[{"id":1,"user_id":123,"club_id":456,"account_id":"acct_123","payout_date":"20th","status":1,"complete":1,"create_at":"2023-01-01","update_at":"2023-01-01 12:00:00","club_name":"Test Club"}]}',
      errors: [],
      needToken: true
    }
  ];
}; 