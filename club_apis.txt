const express = require('express');
const router = express.Router();
// ... other imports (middleware, database services, etc.) 
clubAdminMiddleware= []
adminMiddleware= []
authMiddleware = []

// 1. Get a List of All Clubs 
router.get('/clubs', async (req, res) => {
    try {
        const clubs = await ClubService.getAllClubs();
        res.json(clubs);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get Details of a Specific Club
router.get('/clubs/:id', async (req, res) => {
    try {
        const clubId = req.params.id;
        const club = await ClubService.getClubById(clubId);
        if (!club) {
            return res.status(404).json({ error: 'Club not found' });
        }
        res.json(club);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 3. Create a New Club (Club Admin Functionality)
router.post('/clubs', clubAdminMiddleware, async (req, res) => {
    try {
        const clubData = req.body; 
        const newClub = await ClubService.createClub(clubData);
        res.status(201).json(newClub); // 201 Created status
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Update Club Details (Club Admin Functionality)
router.put('/clubs/:id', clubAdminMiddleware, async (req, res) => {
    try {
        const clubId = req.params.id;
        const updatedClubData = req.body;
        const updatedClub = await ClubService.updateClub(clubId, updatedClubData);
        if (!updatedClub) {
            return res.status(404).json({ error: 'Club not found' });
        }
        res.json(updatedClub); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 5. Delete a Club (Admin Functionality)
router.delete('/clubs/:id', adminMiddleware, async (req, res) => {
    try {
        const clubId = req.params.id;
        const deleted = await ClubService.deleteClub(clubId);
        if (!deleted) {
            return res.status(404).json({ error: 'Club not found' });
        }
        res.json({ message: 'Club deleted' }); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});



// ... other imports

// 1. Get Courts for a Club
router.get('/clubs/:clubId/courts', async (req, res) => {
    try {
        const clubId = req.params.clubId;
        const courts = await CourtService.getCourtsByClub(clubId);
        res.json(courts);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get a Specific Court 
router.get('/courts/:id', async (req, res) => {
    try {
        const courtId = req.params.id;
        const court = await CourtService.getCourtById(courtId);
        if (!court) {
            return res.status(404).json({ error: 'Court not found' });
        }
        res.json(court); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 3. Add a New Court (Club Admin Functionality)
router.post('/clubs/:clubId/courts', clubAdminMiddleware, async (req, res) => {
    try {
        const clubId = req.params.clubId;
        const courtData = req.body; 
        const newCourt = await CourtService.addCourt(clubId, courtData); 
        res.status(201).json(newCourt); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Update a Court (Club Admin Functionality)
router.put('/courts/:id', clubAdminMiddleware, async (req, res) => {
    try {
        const courtId = req.params.id;
        const updatedCourtData = req.body; 
        const updatedCourt = await CourtService.updateCourt(courtId, updatedCourtData);
        if (!updatedCourt) {
            return res.status(404).json({ error: 'Court not found' });
        }
        res.json(updatedCourt); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 5. Delete a Court (Club Admin Functionality)
router.delete('/courts/:id', clubAdminMiddleware, async (req, res) => {
    try {
        const courtId = req.params.id;
        const deleted = await CourtService.deleteCourt(courtId);
        if (!deleted) {
            return res.status(404).json({ error: 'Court not found' }); 
        }
        res.json({ message: 'Court deleted' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});




// ... other imports 

// 1. Get a List of Coaches at a Club
router.get('/clubs/:clubId/coaches', async (req, res) => {
    try {
        const clubId = req.params.clubId; 
        const coaches = await CoachService.getCoachesByClub(clubId); 
        res.json(coaches);
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 2. Get Details of a Specific Coach
router.get('/coaches/:id', async (req, res) => {
    try {
        const coachId = req.params.id; 
        const coach = await CoachService.getCoachById(coachId);
        if (!coach) {
            return res.status(404).json({ error: 'Coach not found' }); 
        }
        res.json(coach); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 3. Create a New Coach Profile (Either self-registration or Club Admin)
router.post('/coaches', async (req, res) => { 
    try {
        const coachData = req.body;
        const newCoach = await CoachService.createCoachProfile(coachData);
        res.status(201).json(newCoach); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 4. Update Coach Details 
router.put('/coaches/:id', authMiddleware, async (req, res) => { // authMiddleware to check if coach is updating their own profile
    try {
        const coachId = req.params.id;
        const updatedCoachData = req.body; 
        const updatedCoach = await CoachService.updateCoachProfile(coachId, updatedCoachData); 
        if (!updatedCoach) {
            return res.status(404).json({ error: 'Coach not found' }); 
        }
        res.json(updatedCoach); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 5. Delete a Coach Profile (Admin Functionality, or Coach deleting own profile)
router.delete('/coaches/:id', authMiddleware, async (req, res) => { 
    try {
        const coachId = req.params.id;
        const deleted = await CoachService.deleteCoachProfile(coachId);
        if (!deleted) {
            return res.status(404).json({ error: 'Coach not found' }); 
        }
        res.json({ message: 'Coach profile deleted' }); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});




// ... other imports 

// 1. Get User Profile (Requires Authentication)
router.get('/users/me', authMiddleware, async (req, res) => {
    try {
        const userId = req.user.id; // Assuming user details are available in req.user after authentication
        const user = await UserService.getUserById(userId);
        if (!user) {
            return res.status(404).json({ error: 'User not found' }); 
        }
        res.json(user); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Update User Profile 
router.put('/users/me', authMiddleware, async (req, res) => {
    try {
        const userId = req.user.id; 
        const updatedUserData = req.body;
        const updatedUser = await UserService.updateUser(userId, updatedUserData);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' }); 
        }
        res.json(updatedUser); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 3. Get User Bookings (Court Bookings, Coaching Sessions, Clinic Registrations)
router.get('/users/me/bookings', authMiddleware, async (req, res) => {
    try {
        const userId = req.user.id;
        const bookings = await UserService.getUserBookings(userId);
        res.json(bookings);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});





// ... other imports 

// --- Court Bookings ---

// 1. Book a Court
router.post('/clubs/:clubId/courts/:courtId/bookings', authMiddleware, async (req, res) => {
    try {
        const clubId = req.params.clubId;
        const courtId = req.params.courtId; 
        const bookingData = req.body; 
        const newBooking = await BookingService.createCourtBooking(clubId, courtId, req.user.id, bookingData); 
        res.status(201).json(newBooking);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get Court Booking Details
router.get('/bookings/:id', authMiddleware, async (req, res) => {
    try {
        const bookingId = req.params.id;
        const booking = await BookingService.getCourtBookingById(bookingId); 
        if (!booking) {
            return res.status(404).json({ error: 'Booking not found' }); 
        }
        // Authorization check - Ensure the current user is associated with the booking 
        if (booking.user_id !== req.user.id) {
            return res.status(403).json({ error: 'Unauthorized access' }); 
        }
        res.json(booking); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 3. Update Court Booking
router.put('/bookings/:id', authMiddleware, async (req, res) => {
    try {
        const bookingId = req.params.id;
        const updatedBookingData = req.body; 
        const updatedBooking = await BookingService.updateCourtBooking(bookingId, req.user.id, updatedBookingData);
        if (!updatedBooking) {
            return res.status(404).json({ error: 'Booking not found' });
        }
        res.json(updatedBooking);
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Cancel Court Booking
router.delete('/bookings/:id', authMiddleware, async (req, res) => {
    try {
        const bookingId = req.params.id;
        const deleted = await BookingService.cancelCourtBooking(bookingId, req.user.id);
        if (!deleted) {
            return res.status(404).json({ error: 'Booking not found' }); 
        }
        res.json({ message: 'Booking cancelled' }); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// --- Coaching Sessions --- 

// 1. Book a Coaching Session
router.post('/coaches/:coachId/bookings', authMiddleware, async (req, res) => {
    try {
        const coachId = req.params.coachId;
        const bookingData = req.body; 
        const newBooking = await BookingService.createCoachingBooking(coachId, req.user.id, bookingData);
        res.status(201).json(newBooking); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 2. (Get Coaching Session Details, Update, Cancel) - Implement similar routes as Court Booking

// --- Clinics/Programs --- 

// 1. Register for a Clinic/Program
router.post('/clinics/:clinicId/bookings', authMiddleware, async (req, res) => {
    try {
        const clinicId = req.params.clinicId;
        const newBooking = await BookingService.createClinicBooking(clinicId, req.user.id); 
        res.status(201).json(newBooking); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. (Get Clinic Registration Details, Update, Cancel) - Implement similar routes as Court Booking



// ... other imports (including admin authentication middleware) 

// 1. Get All Users
router.get('/admin/users', adminMiddleware, async (req, res) => {
    try {
        const users = await AdminService.getAllUsers();
        res.json(users);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get a Specific User
router.get('/admin/users/:id', adminMiddleware, async (req, res) => {
    try {
        const userId = req.params.id;
        const user = await AdminService.getUserById(userId);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        res.json(user); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 3. Update a User (e.g., Change roles, disable accounts)
router.put('/admin/users/:id', adminMiddleware, async (req, res) => {
    try {
        const userId = req.params.id;
        const updatedUserData = req.body; 
        const updatedUser = await AdminService.updateUser(userId, updatedUserData);
        if (!updatedUser) {
            return res.status(404).json({ error: 'User not found' }); 
        }
        res.json(updatedUser); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Delete a User
router.delete('/admin/users/:id', adminMiddleware, async (req, res) => { 
    try {
        const userId = req.params.id;
        const deleted = await AdminService.deleteUser(userId);
        if (!deleted) {
            return res.status(404).json({ error: 'User not found' }); 
        }
        res.json({ message: 'User deleted' });
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 5. Get System-wide Statistics
router.get('/admin/statistics', adminMiddleware, async (req, res) => {
    try {
        const stats = await AdminService.getStatistics();
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// ... Other Admin APIs (e.g., managing custom requests, support tickets, etc.)




// ... other imports

// 1. Create a Find-a-Buddy Request
router.post('/clubs/:clubId/find-a-buddy', authMiddleware, async (req, res) => {
    try {
        const clubId = req.params.clubId;
        const requestData = req.body;
        const newRequest = await BuddyService.createRequest(clubId, req.user.id, requestData);
        res.status(201).json(newRequest);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get All Find-a-Buddy Requests at a Club (Filterable)
router.get('/clubs/:clubId/find-a-buddy', async (req, res) => {
    try {
        const clubId = req.params.clubId;
        const filterOptions = req.query; // e.g., sport, level, time
        const requests = await BuddyService.getRequests(clubId, filterOptions);
        res.json(requests);
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 3. Get Details of a Find-a-Buddy Request
router.get('/find-a-buddy/:id', async (req, res) => {
    try {
        const requestId = req.params.id; 
        const request = await BuddyService.getRequestById(requestId);
        if (!request) {
            return res.status(404).json({ error: 'Request not found' }); 
        }
        res.json(request); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Join a Find-a-Buddy Request
router.post('/find-a-buddy/:id/join', authMiddleware, async (req, res) => {
    try {
        const requestId = req.params.id; 
        const success = await BuddyService.joinRequest(requestId, req.user.id);
        if (!success) {
            return res.status(400).json({ error: 'Unable to join request' }); // May be full, closed, etc.
        }
        res.json({ message: 'Joined request successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 5. Leave a Find-a-Buddy Request 
router.post('/find-a-buddy/:id/leave', authMiddleware, async (req, res) => { 
    // ... Implementation similar to join
});

// 6. Update a Find-a-Buddy Request (Auth to check if request creator)
router.put('/find-a-buddy/:id', authMiddleware, async (req, res) => { 
    // ... Implementation to update details
});

// 7. Delete a Find-a-Buddy Request (Auth to check if request creator)
router.delete('/find-a-buddy/:id', authMiddleware, async (req, res) => { 
    // ... Implementation to delete
});




// ... other imports

// 1. Create a Group
router.post('/groups', authMiddleware, async (req, res) => {
    try {
        const groupData = req.body; 
        const newGroup = await GroupService.createGroup(req.user.id, groupData);
        res.status(201).json(newGroup); 
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 2. Get Groups for User
router.get('/users/me/groups', authMiddleware, async (req, res) => {
    try {
        const groups = await GroupService.getGroupsForUser(req.user.id);
        res.json(groups);
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 3. Get Details of a Group 
router.get('/groups/:id', authMiddleware, async (req, res) => {
    try {
        const groupId = req.params.id;
        const group = await GroupService.getGroupById(groupId);
        if (!group) {
            return res.status(404).json({ error: 'Group not found' }); 
        }
        // Authorization check - Make sure user is a member of this group or is an admin
        if (!GroupService.isUserInGroup(req.user.id, group.members) && !req.user.isAdmin) { 
            return res.status(403).json({ error: 'Unauthorized access' });
        }
        res.json(group); 
    } catch (error) {
        res.status(500).json({ error: error.message }); 
    }
});

// 4. Add a Member to Group (Auth to check if group creator or admin)
router.post('/groups/:id/members', authMiddleware, async (req, res) => { 
    // ... Implementation to add a member (user ID)
});

// 5. Remove a Member from Group (Auth to check if group creator or admin)
router.delete('/groups/:id/members/:memberId', authMiddleware, async (req, res) => { 
    // ... Implementation to remove a member
});

// 6. Update a Group (Auth to check if group creator or admin)
router.put('/groups/:id', authMiddleware, async (req, res) => { 
    // ... Implementation to update group details
});

// 7. Delete a Group (Auth to check if group creator or admin)
router.delete('/groups/:id', authMiddleware, async (req, res) => { 
    // ... Implementation to delete
});

module.exports = router;