const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const { check_staff_permission } = require("../utils/util.js");
const StripeService = require("../../../services/StripeService");
const stripe = new StripeService();

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "staff" }),
];

const base = "/v3/api/custom/courtmatchup/staff/billing";

module.exports = function (app) {
  // Permission middleware helper
  const permissionCheck = (permission) => {
    return async (req, res, next) => {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        
        // const hasPermission = await check_staff_permission(sdk, req.user_id, permission);
        
        // if (!hasPermission) {
        //   return res.status(403).json({
        //     error: true,
        //     message: `You don't have permission to access this feature`
        //   });
        // }
        
        // Add the staff profile to the request
        sdk.setTable("staff");
        const staff = await sdk.get({
          user_id: req.user_id,
        });
        
        if (!staff.length) {
          return res.status(403).json({
            error: true,
            message: "Staff profile not found",
          });
        }
        
        req.staff_profile = staff[0];
        req.club_id = staff[0].club_id;
        
        next();
      } catch (err) {
        return res.status(403).json({
          error: true,
          message: err.message
        });
      }
    };
  };

  // Get all invoices
  app.get(`${base}/invoices`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { 
        status, 
        date_start, 
        date_end, 
        first_name,
        last_name,
        email,
        user_id,
        limit = 20,
        offset = 0,
        type
      } = req.query;
      
      // Build SQL query with filters
      let sql = `
        SELECT 
          b.id AS invoice_id,
          b.payment_intent,
          b.price AS amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.date AS invoice_date,
          b.create_at,
          b.status,
          b.reservation_type,
          b.last_4,
          b.user_id AS customer_id,
          u.first_name AS customer_first_name,
          u.last_name AS customer_last_name,
          u.email AS customer_email,
          
          (COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0)) AS total_amount,
          
          s.name AS sport_name,
          b.type AS booking_type,
          b.subtype AS booking_subtype,
          
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS invoice_type
        FROM 
          courtmatchup_booking b
        JOIN 
          courtmatchup_user u ON b.user_id = u.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.payment_intent IS NOT NULL
      `;
      
      // Add filters
      if (status) {
        sql += ` AND b.status = ${parseInt(status)}`;
      }
      
      if (date_start && date_end) {
        sql += ` AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'`;
      }
      
      if (user_id) {
        sql += ` AND b.user_id = ${parseInt(user_id)}`;
      }
      
      if (type) {
        sql += ` AND b.reservation_type = ${parseInt(type)}`;
      }

      if (first_name) {
        sql += ` AND u.first_name LIKE '%${first_name}%'`;
      }

      if (last_name) {
        sql += ` AND u.last_name LIKE '%${last_name}%'`;
      }

      if (email) {
        sql += ` AND u.email LIKE '%${email}%'`;
      }
      
      // Count total
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_query`;
      const totalCount = await sdk.rawQuery(countSql);
      
      // Add limit and offset
      sql += ` ORDER BY b.create_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
      
      // Execute query
      const invoices = await sdk.rawQuery(sql);
      
      // Calculate summary stats
      const summarySQL = `
        SELECT 
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS total_revenue,
          COUNT(*) AS total_invoices,
          COUNT(DISTINCT b.user_id) AS unique_customers
        FROM 
          courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.payment_intent IS NOT NULL
          ${status ? ` AND b.status = ${parseInt(status)}` : ''}
          ${date_start && date_end ? ` AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
          ${user_id ? ` AND b.user_id = ${parseInt(user_id)}` : ''}
          ${type ? ` AND b.reservation_type = ${parseInt(type)}` : ''}
          ${first_name ? ` AND u.first_name LIKE '%${first_name}%'` : ''}
          ${last_name ? ` AND u.last_name LIKE '%${last_name}%'` : ''}
          ${email ? ` AND u.email LIKE '%${email}%'` : ''}
      `;
      
      const summary = await sdk.rawQuery(summarySQL);
      
      return res.status(200).json({
        error: false,
        invoices,
        count: totalCount[0].total,
        summary: summary[0],
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          total: totalCount[0].total,
          pages: Math.ceil(totalCount[0].total / parseInt(limit))
        }
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get invoice details
  app.get(`${base}/invoices/:id`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const invoiceId = req.params.id;
      
      if (!invoiceId) {
        return res.status(400).json({
          error: true,
          message: "Invoice ID is required"
        });
      }
      
      // Get invoice details
      const sql = `
        SELECT 
          b.id AS invoice_id,
          b.payment_intent,
          b.price AS amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.date AS invoice_date,
          b.start_time,
          b.end_time,
          b.duration,
          b.create_at,
          b.status,
          b.reservation_type,
          b.last_4,
          b.notes,
          b.custom_request,
          b.player_ids,
          
          b.user_id AS customer_id,
          u.first_name AS customer_first_name,
          u.last_name AS customer_last_name,
          u.email AS customer_email,
          u.phone AS customer_phone,
          
          (COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0)) AS total_amount,
          
          s.name AS sport_name,
          b.type AS booking_type,
          b.subtype AS booking_subtype,
          
          cc.name AS court_name,
          
          coach_user.first_name AS coach_first_name,
          coach_user.last_name AS coach_last_name,
          
          c.name AS clinic_name,
          
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS invoice_type
        FROM 
          courtmatchup_booking b
        JOIN 
          courtmatchup_user u ON b.user_id = u.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_club_court cc ON b.court_id = cc.id
        LEFT JOIN
          courtmatchup_coach coach ON b.coach_id = coach.id
        LEFT JOIN
          courtmatchup_user coach_user ON coach.user_id = coach_user.id
        LEFT JOIN
          courtmatchup_clinics c ON b.clinic_id = c.id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.id = ${parseInt(invoiceId)}
      `;
      
      const invoice = await sdk.rawQuery(sql);
      
      if (!invoice.length) {
        return res.status(404).json({
          error: true,
          message: "Invoice not found"
        });
      }
      
      // Get reservations associated with this booking
      const reservationSql = `
        SELECT 
          r.id,
          r.user_id,
          u.first_name,
          u.last_name,
          u.email,
          r.status,
          r.create_at
        FROM 
          courtmatchup_reservation r
        JOIN
          courtmatchup_user u ON r.user_id = u.id
        WHERE 
          r.booking_id = ${parseInt(invoiceId)}
      `;
      
      const reservations = await sdk.rawQuery(reservationSql);
      
      // Get payment history from Stripe if we have a payment intent
      let paymentHistory = [];
      if (invoice[0].payment_intent && invoice[0].payment_intent.startsWith('pi_')) {
        try {
          const stripePayment = await stripe.retrievePaymentIntent(invoice[0].payment_intent);
          if (stripePayment) {
            paymentHistory = [{
              amount: stripePayment.amount,
              status: stripePayment.status,
              payment_method: stripePayment.payment_method_types[0],
              created: new Date(stripePayment.created * 1000).toISOString(),
              currency: stripePayment.currency,
              receipt_url: stripePayment.charges?.data[0]?.receipt_url
            }];
          }
        } catch (stripeErr) {
          console.error("Error fetching Stripe payment:", stripeErr);
        }
      }
      
      return res.status(200).json({
        error: false,
        invoice: invoice[0],
        reservations,
        payment_history: paymentHistory
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Create a manual invoice
  app.post(`${base}/invoices`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const {
        user_id,
        sport_id,
        type,
        subtype,
        date,
        start_time,
        end_time,
        duration,
        court_id,
        coach_id,
        clinic_id,
        price = 0,
        club_fee = 0,
        service_fee = 0,
        coach_fee = 0,
        clinic_fee = 0,
        court_fee = 0,
        notes,
        reservation_type,
        player_ids
      } = req.body;
      
      if (!user_id || !date) {
        return res.status(400).json({
          error: true,
          message: "User ID and date are required"
        });
      }
      
      // Validate user exists
      sdk.setTable("user");
      const user = await sdk.get({ id: user_id });
      if (!user.length) {
        return res.status(404).json({
          error: true,
          message: "User not found"
        });
      }
      
      // Create booking entry (invoice)
      sdk.setTable("booking");
      const invoiceId = await sdk.insert(filterEmptyFields({
        user_id,
        club_id,
        sport_id,
        type,
        subtype,
        date: sqlDateFormat(new Date(date)),
        start_time,
        end_time,
        duration,
        court_id,
        coach_id,
        clinic_id,
        price,
        club_fee,
        service_fee,
        coach_fee,
        clinic_fee,
        court_fee,
        notes,
        reservation_type,
        player_ids: player_ids ? JSON.stringify(player_ids) : null,
        status: 1, // Success status
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      }));
      
      // Create reservation for the user
      sdk.setTable("reservation");
      await sdk.insert({
        user_id,
        booking_id: invoiceId,
        status: 1, // Confirmed
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
      
      // If there are additional players, create reservations for them too
      if (player_ids && Array.isArray(player_ids) && player_ids.length > 0) {
        for (const playerId of player_ids) {
          if (playerId !== user_id) {
            await sdk.insert({
              user_id: playerId,
              booking_id: invoiceId,
              status: 1, // Confirmed
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            });
          }
        }
      }
      
      return res.status(200).json({
        error: false,
        message: "Invoice created successfully",
        invoice_id: invoiceId
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Update invoice status
  app.post(`${base}/invoices/:id/status`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const invoiceId = req.params.id;
      const { status } = req.body;
      
      if (!invoiceId || status === undefined) {
        return res.status(400).json({
          error: true,
          message: "Invoice ID and status are required"
        });
      }
      
      // Check if invoice exists and belongs to this club
      sdk.setTable("booking");
      const invoice = await sdk.get({
        id: invoiceId,
        club_id
      });
      
      if (!invoice.length) {
        return res.status(404).json({
          error: true,
          message: "Invoice not found or does not belong to this club"
        });
      }
      
      // Update status
      await sdk.update({
        status,
        update_at: sqlDateTimeFormat(new Date())
      }, invoiceId);
      
      return res.status(200).json({
        error: false,
        message: "Invoice status updated successfully"
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Generate financial report
  app.get(`${base}/reports/financial`, middlewares, permissionCheck('invoicing_bank'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { 
        date_start, 
        date_end, 
        group_by = 'day' // day, week, month
      } = req.query;
      
      if (!date_start || !date_end) {
        return res.status(400).json({
          error: true,
          message: "Start and end dates are required"
        });
      }
      
      // Determine grouping format
      let timeFormat;
      if (group_by === 'month') {
        timeFormat = '%Y-%m';
      } else if (group_by === 'week') {
        timeFormat = '%Y-%u'; // Year-Week number
      } else {
        timeFormat = '%Y-%m-%d';
      }
      
      // Revenue by time period
      const revenueSql = `
        SELECT 
          DATE_FORMAT(date, '${timeFormat}') AS period,
          COUNT(*) AS invoice_count,
          SUM(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS total_revenue,
          SUM(COALESCE(price, 0)) AS base_revenue,
          SUM(COALESCE(club_fee, 0)) AS club_fees,
          SUM(COALESCE(service_fee, 0)) AS service_fees,
          SUM(COALESCE(coach_fee, 0)) AS coach_fees,
          SUM(COALESCE(clinic_fee, 0)) AS clinic_fees,
          SUM(COALESCE(court_fee, 0)) AS court_fees
        FROM 
          courtmatchup_booking
        WHERE 
          club_id = ${club_id}
          AND date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND status = 1
        GROUP BY period
        ORDER BY period
      `;
      
      const revenue = await sdk.rawQuery(revenueSql);
      
      // Revenue by type
      const typeRevenueSql = `
        SELECT 
          CASE
            WHEN reservation_type = 1 THEN 'Club Court'
            WHEN reservation_type = 2 THEN 'Clinic'
            WHEN reservation_type = 3 THEN 'Lesson'
            WHEN reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS type,
          COUNT(*) AS invoice_count,
          SUM(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS total_revenue
        FROM 
          courtmatchup_booking
        WHERE 
          club_id = ${club_id}
          AND date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND status = 1
        GROUP BY reservation_type
        ORDER BY total_revenue DESC
      `;
      
      const revenueByType = await sdk.rawQuery(typeRevenueSql);
      
      // Court utilization
      const courtSql = `
        SELECT 
          cc.id,
          cc.name,
          COUNT(b.id) AS booking_count,
          SUM(b.duration) AS hours_booked,
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS revenue
        FROM 
          courtmatchup_club_court cc
        LEFT JOIN
          courtmatchup_booking b ON cc.id = b.court_id AND b.status = 1
          AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
        WHERE 
          cc.club_id = ${club_id}
        GROUP BY cc.id
        ORDER BY revenue DESC
      `;
      
      const courtUtilization = await sdk.rawQuery(courtSql);
      
      // Summary statistics
      const summarySql = `
        SELECT 
          COUNT(*) AS total_invoices,
          COUNT(DISTINCT user_id) AS unique_customers,
          SUM(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS total_revenue,
          AVG(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS average_invoice_value
        FROM 
          courtmatchup_booking
        WHERE 
          club_id = ${club_id}
          AND date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND status = 1
      `;
      
      const summary = await sdk.rawQuery(summarySql);
      
      // Get subscription revenue
      const subscriptionRevenueSql = `
        SELECT 
          DATE_FORMAT(s.current_period_start, '${timeFormat}') AS period,
          SUM(s.amount) AS revenue
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.club_id = ${club_id}
          AND s.status = 'active'
          AND s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
        GROUP BY 
          period
        ORDER BY 
          MIN(s.current_period_start)
      `;
      
      const subscriptionRevenue = await sdk.rawQuery(subscriptionRevenueSql);
      
      return res.status(200).json({
        error: false,
        revenue_by_period: revenue,
        subscription_revenue: subscriptionRevenue,
        revenue_by_type: revenueByType,
        court_utilization: courtUtilization,
        summary: summary[0],
        period: {
          start: date_start,
          end: date_end,
          group_by
        }
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get outstanding payments
  app.get(`${base}/outstanding`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      
      // Query for pending payments
      const pendingSql = `
        SELECT 
          b.id AS invoice_id,
          b.payment_intent,
          b.price AS amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.date AS invoice_date,
          b.create_at,
          b.status,
          
          b.user_id AS customer_id,
          u.first_name AS customer_first_name,
          u.last_name AS customer_last_name,
          u.email AS customer_email,
          
          (COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0)) AS total_amount,
          
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS invoice_type,
          
          DATEDIFF(CURDATE(), b.date) AS days_outstanding
        FROM 
          courtmatchup_booking b
        JOIN 
          courtmatchup_user u ON b.user_id = u.id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.status = 0  -- Pending status
        ORDER BY b.date ASC
      `;
      
      const pendingPayments = await sdk.rawQuery(pendingSql);
      
      // Count by age buckets
      const agingSql = `
        SELECT 
          CASE
            WHEN DATEDIFF(CURDATE(), date) <= 30 THEN '0-30 days'
            WHEN DATEDIFF(CURDATE(), date) <= 60 THEN '31-60 days'
            WHEN DATEDIFF(CURDATE(), date) <= 90 THEN '61-90 days'
            ELSE 'Over 90 days'
          END AS aging_bucket,
          COUNT(*) AS invoice_count,
          SUM(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS total_amount
        FROM 
          courtmatchup_booking
        WHERE 
          club_id = ${club_id}
          AND status = 0  -- Pending status
        GROUP BY aging_bucket
        ORDER BY 
          CASE 
            WHEN aging_bucket = '0-30 days' THEN 1
            WHEN aging_bucket = '31-60 days' THEN 2
            WHEN aging_bucket = '61-90 days' THEN 3
            ELSE 4
          END
      `;
      
      const agingReport = await sdk.rawQuery(agingSql);
      
      // Summary
      const summarySql = `
        SELECT 
          COUNT(*) AS total_outstanding,
          SUM(COALESCE(price, 0) + 
              COALESCE(club_fee, 0) + 
              COALESCE(service_fee, 0) + 
              COALESCE(coach_fee, 0) + 
              COALESCE(clinic_fee, 0) +
              COALESCE(court_fee, 0)) AS total_amount
        FROM 
          courtmatchup_booking
        WHERE 
          club_id = ${club_id}
          AND status = 0  -- Pending status
      `;
      
      const summary = await sdk.rawQuery(summarySql);
      
      return res.status(200).json({
        error: false,
        outstanding_invoices: pendingPayments,
        aging_report: agingReport,
        summary: summary[0]
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get billing data grouped by users
  app.get(`${base}/invoices/by-user`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { 
        date_start, 
        date_end,
        limit = 50,
        offset = 0 
      } = req.query;
      
      // Date validation
      if (date_start && date_end) {
        const startDate = new Date(date_start);
        const endDate = new Date(date_end);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return res.status(400).json({
            error: true,
            message: "Invalid date format. Use YYYY-MM-DD"
          });
        }
      }
      
      // First get all users who have made bookings at this club
      const usersSql = `
        SELECT DISTINCT 
          u.id AS user_id,
          u.first_name,
          u.last_name,
          u.email,
          u.photo,
          u.phone,
          COUNT(b.id) AS total_bookings,
          SUM(
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0)
          ) AS total_spent,
          MAX(b.date) AS latest_booking_date
        FROM 
          courtmatchup_user u
        JOIN 
          courtmatchup_booking b ON u.id = b.user_id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          ${date_start && date_end ? 
            `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
        GROUP BY 
          u.id, u.first_name, u.last_name, u.email, u.photo, u.phone
        ORDER BY 
          total_spent DESC
        LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
      `;
      
      const usersWithBookings = await sdk.rawQuery(usersSql);
      
      // Get total count without limit/offset
      const countSql = `
        SELECT 
          COUNT(DISTINCT u.id) AS total_users
        FROM 
          courtmatchup_user u
        JOIN 
          courtmatchup_booking b ON u.id = b.user_id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          ${date_start && date_end ? 
            `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
      `;
      
      const countResult = await sdk.rawQuery(countSql);
      const totalUsers = countResult[0].total_users;
      
      // For each user, get their booking type breakdown
      for (const user of usersWithBookings) {
        const bookingTypesSql = `
          SELECT 
            CASE
              WHEN b.reservation_type = 1 THEN 'Club Court'
              WHEN b.reservation_type = 2 THEN 'Clinic'
              WHEN b.reservation_type = 3 THEN 'Lesson'
              WHEN b.reservation_type = 4 THEN 'Coach'
              ELSE 'Other'
            END AS booking_type,
            COUNT(b.id) AS count,
            SUM(
              COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)
            ) AS type_total
          FROM 
            courtmatchup_booking b
          JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
          WHERE 
            r.club_id = ${club_id}
            AND b.user_id = ${user.user_id}
            ${date_start && date_end ? 
              `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
          GROUP BY 
            booking_type
        `;
        
        user.booking_types = await sdk.rawQuery(bookingTypesSql);
        
        // Get payment status breakdown
        const paymentStatusSql = `
          SELECT 
            b.status,
            COUNT(b.id) AS count,
            SUM(
              COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)
            ) AS status_total
          FROM 
            courtmatchup_booking b
          JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
          WHERE 
            r.club_id = ${club_id}
            AND b.user_id = ${user.user_id}
            ${date_start && date_end ? 
              `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
          GROUP BY 
            b.status
        `;
        
        user.payment_status = await sdk.rawQuery(paymentStatusSql);
        
        // Get subscriptions for this user
        const userSubscriptionsSql = `
          SELECT 
            s.id,
            s.stripe_id,
            s.price_id,
            s.status,
            s.create_at,
            s.current_period_start,
            s.current_period_end,
            s.plan_id,
            s.price_type,
            s.amount,
            s.interval,
            s.interval_count,
            p.name AS plan_name
          FROM 
            courtmatchup_stripe_subscription s
          LEFT JOIN
            courtmatchup_plans p ON s.plan_id = p.id
          WHERE 
            s.club_id = ${club_id}
            AND s.user_id = ${user.user_id}
        `;
        
        user.subscriptions = await sdk.rawQuery(userSubscriptionsSql);
        
        // Add active subscription status
        user.has_active_subscription = user.subscriptions.some(sub => sub.status === 'active');
      }
      
      // Get summary statistics
      const summarySql = `
        SELECT 
          COUNT(DISTINCT b.user_id) AS total_unique_customers,
          COUNT(b.id) AS total_bookings,
          SUM(
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0)
          ) AS total_revenue,
          AVG(
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0)
          ) AS average_booking_value,
          SUM(CASE WHEN b.status = 1 THEN 1 ELSE 0 END) AS completed_bookings,
          SUM(CASE WHEN b.status = 0 THEN 1 ELSE 0 END) AS pending_bookings
        FROM 
          courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          ${date_start && date_end ? 
            `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
      `;
      
      const summary = await sdk.rawQuery(summarySql);
      
      // Get top spenders
      const topSpendersSql = `
        SELECT 
          u.id AS user_id,
          u.first_name,
          u.last_name,
          u.email,
          COUNT(b.id) AS booking_count,
          SUM(
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0)
          ) AS total_spent
        FROM 
          courtmatchup_user u
        JOIN 
          courtmatchup_booking b ON u.id = b.user_id
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          ${date_start && date_end ? 
            `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
        GROUP BY 
          u.id, u.first_name, u.last_name, u.email
        ORDER BY 
          total_spent DESC
        LIMIT 10
      `;
      
      const topSpenders = await sdk.rawQuery(topSpendersSql);
      
      return res.status(200).json({
        error: false,
        users: usersWithBookings,
        total_users: totalUsers,
        summary: summary[0],
        top_spenders: topSpenders
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Add subscriptions to the invoice endpoint
  app.get(`${base}/subscriptions`, middlewares, permissionCheck('invoicing'), async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { club_id } = req;
      const { 
        status, 
        date_start, 
        date_end, 
        user_id,
        limit = 20,
        offset = 0
      } = req.query;
      
      // Build SQL query with filters
      let sql = `
        SELECT 
          s.id,
          s.stripe_id,
          s.price_id,
          s.status,
          s.create_at,
          s.update_at,
          s.user_id,
          s.cancel_at,
          s.current_period_start,
          s.current_period_end,
          s.plan_id,
          s.price_type,
          s.amount,
          s.interval,
          s.interval_count,
          s.club_id,
          s.object,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          p.name AS plan_name,
          p.description AS plan_description,
          'Subscription' as invoice_type
        FROM 
          courtmatchup_stripe_subscription s
        JOIN 
          courtmatchup_user u ON s.user_id = u.id
        LEFT JOIN
          courtmatchup_plans p ON s.plan_id = p.id
        WHERE 
          s.club_id = ${club_id}
          AND s.object IS NOT NULL
      `;
      
      // Add filters
      if (status) {
        sql += ` AND s.status = '${status}'`;
      }
      
      if (date_start && date_end) {
        sql += ` AND (
          (s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
          (s.current_period_end BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
          (s.current_period_start <= '${sqlDateFormat(new Date(date_start))}' AND s.current_period_end >= '${sqlDateFormat(new Date(date_end))}')
        )`;
      }
      
      if (user_id) {
        sql += ` AND s.user_id = ${parseInt(user_id)}`;
      }
      
      // Count total
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_query`;
      const totalCount = await sdk.rawQuery(countSql);
      
      // Add limit and offset
      sql += ` ORDER BY s.create_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
      
      // Execute query
      const subscriptions = await sdk.rawQuery(sql);
      
      // Calculate summary stats
      const summarySql = `
        SELECT 
          SUM(s.amount) AS total_subscription_revenue,
          COUNT(*) AS total_subscriptions,
          COUNT(DISTINCT s.user_id) AS unique_subscribers,
          COUNT(CASE WHEN s.status = 'active' THEN 1 ELSE NULL END) AS active_subscriptions,
          COUNT(CASE WHEN s.status = 'canceled' THEN 1 ELSE NULL END) AS canceled_subscriptions
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.club_id = ${club_id}
      `;
      
      const summary = await sdk.rawQuery(summarySql);
      
      return res.status(200).json({
        error: false,
        subscriptions,
        count: totalCount[0].total,
        summary: summary[0]
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Staff Billing API - Get Invoices",
      url: "/v3/api/custom/courtmatchup/staff/billing/invoices",
      successPayload: '{"error":false,"invoices":[],"count":0,"summary":{"total_revenue":0,"total_invoices":0}}',
      needToken: true
    },
    {
      method: "GET",
      name: "Staff Billing API - Get Invoice Details",
      url: "/v3/api/custom/courtmatchup/staff/billing/invoices/:id",
      successPayload: '{"error":false,"invoice":{},"reservations":[]}',
      needToken: true
    },
    {
      method: "POST",
      name: "Staff Billing API - Create Invoice",
      url: "/v3/api/custom/courtmatchup/staff/billing/invoices",
      successPayload: '{"error":false,"message":"Invoice created successfully","invoice_id":123}',
      needToken: true
    },
    {
      method: "GET",
      name: "Staff Billing API - Financial Report",
      url: "/v3/api/custom/courtmatchup/staff/billing/reports/financial",
      successPayload: '{"error":false,"revenue_by_period":[],"revenue_by_type":[],"court_utilization":[]}',
      needToken: true
    },
    {
      method: "GET",
      name: "Staff Billing API - Get Invoices by User",
      url: "/v3/api/custom/courtmatchup/staff/billing/invoices/by-user",
      successPayload: '{"error":false,"users":[],"total_users":0,"summary":{"total_revenue":0,"total_bookings":0,"average_booking_value":0,"completed_bookings":0,"pending_bookings":0}}',
      needToken: true
    },
    {
      method: "GET",
      name: "Staff Billing API - Get Subscriptions",
      url: "/v3/api/custom/courtmatchup/staff/billing/subscriptions",
      successPayload: '{"error":false,"subscriptions":[],"count":0,"summary":{"total_subscription_revenue":0,"total_subscriptions":0,"unique_subscribers":0,"active_subscriptions":0,"canceled_subscriptions":0}}',
      needToken: true
    }
  ];
}; 