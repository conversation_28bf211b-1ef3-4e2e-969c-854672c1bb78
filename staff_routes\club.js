const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const { filterEmptyFields, sqlDateTimeFormat } = require("../../../services/UtilService");
const { filterEmptyFieldsAllowEmptyString } = require("../utils/util");
const PasswordService = require("../../../services/PasswordService");
const { validate_availability, validate_account } = require("../utils/util");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "staff" }),
];

const base = "/v3/api/custom/courtmatchup/staff/club";

module.exports = function (app) {

  // Get club data
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("staff")
      const staff = await sdk.get({
        user_id:req.user_id
      })
      if (!staff[0]) throw new Error("Staff not found")
      const club_id = staff[0].club_id

      sdk.setTable("clubs")
      const club = await sdk.get({
        id:club_id
      })

      return res.status(200).json({
        error: false,
        model: club[0] || {}
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  app.post(base + "/profile-edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("staff");
      const result = await sdk.get({
        user_id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: "Staff not found",
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      const club_id = result[0].club_id;
      

      sdk.setTable("user");

      // const updateResult = await sdk.update(filterEmptyFields({
      //   first_name: req.body.first_name,
      //   last_name: req.body.last_name,
      //   photo: req.body.photo,
      //   phone: req.body.phone,
      //   update_at: sqlDateTimeFormat(new Date()),
      // }), req.user_id);

      sdk.setTable("clubs");
      const club = (await sdk.get({
        id: club_id,
      }))[0];

      if(!club) {
        return res.status(403).json({
          error: true,
          message: "Club not found",
        });
   
      }else{
        let {
          name,
          bio,
          opening_time,
          closing_time,
          times,
          title,
          description,
          days_off,
          daily_breaks,
          splash_screen,
          show_clinic,
          show_buddy,
          show_coach,
          completed,
          slug,
          show_groups,
          show_court,
          club_logo,
          show_notification,
          home_image,
          buddy_description,
          court_description,
          coach_description,
          fee_settings,
          sports,
          clinic_description,
          lesson_description,
          service_fee,
          custom_request_threshold,
          max_players,
          account_details,
          club_location,
          account_settings,
          exceptions,
          sport_ids = [],
          pricing = [],
          membership_settings,
          courts
        } = req.body;

        if (name) {
          name = name.trim();
          sdk.setTable('clubs');
          const clubs = await sdk.get({
            name
          });
          if (clubs.length > 0) {
            return res.status(403).json({
              error: true,
              message: "Name already exists"
            });
          }
        }
        
        if (account_details) validate_account(account_details);
        // if (availability) validate_availability(availability);
        sdk.setTable('clubs');
        await sdk.update(filterEmptyFields({
          user_id: req.user_id,
          name,
          bio,
          opening_time,
          closing_time,
          service_fee,
          splash_screen,
          show_clinic,
          completed,
          show_buddy,
          slug,
          times: times ? JSON.stringify( times ) : null,
          fee_settings: fee_settings ? JSON.stringify( fee_settings || []) : null,
          show_coach,
          title,
          description,
          days_off: days_off ? JSON.stringify( days_off || []) : null,
          daily_breaks: daily_breaks ? JSON.stringify( daily_breaks || []) : null,
          club_logo,
          show_notification,
          home_image,
          show_groups,
          show_court,
          buddy_description,
          court_description,
          club_location,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details: account_details ?  JSON.stringify( account_details) : null,
          account_settings:account_settings ?   JSON.stringify( account_settings) : null,
          membership_settings:membership_settings ?   JSON.stringify( membership_settings) : null,
          exceptions: exceptions ?  JSON.stringify( exceptions) : null,
          update_at: sqlDateTimeFormat(new Date())
        }),club.id);
        const club_id = club.id;

        if(sport_ids && sport_ids.length > 0) {
          sdk.setTable('club_sports');
          for (const sport of sport_ids) {
            const { type, price, sport_id, club_sport_id } = sport
            if(club_sport_id){
              await sdk.update(filterEmptyFields({
                club_id,
                sport_id,
                type,
                price
              }), club_sport_id)
            }else{
              await sdk.insert(filterEmptyFields({
                club_id,
                sport_id,
                type,
                price
              }))
            }
          }
        }

        if (sports && sports.length > 0) {
          for (const sport of sports) {
            let { name, types = [], sport_id } = sport;
            sdk.setTable("sports");
        
            if (sport_id) {
              await sdk.update(
                filterEmptyFieldsAllowEmptyString({
                  name,
                  allow_cancel_reservation: allow_cancel_reservation ? 1 : 0,
                  cancel_hours_before,
                  update_at: sqlDateTimeFormat(new Date()),
                }),
                sport_id
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype, club_sport_type_id } = type;    
                  return club_sport_type_id ? sdk.update(
                    filterEmptyFieldsAllowEmptyString({
                      subtype: subtype ? JSON.stringify(subtype) : null,
                      name,
                      update_at: sqlDateTimeFormat(new Date()),
                    }),
                    club_sport_type_id
                  ) : sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: subtype ? JSON.stringify(subtype) : null,
                    })
                  );
                })
              );
            } else {
              if (!club_id) throw new Error("club_id is undefined");
        
              sport_id = await sdk.insert(
                filterEmptyFieldsAllowEmptyString({
                  club_id,
                  name,
                })
              );
        
              sdk.setTable("club_sport_type");
        
              await Promise.all(
                types.map(async (type) => {
                  const { name, sub_type:subtype = [] } = type;
                  return sdk.insert(
                    filterEmptyFieldsAllowEmptyString({
                      club_id,
                      sport_id,
                      name,
                      subtype: JSON.stringify(subtype),
                    })
                  );
                })
              );
            }
          }
        }
        


        sdk.setTable('club_pricing');
        if (pricing && pricing.length){
          for (const price of pricing) {
            const { sport_id, type, sub_type:subtype, price_by_hours, club_pricing_id, is_general, general_rate, lesson_club_fixed_amount, lesson_club_percentage, lesson_pricing_type, is_lesson } = price;
            if(club_pricing_id){
              await sdk.update(filterEmptyFields({
                sport_id,
                type,
                subtype,
                lesson_club_fixed_amount,
                lesson_club_percentage,
                is_lesson: is_lesson,
                lesson_pricing_type,
                price_by_hours: price_by_hours ?  JSON.stringify(price_by_hours) : null,
                is_general,
                general_rate
              }), club_pricing_id)
            }else{
              await sdk.insert(filterEmptyFields({
                sport_id,
                type,
                club_id,
                subtype,
                lesson_club_fixed_amount,
                is_lesson: is_lesson ? 1 : 0,
                lesson_club_percentage,
                lesson_pricing_type,
                price_by_hours: price_by_hours ?  JSON.stringify(price_by_hours) : null,
                is_general,
                general_rate
              }))
            }
          }
        }

        
        if(courts && courts.length){
          for (const sport of courts) {
            const { availability, type, price, sport_id,name,days,start_time,end_time, start_date,end_date, court_id, court_price_id,surface_id, sub_type } = sport
            if(court_id){
              sdk.setTable('club_court');
              await sdk.update(filterEmptyFields({
                sport_id,
                name,
                type,
                availability: availability ? JSON.stringify(availability) : null,
                surface_id,
                sub_type
              }),court_id)

              if (court_price_id) {
                sdk.setTable('court_pricing');
                const price_id = await sdk.update(filterEmptyFields({
                  price,
                  days: days ? JSON.stringify(days) : null,
                  start_time,
                  end_time,
                  start_date,
                  end_date
                }),court_price_id)
              }
             
            }else{
              sdk.setTable('club_court');
              const court_id = await sdk.insert(filterEmptyFields({
                club_id,
                sport_id,
                name,
                type,
                sub_type,
                availability: availability ? JSON.stringify(availability || []) : null,
                surface_id
              }))
              sdk.setTable('court_pricing');
              const price_id = await sdk.insert(filterEmptyFields({
                court_id,
                club_id,
                price,
                days: JSON.stringify(days || []),
                start_time,
                end_time,
                start_date,
                end_date
              }))
            }
          }
        }
      }
      
      

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }




      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      console.log(err)
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
