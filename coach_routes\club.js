const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "coach" }),
];

const base = "/v3/api/custom/courtmatchup/coach/club";

module.exports = function (app) {

  // Get club data
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("coach")
      const coach = await sdk.get({
        id:req.coach_id
      })
      if (!coach[0]) throw new Error("Coach not found")
      const club_id = coach[0].club_id

      sdk.setTable("clubs")
      const club = await sdk.get({
        id:club_id
      })

      return res.status(200).json({
        error: false,
        model: club[0] || {}
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
