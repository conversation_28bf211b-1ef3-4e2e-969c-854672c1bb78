const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const { ERROR_MESSAGES } = require("../utils/constants.js");
const BookingService = require("../services/bookingService.js");
const MkdEventService = require("../../../services/MkdEventService.js");
const ManaKnightSDK = require("../../../core/ManaKnightSDK.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/groups";

module.exports = function (app) {

  // Get club data
  app.post(base + "/create", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        name,
        members=[],
        type=0
      } = req.body;

      if(!name) throw new Error(ERROR_MESSAGES.MISSING_NAME)

      sdk.setTable("user_groups")
      await sdk.insert({
        name,
        members: JSON.stringify(members),
        type,
        user_id:req.user_id,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      })
      return res.status(200).json({
        error: false,
        message: "Group created successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(base + "/edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        name,
        members=null,
        group_id
      } = req.body;


      if(!group_id) throw new Error("group id is required")

      sdk.setTable("user_groups")
      await sdk.update({
        name,
        members: members ? JSON.stringify(members) : null,
        update_at: sqlDateTimeFormat(new Date())
      }, group_id)
      return res.status(200).json({
        error: false,
        message: "Group edited successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(base + "/remove-from-group", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        member_id,
        group_id
      } = req.body;


      if(!group_id) throw new Error("group id is required")

      sdk.setTable("user_groups")
      const group = await sdk.get({
        id:group_id
      })
      if (!group[0]) throw new Error("Group not found")
      const members = JSON.parse(group[0].members || "[]");
      const newMembers = members.filter(m => m != member_id);
      await sdk.update({
        members: JSON.stringify(newMembers),
        update_at: sqlDateTimeFormat(new Date())
      }, group_id)
      return res.status(200).json({
        error: false,
        message: "Group edited successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  
  app.post(base + "/delete", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        group_id
      } = req.body;


      if(!group_id) throw new Error("group id is required")

      sdk.setTable("user_groups")
      await sdk.delete({}, group_id)
      return res.status(200).json({
        error: false,
        message: "Group deleted successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(base + "/add/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        members = [], // This can now contain both emails and user IDs
        password_login = 1
      } = req.body;

      const group_id = req.params.id;

      // Validate group exists
      sdk.setTable("user_groups");
      const group = await sdk.get({id: group_id});
      if(!group.length) {
        throw new Error("Group not found");
      }

      // Get existing members
      let existing_members = JSON.parse(group[0].members || "[]");

      // Process each member
      const invitePromises = members.map(async (member) => {
        try {
          // Check if member is a user ID (number) or email (string)
          const isEmail = typeof member === 'string' && member.includes('@');
          
          if (isEmail) {
            // Handle email invitation
            sdk.setTable("user");
            const user = await sdk.get({email: member});
            
            let invite_user_id = null;
            if(user.length > 0) {
              // User exists
              invite_user_id = user[0].id;
              
              // Check if already a member
              if(existing_members.includes(invite_user_id.toString())) {
                return {
                  identifier: member,
                  status: 'skipped',
                  message: 'Already a member'
                };
              }
            }

            // Check if invite already exists
            sdk.setTable("group_invite");
            const existing_invite = await sdk.get({
              email: member,
              group_id,
              status: "pending"
            });

            if(existing_invite.length > 0) {
              return {
                identifier: member,
                status: 'skipped',
                message: 'Invitation already pending'
              };
            }

            // Create invite record
            await sdk.insert({
              user_id: invite_user_id,
              email: member,
              group_id,
              status: "pending",
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            });

            // Send invitation email if not registered
            if (!invite_user_id) {
              // Get inviter's name for email
              sdk.setTable("user");
              const {first_name} = (await sdk.get({
                id: req.user_id 
              }))[0];

              const MKDSDK = new ManaKnightSDK();
              let project = {};

              if (config.env == "production") {
                project = require("../../../project");
              } else {
                MKDSDK.setProjectId("manaknight");
                MKDSDK.setTable("projects");
                project = (await MKDSDK.get({
                  project_id: req.projectId
                }))[0];
              }

              const eventService = new MkdEventService(sdk, req.projectId, req.headers);
              await eventService.sendMail(
                {
                  email: config.mail_user,
                  to: member,
                  from: config.from_mail,
                  link: req.body.link,
                  first_name,
                  group: group[0]?.name
                },
                "invitation"
              );
            }

            return {
              identifier: member,
              status: 'success',
              message: invite_user_id ? 'Invitation sent to registered user' : 'Invitation email sent'
            };

          } else {
            // Handle user ID
            const user_id = parseInt(member);
            
            // Check if user exists
            sdk.setTable("user");
            const user = await sdk.get({id: user_id});
            if(!user.length) {
              return {
                identifier: member,
                status: 'failed',
                message: 'User not found'
              };
            }

            // Check if already a member
            if(existing_members.includes(user_id.toString())) {
              return {
                identifier: member,
                status: 'skipped',
                message: 'Already a member'
              };
            }

            // check if family member 'guardian'
            const { guardian, family_role } = user[0];
            if(guardian === req.user_id) {
              // add to group
              existing_members.push(user_id);
              sdk.setTable("user_groups");
              await sdk.update({
                members: JSON.stringify(existing_members),
                update_at: sqlDateTimeFormat(new Date())
              }, group_id);
              return {
                identifier: member,
                status: 'family_member',
                message: 'Added to group'
              };
            }

            // Check if invite already exists
            sdk.setTable("group_invite");
            const existing_invite = await sdk.get({
              user_id,
              group_id,
              status: "pending"
            });

            if(existing_invite.length > 0) {
              return {
                identifier: member,
                status: 'skipped',
                message: 'Invitation already pending'
              };
            }

            // Create invite record
            await sdk.insert({
              user_id,
              email: user[0].email,
              group_id,
              status: "pending",
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            });

            return {
              identifier: member,
              status: 'success',
              message: 'Invitation sent to user'
            };
          }

        } catch (error) {
          return {
            identifier: member,
            status: 'failed',
            message: error.message
          };
        }
      });

      // Wait for all invites to be processed
      const results = await Promise.all(invitePromises);

      // Count results
      const summary = {
        total: results.length,
        success: results.filter(r => r.status === 'success').length,
        skipped: results.filter(r => r.status === 'skipped').length,
        failed: results.filter(r => r.status === 'failed').length,
        directly_added: results.filter(r => r.status === 'family_member').length
      };
      
      return res.status(200).json({
        error: false,
        message: `Processed ${summary.total} invites: ${summary.success} sent, ${summary.skipped} skipped, ${summary.failed} failed`,
        results,
        summary
      });

    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(base + "/invite/:id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        email,
      } = req.body;
      const group_id = req.params.id;

      // Validate group exists
      sdk.setTable("user_groups");
      const group = await sdk.get({id: group_id});
      if(!group.length) {
        throw new Error("Group not found");
      }

      // Check if user is already a member
      const existing_members = JSON.parse(group[0].members || "[]");
      
      // Check if email exists in users table
      sdk.setTable("user");
      const user = await sdk.get({email});
      
      let invite_user_id = null;
      if(user.length > 0) {
        // User exists
        invite_user_id = user[0].id;
        
        // Check if already a member
        if(existing_members.includes(invite_user_id)) {
          throw new Error("User is already a member of this group");
        }
      }

      // Check if invite already exists
      sdk.setTable("group_invite");
      const existing_invite = await sdk.get({
        email,
        group_id,
        status: "pending"
      });

      if(existing_invite.length > 0) {
        throw new Error("Invitation already sent and pending");
      }

      // Create invite record
      await sdk.insert({
        user_id: invite_user_id,
        email,
        group_id,
        status: "pending",
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Get inviter's name for email
      sdk.setTable("user");
      const {first_name} = (await sdk.get({
        id: req.user_id 
      }))[0];

      // Send invitation email
      const MKDSDK = new ManaKnightSDK();
      let project = {};

      if (config.env == "production") {
        project = require("../../../project");
      } else {
        MKDSDK.setProjectId("manaknight");
        MKDSDK.setTable("projects");
        project = (await MKDSDK.get({
          project_id: req.projectId
        }))[0];
      }

      const eventService = new MkdEventService(sdk, req.projectId, req.headers);
      await eventService.sendMail(
        {
          email: config.mail_user,
          to: email,
          from: config.from_mail,
          link: req.body.link,
          first_name,
          group: group[0]?.name
        },
        "invitation"
      );

      return res.status(200).json({
        error: false,
        message: "Invitation sent successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/pending-invites", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("user");
      const user = (await sdk.get({id: req.user_id}))[0];

      const sql = `
        SELECT 
          gi.id AS invite_id,
          gi.status,
          gi.create_at,
          g.id AS group_id,
          g.name AS group_name,
          g.type AS group_type,
          u.first_name AS inviter_first_name,
          u.last_name AS inviter_last_name,
          u.email AS inviter_email
        FROM 
          courtmatchup_group_invite gi
        LEFT JOIN
          courtmatchup_user_groups g ON gi.group_id = g.id
        LEFT JOIN
          courtmatchup_user u ON g.user_id = u.id
        WHERE 
          (gi.user_id = ${req.user_id} OR gi.email = '${user.email}')
          AND gi.status = 'pending'
      `;

      const invites = await sdk.rawQuery(sql);

      return res.status(200).json({
        error: false,
        invites
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(base + "/sent-invites", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("user");
      const user = (await sdk.get({id: req.user_id}))[0];

      const sql = `
        SELECT 
          gi.id AS invite_id,
          gi.status,
          gi.create_at,
          g.id AS group_id,
          g.name AS group_name,
          g.type AS group_type,
          u.first_name AS invitee_first_name,
          u.last_name AS invitee_last_name,
          u.email AS invitee_email
        FROM 
          courtmatchup_group_invite gi
        LEFT JOIN
          courtmatchup_user_groups g ON gi.group_id = g.id
        LEFT JOIN
          courtmatchup_user u ON gi.user_id = u.id
        WHERE 
          (g.user_id = ${req.user_id})
      `;

      const invites = await sdk.rawQuery(sql);

      return res.status(200).json({
        error: false,
        invites
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.post(base + "/invite-response", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const {
        invite_id,
        status // "accepted" or "rejected"
      } = req.body;

      if(!["accepted", "rejected"].includes(status)) {
        throw new Error("Invalid status");
      }

      // Get invite
      sdk.setTable("group_invite");
      const invite = (await sdk.get({id: invite_id}))[0];
      if(!invite) {
        throw new Error("Invite not found");
      }

      if(invite.status !== "pending") {
        throw new Error("Invite has already been processed");
      }

      // Verify the responding user matches the invite
      if(invite.user_id && invite.user_id !== req.user_id) {
        throw new Error("Unauthorized to respond to this invite");
      }
      if(!invite.user_id && invite.email) {
        sdk.setTable("user");
        const user = (await sdk.get({id: req.user_id}))[0];
        if(user.email !== invite.email) {
          throw new Error("Unauthorized to respond to this invite");
        }
      }

      // Update invite status
      sdk.setTable("group_invite");
      await sdk.update({
        status,
        update_at: sqlDateTimeFormat(new Date())
      }, invite_id);

      if(status === "accepted") {
        // Add user to group members
        sdk.setTable("user_groups");
        const group = (await sdk.get({id: invite.group_id}))[0];
        if(!group) {
          throw new Error("Group not found");
        }

        const existing_members = JSON.parse(group.members || "[]");
        existing_members.push(req.user_id);

        await sdk.update({
          members: JSON.stringify(existing_members),
          update_at: sqlDateTimeFormat(new Date())
        }, group.id);
      }

      return res.status(200).json({
        error: false,
        message: `Invitation ${status} successfully`
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true, 
        message: err.message
      });
    }
  });

  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {type} = req.query;

      const sql = `
          SELECT 
              g.id AS group_id,
              g.name AS group_name,
              g.user_id AS group_owner_id,
              g.type AS group_type,
              g.create_at,
              g.update_at,
              (
                  SELECT 
                      JSON_ARRAYAGG(
                          JSON_OBJECT(
                              'id', u.id,
                              'first_name', u.first_name,
                              'last_name', u.last_name,
                              'email', u.email,
                              'photo', u.photo,
                              'guardian', u.guardian,
                              'is_family_member', CASE WHEN u.guardian = g.user_id THEN 1 ELSE 0 END,
                              'family_role', u.family_role
                          )
                      )
                  FROM 
                      courtmatchup_user u
                  WHERE 
                      FIND_IN_SET(u.id, REPLACE(REPLACE(g.members, '[', ''), ']', ''))
              ) AS members
          FROM 
              courtmatchup_user_groups g
          WHERE 
              (
                  g.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(g.members, '[', ''), ']', '')) > 0
              )
              ${type ? `AND g.type = '${type}'` : ""}
      `;

      let groups = await sdk.rawQuery(sql);

      groups.forEach(g => {
          const members = JSON.parse(g.members || "[]");
          g.members = members;
          g.total = members.length;
      });


      return res.status(200).json({
        error: false,
        groups,
        mapping: {
          type: {
            0: "Friends",
            1: "Family"
          }
        }
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(base + "/:group_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {type} = req.query;
      const {group_id} = req.params

      //  it should also check if the user id is in the members array if not the main id
      const sql = `SELECT 
          g.id AS group_id,
          g.name AS group_name,
          g.user_id AS group_owner_id,
          g.type AS group_type,
          g.create_at,
          g.update_at,
          (
              SELECT 
                  JSON_ARRAYAGG(
                      JSON_OBJECT(
                          'id', u.id,
                          'first_name', u.first_name,
                          'last_name', u.first_name,
                          'email', u.email,
                          'photo', u.photo,
                          'guardian', u.guardian,
                          'family_role', u.family_role,
                          'is_family_member', CASE WHEN u.guardian = g.user_id THEN 1 ELSE 0 END
                      )
                  )
              FROM 
                  courtmatchup_user u
              WHERE 
                  FIND_IN_SET(u.id, REPLACE(REPLACE(g.members, '[', ''), ']', ''))
          ) AS members
      FROM 
          courtmatchup_user_groups g
          WHERE 
              (
                  g.user_id = ${req.user_id} OR 
                  FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(g.members, '[', ''), ']', '')) > 0
              )
              ${type ? `AND g.type = '${type}'` : ""}
          and g.id=${group_id}
          ;`

      let groups = await sdk.rawQuery(sql);
      groups.forEach(g => {
        const total = (JSON.parse(g.members || "[]")).length;
        g.members = JSON.parse(g.members || "[]");
        g.total = total
      });


      return res.status(200).json({
        error: false,
        groups,
        mapping: {
          type: {
            0: "Friends",
            1: "Family"
          }
        }
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // app.get(base, middlewares, async function (req, res) {
  //   try {
  //     let sdk = req.sdk;
  //     sdk.getDatabase();
  //     sdk.setProjectId(req.projectId);

  //     const {group_id, user_id} = req.body;
  //     sdk.setTable("user_groups")
  //     const groups = (await sdk.get({
  //       user_id
  //     }))
  //     groups.forEach(g => {
  //       const total = (JSON.parse(g.members || "[]")).length;
  //       g.total = total
  //     });

  //     return res.status(200).json({
  //       error: false,
  //       groups
  //     });
  //   } catch (err) {
  //     res.status(403);
  //     res.json({
  //       error: true,
  //       message: err.message
  //     });
  //   }
  // });
  // app.get(base, middlewares, async function (req, res) {
  //   try {
  //     let sdk = req.sdk;
  //     sdk.getDatabase();
  //     sdk.setProjectId(req.projectId);

  //     const {group_id, user_id} = req.body;
  //     sdk.setTable("user_groups")
  //     const groups = (await sdk.get({
  //       user_id
  //     }))
  //     const sql = ``

  //     return res.status(200).json({
  //       error: false,
  //       groups
  //     });
  //   } catch (err) {
  //     res.status(403);
  //     res.json({
  //       error: true,
  //       message: err.message
  //     });
  //   }
  // });



  app.post(base + "/switch-family-member", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      const { family_member_id } = req.body;

      if (!family_member_id) {
        throw new Error("Family member ID is required");
      }

      // Verify the family member exists and is in a family group with the current user
      const sql = `
        SELECT 
          g.id AS group_id,
          g.type AS group_type,
          u.id AS user_id,
          u.first_name,
          u.last_name,
          u.email,
          u.family_role,
          u.guardian,
          CASE 
            WHEN u.guardian = ${req.user_id} THEN 1
            WHEN ${req.user_id} = u.guardian THEN 2
            ELSE 0
          END as relationship_type
        FROM 
          courtmatchup_user_groups g
        JOIN 
          courtmatchup_user u ON (
            FIND_IN_SET(u.id, REPLACE(REPLACE(g.members, '[', ''), ']', ''))
            OR u.guardian = ${req.user_id}
            OR ${req.user_id} = u.guardian
          )
        WHERE 
          g.type = 1
          AND (
              g.user_id = ${req.user_id} OR 
              FIND_IN_SET(${req.user_id}, REPLACE(REPLACE(g.members, '[', ''), ']', '')) > 0
          )
          AND u.id = ${family_member_id}
      `;

      const result = await sdk.rawQuery(sql);

      if (result.length === 0) {
        throw new Error("Invalid family member or not in same family group");
      }

      const familyMember = result[0];

      // Generate a temporary token for the family member
      const token = JwtService.createAccessToken(
        {
          user_id: family_member_id,
          role: "user",
          is_family_switch: true,
          original_user_id: req.user_id,
          relationship_type: familyMember.relationship_type
        },
        config.jwt_expire,
        config.jwt_key
      );

      return res.status(200).json({
        error: false,
        token,
        family_member: {
          id: familyMember.user_id,
          first_name: familyMember.first_name,
          last_name: familyMember.last_name,
          email: familyMember.email,
          family_role: familyMember.family_role,
          relationship_type: familyMember.relationship_type
        }
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
