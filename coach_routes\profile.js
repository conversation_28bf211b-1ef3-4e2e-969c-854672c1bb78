const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const { validate_availability, validate_account } = require("../utils/util");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({
    role: "coach",
  }),
];


module.exports = function (app) {
  app.get("/v3/api/custom/courtmatchup/coach/profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      console.log(req.user_id);
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      sdk.setTable("coach")
      const profile = await sdk.get({
        user_id: req.user_id,
      })
      if (!profile.length) {
        return res.status(403).json({
          error: true,
          message: "Coach not found",
        });
      }

      sdk.setTable("coach_sports")
      // const coach_sports = await sdk.get({
      //   coach_id: profile[0].id
      // })
      const coach_sports = await sdk.rawQuery(`
        SELECT cs.*, s.name as sport_name FROM courtmatchup_coach_sports cs
        LEFT JOIN courtmatchup_sports s ON cs.sport_id = s.id
        WHERE coach_id = ${profile[0].id}
      `)

      const profileObject = { ...profile[0] }



      return res.status(200).json({
        role: result[0].role,
        ...profileObject,
        photo: result[0].photo ?? "",
        email: result[0].email,
        first_name: result[0].first_name,
        last_name: result[0].last_name,
        phone: result[0].phone ?? "",
        sports: coach_sports,
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });


  app.post("/v3/api/custom/courtmatchup/coach/update-password", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result
        });
      }
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }
      const hash = result[0].password;
      const passwordValid = await PasswordService.compareHash(req.body.current_password, hash);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Password"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated Password"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/courtmatchup/coach/profile-edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (!result.length) {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("user");

      const updateResult = await sdk.update(filterEmptyFields({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        photo: req.body.photo ?? null,
        phone: req.body.phone ?? null,
        // completed: req.body.completed ?? null,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.user_id);

      sdk.setTable("coach");
      const coach = (await sdk.get({
        user_id: req.user_id,
      }))[0];

      if(!coach) {
        const {
          name,
          bio,
          club_id,
          not_paid_through_platform=0,
          availability,
          default_availability,
          week_specific_availability,
          hourly_rate,
          sport_ids,
          account_details,
        } = req.body;

        if (account_details) validate_account(account_details);
        if (availability) validate_availability(availability);
        
        sdk.setTable('coach');
        const coach_id = await sdk.insert(filterEmptyFields({
          user_id: req.user_id,
          name,
          club_id,
          not_paid_through_platform,
          availability: JSON.stringify(availability || []),
          default_availability: JSON.stringify(default_availability || []),
          week_specific_availability: JSON.stringify(week_specific_availability || []),
          hourly_rate,
          completed: req.body.completed ?? null,  
          bio,
          account_details: JSON.stringify( account_details || []),
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }))

        if(sport_ids && sport_ids.length > 0) {
          sdk.setTable('coach_sports');
          for (const sport of sport_ids) {
            const { type, price, sport_id, sub_type } = sport
            await sdk.insert(filterEmptyFields({
              coach_id,
              sport_id,
              type,
              price,
              sub_type
            }))
          }
        }
      }else{
        const {
          name,
          bio,
          club_id,
          availability,
          hourly_rate,
          not_paid_through_platform,
          default_availability,
          week_specific_availability,
          sport_ids,
          account_details,
        } = req.body;
        
        sdk.setTable('coach');
        await sdk.update(filterEmptyFields({
          name,
          bio,
          club_id,
          not_paid_through_platform,
          availability: availability ? JSON.stringify(availability) : null,
          default_availability: default_availability ? JSON.stringify(default_availability) : null,
          week_specific_availability: week_specific_availability ? JSON.stringify(week_specific_availability) : null,
          hourly_rate,
          completed: req.body.completed ?? null,
          account_details: account_details ? JSON.stringify(account_details) : null,
          update_at: sqlDateTimeFormat(new Date())
        }), coach.id)

        if(sport_ids && sport_ids.length > 0) {
          sdk.setTable('coach_sports');
          for (const sport of sport_ids) {
            const { type, price, sport_id, sub_type } = sport;
            const check_existing = (await sdk.get(filterEmptyFields({
              coach_id: coach.id,
              sport_id,
              sub_type,
              type,
              price
            })))[0];

            if(check_existing) {
              await sdk.update({
                coach_id: coach.id,
                sport_id,
                type,
                price,
                sub_type
              }, check_existing.id)
            }else{
              await sdk.insert(filterEmptyFields({
                coach_id: coach.id,
                sport_id,
                type,
                price,
                sub_type
              }))
            }
          }
        }
      }
      
      

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }




      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Profile API",
      url: "/v3/api/custom/profile",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
      ],
      needToken: true
    }
  ];
};
