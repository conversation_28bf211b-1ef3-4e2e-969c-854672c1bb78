const GptService = require('./GptService');

class CourtMatchupBotService {
    /**
     * Get or create a chat session
     * @param {number} club_id - The club ID
     * @param {number} [user_id] - Optional user ID if logged in
     * @param {number} [chat_id] - Optional existing chat ID
     * @returns {Promise<Object>} - The chat session
     */
    async getOrCreateChat(club_id, user_id = null, chat_id = null, sdk) {
        try {
            if (chat_id) {
                const existingChat = await sdk.rawQuery(
                    `SELECT * FROM courtmatchup_bot WHERE id = ${chat_id} AND club_id = ${club_id}`
                );
                if (existingChat) {
                    return existingChat;
                }
            }

            const result = await sdk.rawQuery(
                `INSERT INTO courtmatchup_bot (user_id, club_id, status, create_at, update_at) VALUES (${user_id}, ${club_id}, 0, NOW(), NOW())`
            );

            return {
                id: result.insertId,
                user_id,
                club_id,
                status: 0,
                history: null,
                data: null
            };
        } catch (error) {
            console.error('Error in getOrCreateChat:', error);
            throw error;
        }
    }

    /**
     * Get FAQ questions for a club
     * @param {number} club_id - The club ID
     * @returns {Promise<Array>} - Array of FAQ questions and answers
     */
    async getClubFaqs(club_id, sdk) {
        try {
            const faqs = await sdk.rawQuery(
                `SELECT  answer FROM courtmatchup_faq WHERE club_id = ${club_id}  ORDER BY id ASC`
            );
            return faqs;
        } catch (error) {
            console.error('Error in getClubFaqs:', error);
            throw error;
        }
    }

    /**
     * Process a user message and get a response
     * @param {number} chat_id - The chat ID
     * @param {string} message - The user's message
     * @param {number} club_id - The club ID
     * @returns {Promise<string>} - The bot's response
     */
    async processMessage(chat_id, message, club_id, sdk) {
        try {
            // Get chat history
            const chat = await sdk.rawQuery(
                `SELECT * FROM courtmatchup_bot WHERE id = ${chat_id} AND club_id = ${club_id}`
            );

            if (!chat.length) {
                throw new Error('Chat not found');
            }

            // Get FAQ data
            const faqs = await this.getClubFaqs(club_id, sdk);
            
            // Prepare system message with FAQ context
            const systemMessage = {
                role: 'system',
                content: `You are a helpful assistant for a club. Here are some frequently asked questions and their answers:
                ${faqs.map(faq => `A: ${faq.answer}`).join('\n\n')}
                Use this information to help answer questions. If the answer isn't in the FAQs, provide the closest answer.`
            };

            console.log(systemMessage);

            // Parse existing history
            let history = [];
            if (chat[0].history) {
                try {
                    history = JSON.parse(chat[0].history);
                } catch (e) {
                    console.error('Error parsing chat history:', e);
                }
            }

            // Add user message to history
            history.push({ role: 'user', content: message });

            // Get response from OpenAI
            const response = await GptService.getChatResponse([
                systemMessage,
                ...history
            ], {
                temperature: 0.7,
                top_p: 0.9
            });

            // Add assistant response to history
            history.push(response);

            // Update chat history in database
            await sdk.setTable('bot');
            await sdk.update({
                history: JSON.stringify(history),
                update_at: new Date()
            }, chat_id);

            return response.content;
        } catch (error) {
            console.error('Error in processMessage:', error);
            throw error;
        }
    }
}

module.exports = new CourtMatchupBotService(); 