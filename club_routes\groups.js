const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const ValidationService = require("../../../services/ValidationService.js");
const PaginationService = require("../../../services/PaginationService.js");
const { saveCount } = require("../services/logs");
const { emitEvent, getCount } = require("../services/EmitEventService");
const { sendNotification } = require("../services/PushNotificaionService");
const MkdEventService = require("../services/MkdEventService");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const { ERROR_MESSAGES } = require("../utils/constants.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/groups";

module.exports = function (app) {

  // Get club data
  app.post(base + "/create", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {
        name,
        members=[],
        type=0
      } = req.body;

      if(!name) throw new Error(ERROR_MESSAGES.MISSING_NAME)

      sdk.setTable("user_groups")
      await sdk.insert({
        name,
        members: JSON.stringify(members),
        type,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      })
      return res.status(200).json({
        error: false,
        model: club[0] || {}
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {group_id, user_id} = req.body;
      sdk.setTablbe("user_groups")
      const groups = (await sdk.get({
        user_id
      }))
      const sql = ``

      return res.status(200).json({
        error: false,
        groups
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/join-group", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);



      return res.status(200).json({
        error: false,
        model:  {}
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/join-group", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);



      return res.status(200).json({
        error: false,
        model:  {}
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
