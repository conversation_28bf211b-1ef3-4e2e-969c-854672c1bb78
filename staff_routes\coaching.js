const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const ValidationService = require("../../../services/ValidationService.js");
const PaginationService = require("../../../services/PaginationService.js");
const { saveCount } = require("../services/logs");
const { emitEvent, getCount } = require("../services/EmitEventService");
const { sendNotification } = require("../services/PushNotificaionService");
const MkdEventService = require("../services/MkdEventService");
const { reservation_hours_left, log_reservation, build_coach_availability_query, generateAvailabilityForNextMonth } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/coach";

module.exports = function (app) {
  // get reservations
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);


      return res.status(200).json({
        error: false,
        list: categories,
        total: categories.length
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Book coach
  app.post(base + "/book-coach", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const bookingService = BookingService(sdk);

      const booking_id = await bookingService.createBooking(req, req.user_id);
      
      return res.status(200).json({
        error: false,
        message: "Booking created successfully",
        booking_id
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/availability/:coach_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      sdk.setTable("coaches")
      const coach = await sdk.get({id: req.params.coach_id});
      const availability = JSON.parse(coach?.availability || "[]");
      const slots_for_next_month = generateAvailabilityForNextMonth(availability);

      return res.status(200).json({
        error: false,
        availability,
        slots_for_next_month
      })
    }catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Find coaches by time slots
  app.post(base + "/search-time-slots", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {
        sport_id,
        type,
        date,
        start_time,
        end_time
      } = req.body;

    //   available_hours = [{
    //     day: "monday",
    //     timeslots: ["9:10", "10:10", "11:10", "12:10", "13:10", "14:10", "15:10", "16:10", "17:10", "18:10"],
    //  }]

      let query = build_coach_availability_query({ sport_id, date, start_time, end_time });
      const exact_coaches = await sdk.rawQuery(query);
      query = build_coach_availability_query({ sport_id, date, start_time, start_time });

      const close_coaches = await sdk.rawQuery(query);

      const coaches = exact_coaches.concat(close_coaches);
      

      return res.status(200).json({
        error: false,
        list: coaches
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
