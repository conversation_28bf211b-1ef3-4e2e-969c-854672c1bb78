const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const { RESERVATION_TYPES, BOOKING_STATUSES } = require("../utils/constants");
const stripe = new StripeService();

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "club" }),
];
const adminMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "admin|admin_staff" }),
];
const combinedMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "admin|club|staff|admin_staff" }),
];

const base = "/v3/api/custom/courtmatchup/club/billing";
const _base = "/v3/api/custom/courtmatchup/admin/billing";

module.exports = function (app) {
  // Get all invoices
  // app.get(`${base}/invoices`, middlewares, async function (req, res) {
  //   try {
  //     let sdk = req.sdk;
  //     sdk.getDatabase();
  //     sdk.setProjectId(req.projectId);
      
  //     // Get the club ID from the authenticated user
  //     sdk.setTable("clubs");
  //     const clubs = await sdk.get({ user_id: req.user_id });
      
  //     if (!clubs.length) {
  //       return res.status(404).json({
  //         error: true,
  //         message: "Club not found for this user"
  //       });
  //     }
      
  //     const club_id = clubs[0].id;
      
  //     const { 
  //       status, 
  //       date_start, 
  //       date_end, 
  //       user_id,
  //       limit = 20,
  //       offset = 0,
  //       type,
  //       include_subscriptions = "true" // Default to true - include subscriptions
  //     } = req.query;
      
  //     // Build SQL query with filters for regular invoices
  //     let sql = `
  //       SELECT 
  //         b.id AS invoice_id,
  //         b.payment_intent,
  //         b.price AS amount,
  //         b.club_fee,
  //         b.service_fee,
  //         b.coach_fee,
  //         b.clinic_fee,
  //         b.court_fee,
  //         b.date AS invoice_date,
  //         b.create_at,
  //         b.status,
  //         b.reservation_type,
  //         b.last_4,
  //         b.user_id AS customer_id,
  //         u.first_name AS customer_first_name,
  //         u.last_name AS customer_last_name,
  //         u.email AS customer_email,
          
  //         (COALESCE(b.price, 0) + 
  //         COALESCE(b.club_fee, 0) + 
  //         COALESCE(b.service_fee, 0) + 
  //         COALESCE(b.coach_fee, 0) + 
  //         COALESCE(b.clinic_fee, 0) +
  //         COALESCE(b.court_fee, 0)) AS total_amount,
          
  //         s.name AS sport_name,
  //         b.type AS booking_type,
  //         b.subtype AS booking_subtype,
          
  //         CASE
  //           WHEN b.reservation_type = 1 THEN 'Club Court'
  //           WHEN b.reservation_type = 2 THEN 'Clinic'
  //           WHEN b.reservation_type = 3 THEN 'Lesson'
  //           WHEN b.reservation_type = 4 THEN 'Coach'
  //           ELSE 'Other'
  //         END AS invoice_type,
  //         'one-time' AS payment_type
  //       FROM 
  //         courtmatchup_booking b
  //       JOIN 
  //         courtmatchup_user u ON b.user_id = u.id
  //       LEFT JOIN
  //         courtmatchup_sports s ON b.sport_id = s.id
  //       WHERE 
  //         b.club_id = ${club_id}
  //         AND b.payment_intent IS NOT NULL
  //     `;
      
  //     // Add filters for invoices
  //     if (status) {
  //       sql += ` AND b.status = ${parseInt(status)}`;
  //     }
      
  //     if (date_start && date_end) {
  //       sql += ` AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'`;
  //     }
      
  //     if (user_id) {
  //       sql += ` AND b.user_id = ${parseInt(user_id)}`;
  //     }
      
  //     if (type) {
  //       sql += ` AND b.reservation_type = ${parseInt(type)}`;
  //     }
      
  //     // Build subscription query if requested
  //     let subscriptionSql = "";
  //     if (include_subscriptions === "true") {
  //       subscriptionSql = `
  //         SELECT 
  //           s.id AS invoice_id,
  //           s.stripe_id AS payment_intent,
  //           s.amount,
  //           0 AS club_fee,
  //           0 AS service_fee,
  //           0 AS coach_fee,
  //           0 AS clinic_fee,
  //           0 AS court_fee,
  //           s.current_period_start AS invoice_date,
  //           s.create_at,
  //           CASE 
  //             WHEN s.status = 'active' THEN 1
  //             WHEN s.status = 'canceled' THEN 2
  //             ELSE 0
  //           END AS status,
  //           0 AS reservation_type,
  //           '' AS last_4,
  //           s.user_id AS customer_id,
  //           u.first_name AS customer_first_name,
  //           u.last_name AS customer_last_name,
  //           u.email AS customer_email,
            
  //           s.amount AS total_amount,
            
  //           p.name AS sport_name,
  //           s.interval AS booking_type,
  //           CONCAT(s.interval_count, ' ', s.interval) AS booking_subtype,
            
  //           'Subscription' AS invoice_type,
  //           'subscription' AS payment_type
  //         FROM 
  //           courtmatchup_stripe_subscription s
  //         JOIN 
  //           courtmatchup_user u ON s.user_id = u.id
  //         LEFT JOIN
  //           courtmatchup_plans p ON s.plan_id = p.id
  //         WHERE 
  //           s.club_id = ${club_id}
  //           AND s.object IS NOT NULL
  //       `;
        
  //       // Add filters for subscriptions
  //       if (status) {
  //         subscriptionSql += ` AND (
  //           ${parseInt(status) === 1 ? "s.status = 'active'" : ""} 
  //           ${parseInt(status) === 2 ? "s.status = 'canceled'" : ""}
  //           ${parseInt(status) === 0 ? "s.status NOT IN ('active', 'canceled')" : ""}
  //         )`;
  //       }
        
  //       if (date_start && date_end) {
  //         subscriptionSql += ` AND (
  //           (s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
  //           (s.current_period_end BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
  //           (s.current_period_start <= '${sqlDateFormat(new Date(date_start))}' AND s.current_period_end >= '${sqlDateFormat(new Date(date_end))}')
  //         )`;
  //       }
        
  //       if (user_id) {
  //         subscriptionSql += ` AND s.user_id = ${parseInt(user_id)}`;
  //       }
  //     }
      
  //     // Combine queries if needed
  //     let combinedSql = sql;
  //     if (include_subscriptions === "true") {
  //       combinedSql = `(${sql}) UNION ALL (${subscriptionSql})`;
  //     }
      
  //     // Count total
  //     const countSql = `SELECT COUNT(*) as total FROM (${combinedSql}) as count_query`;
  //     const totalCount = await sdk.rawQuery(countSql);
      
  //     // Add ordering and pagination
  //     combinedSql += ` ORDER BY create_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
      
  //     // Execute query
  //     const billingItems = await sdk.rawQuery(combinedSql);
      
  //     // Calculate summary stats for invoices
  //     const invoiceSummarySql = `
  //       SELECT 
  //         SUM(COALESCE(b.price, 0) + 
  //             COALESCE(b.club_fee, 0) + 
  //             COALESCE(b.service_fee, 0) + 
  //             COALESCE(b.coach_fee, 0) + 
  //             COALESCE(b.clinic_fee, 0) +
  //             COALESCE(b.court_fee, 0)) AS invoice_revenue,
  //         COUNT(*) AS total_invoices,
  //         COUNT(DISTINCT b.user_id) AS invoice_customers
  //       FROM 
  //         courtmatchup_booking b
  //       WHERE 
  //         b.club_id = ${club_id}
  //         AND b.payment_intent IS NOT NULL
  //         ${status ? ` AND b.status = ${parseInt(status)}` : ''}
  //         ${date_start && date_end ? ` AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
  //         ${user_id ? ` AND b.user_id = ${parseInt(user_id)}` : ''}
  //         ${type ? ` AND b.reservation_type = ${parseInt(type)}` : ''}
  //     `;
      
  //     // Subscription summary
  //     const subscriptionSummarySql = `
  //       SELECT 
  //         SUM(s.amount) AS subscription_revenue,
  //         COUNT(*) AS total_subscriptions,
  //         COUNT(DISTINCT s.user_id) AS subscription_customers
  //       FROM 
  //         courtmatchup_stripe_subscription s
  //       WHERE 
  //         s.club_id = ${club_id}
  //         AND s.object IS NOT NULL
  //         ${status ? ` AND (
  //           ${parseInt(status) === 1 ? "s.status = 'active'" : ""} 
  //           ${parseInt(status) === 2 ? "s.status = 'canceled'" : ""}
  //           ${parseInt(status) === 0 ? "s.status NOT IN ('active', 'canceled')" : ""}
  //         )` : ''}
  //         ${date_start && date_end ? ` AND (
  //           (s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
  //           (s.current_period_end BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
  //           (s.current_period_start <= '${sqlDateFormat(new Date(date_start))}' AND s.current_period_end >= '${sqlDateFormat(new Date(date_end))}')
  //         )` : ''}
  //         ${user_id ? ` AND s.user_id = ${parseInt(user_id)}` : ''}
  //     `;
      
  //     const invoiceSummary = await sdk.rawQuery(invoiceSummarySql);
  //     const subscriptionSummary = include_subscriptions === "true" ? 
  //       await sdk.rawQuery(subscriptionSummarySql) : 
  //       [{ subscription_revenue: 0, total_subscriptions: 0, subscription_customers: 0 }];
      
  //     // Combined summary
  //     const combinedSummary = {
  //       total_revenue: (invoiceSummary[0]?.invoice_revenue || 0) + (subscriptionSummary[0]?.subscription_revenue || 0),
  //       total_payments: (invoiceSummary[0]?.total_invoices || 0) + (subscriptionSummary[0]?.total_subscriptions || 0),
  //       unique_customers: 0, // Calculated below
  //       invoice_revenue: invoiceSummary[0]?.invoice_revenue || 0,
  //       subscription_revenue: subscriptionSummary[0]?.subscription_revenue || 0,
  //       total_invoices: invoiceSummary[0]?.total_invoices || 0,
  //       total_subscriptions: subscriptionSummary[0]?.total_subscriptions || 0
  //     };
      
  //     // Get unique customer count
  //     if (include_subscriptions === "true") {
  //       const uniqueCustomersSql = `
  //         SELECT COUNT(DISTINCT user_id) AS unique_customers FROM (
  //           SELECT user_id FROM courtmatchup_booking WHERE club_id = ${club_id}
  //           ${status ? ` AND status = ${parseInt(status)}` : ''}
  //           ${date_start && date_end ? ` AND date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
  //           ${user_id ? ` AND user_id = ${parseInt(user_id)}` : ''}
  //           ${type ? ` AND reservation_type = ${parseInt(type)}` : ''}
  //           UNION 
  //           SELECT user_id FROM courtmatchup_stripe_subscription WHERE club_id = ${club_id}
  //           ${status ? ` AND (
  //             ${parseInt(status) === 1 ? "status = 'active'" : ""} 
  //             ${parseInt(status) === 2 ? "status = 'canceled'" : ""}
  //             ${parseInt(status) === 0 ? "status NOT IN ('active', 'canceled')" : ""}
  //           )` : ''}
  //           ${date_start && date_end ? ` AND (
  //             (current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
  //             (current_period_end BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}')
  //           )` : ''}
  //           ${user_id ? ` AND user_id = ${parseInt(user_id)}` : ''}
  //         ) AS combined_users
  //       `;
        
  //       const uniqueCustomers = await sdk.rawQuery(uniqueCustomersSql);
  //       combinedSummary.unique_customers = uniqueCustomers[0]?.unique_customers || 0;
  //     } else {
  //       combinedSummary.unique_customers = invoiceSummary[0]?.invoice_customers || 0;
  //     }
      
  //     return res.status(200).json({
  //       error: false,
  //       billing_items: billingItems,
  //       count: totalCount[0].total,
  //       summary: combinedSummary
  //     });
      
  //   } catch (err) {
  //     console.error(err);
  //     res.status(403).json({
  //       error: true,
  //       message: err.message
  //     });
  //   }
  // });
    // Get user's billing history/invoices
    app.get(base + "/invoices", middlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
    
        // Get query parameters
        const { sort = 'desc', invoice_id, first_name, last_name, email, receipt_id, invoice_type, from: date_start, until: date_end=new Date(), sport_id, booking_type } = req.query;
        
        
        // Validate sort parameter
        if (sort !== 'asc' && sort !== 'desc') {
          throw new Error("Sort parameter must be 'asc' or 'desc'");
        }

        const user_id = req.user_id;
        if(!user_id){
          throw new Error("User ID is required");
        }

        sdk.setTable("clubs");
        const club = await sdk.get({ user_id: user_id });
        if(!club.length){
          throw new Error("Club not found");
        }
        const club_id = club[0].id;
        
        
    
        // Get one-time payments from bookings
        let bookings_sql = `
          SELECT 
            u.first_name,
            u.last_name,
            u.email,
            b.id,
            b.id as invoice_id,
            b.price as amount,
            b.club_fee,
            b.service_fee,
            b.sport_id,
            s.name as sport_name,
            b.coach_fee,
            b.clinic_fee,
            b.receipt_id,
            b.last_4,
            b.court_fee,
            b.payment_intent,
            b.date,
            b.create_at,
            b.status,
            b.reservation_type,
            b.update_at,
            'Checkout' as payment_method,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS type,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS invoice_type,
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0) AS total_amount
          FROM 
            courtmatchup_booking b
          JOIN
            courtmatchup_user u ON b.user_id = u.id
          LEFT JOIN
            courtmatchup_sports s ON b.sport_id = s.id
          WHERE 
            b.status = ${BOOKING_STATUSES.SUCCESS}
            AND b.payment_intent IS NOT NULL
            AND u.club_id = ${club_id}
            ${date_start && date_end ? `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
            ${booking_type ? `AND b.reservation_type = ${booking_type}` : ''}
            ${sport_id ? `AND b.sport_id = ${sport_id}` : ''}
            ${invoice_id ? `AND b.id = '${invoice_id}'` : ''}
            ${receipt_id ? `AND b.receipt_id = '${receipt_id}'` : ''}
            ${first_name ? `AND u.first_name LIKE '%${first_name}%'` : ''}
            ${last_name ? `AND u.last_name LIKE '%${last_name}%'` : ''}
            ${email ? `AND u.email LIKE '%${email}%'` : ''}
        `;
    
        // Add invoice_type filter for bookings if specified
        if (invoice_type) {
          switch(invoice_type.toLowerCase()) {
            case 'court':
              bookings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
              break;
            case 'lesson':
              bookings_sql += ` AND b.lesson_id IS NOT NULL`;
              break;
            case 'clinic':
              bookings_sql += ` AND b.clinic_id IS NOT NULL`;
              break;
          }
        }
        // Get subscription payments
        const subscriptions_sql = `
          SELECT 
            s.id,
            s.id as invoice_id,
            u.first_name,
            u.last_name,
            u.email,
            s.stripe_id,
            'null' as sport_name,
            'null' as sport_id,
            s.price_id,
            s.status,
            s.create_at,
            s.object,
            'Subscription' as invoice_type,
            s.stripe_id as receipt_id,
            s.update_at
          FROM 
            courtmatchup_stripe_subscription s
          LEFT JOIN
            courtmatchup_user u ON s.user_id = u.id
          WHERE 
             s.object IS NOT NULL
             AND u.club_id = ${club_id}
             ${
              date_start && date_end ? `AND s.create_at BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''
             }
             ${invoice_id ? `AND s.id = '${invoice_id}'` : ''}
             ${receipt_id ? `AND s.stripe_id = '${receipt_id}'` : ''}
             ${first_name ? `AND u.first_name LIKE '%${first_name}%'` : ''}
             ${last_name ? `AND u.last_name LIKE '%${last_name}%'` : ''}
             ${email ? `AND u.email LIKE '%${email}%'` : ''}
        `;
    
        let bookings = [], subscriptions = [];
        
        // Only fetch relevant data based on invoice_type filter
        if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
          bookings = await sdk.rawQuery(bookings_sql);
        }
        
        if (!invoice_type || invoice_type.toLowerCase() === 'subscription' || !sport_id || !booking_type) {
          subscriptions = await sdk.rawQuery(subscriptions_sql);
        }
    
        // Process subscription data to extract amount and other details
        const processed_subscriptions = subscriptions.map(sub => {
          const obj = JSON.parse(sub.object);
          const paymentMethod = obj.default_payment_method;
          let paymentMethodDisplay = 'Unknown Payment Method';
          
          if (paymentMethod && typeof paymentMethod === 'string') {
            // If it's just the ID, show generic "Credit Card"
            paymentMethodDisplay = 'Credit Card';
          } else if (paymentMethod && paymentMethod.card) {
            // If we have card details, show last 4
            paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
          }
  
          return {
            id: sub.id,
            stripe_id: sub.stripe_id,
            subscription_id: obj.id,
            price_id: sub.price_id,
            invoice_id: sub.invoice_id,
            receipt_id: sub.receipt_id,
            status: sub.status,
            create_at: sub.create_at,
            invoice_type: 'Subscription',
            type: obj.plan?.nickname || 'Subscription Plan',
            plan_name: obj.plan?.product || 'Standard Plan',
            total_amount: obj.plan?.amount || 0,
            currency: obj.currency || 'usd',
            interval: obj.plan?.interval || 'month',
            payment_method: paymentMethodDisplay,
            update_at: sub.update_at,
            valid_until: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            current_period_start: obj.current_period_start ? 
              new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
            current_period_end: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            canceled_at: obj.canceled_at ? 
              new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
          };
        });
    
        // Format booking data to match structure
        const processed_bookings = bookings.map(booking => ({
          ...booking,
          currency: 'usd',
          total_amount: parseFloat(booking.amount),
          status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
          valid_until: booking.date, // For bookings, valid until is the booking date
          update_at: booking.update_at
        }));
    
        // Combine and sort all invoices by create_at date
        const all_invoices = [...processed_bookings, ...processed_subscriptions].sort((a, b) => {
          const dateComparison = new Date(b.create_at) - new Date(a.create_at);
          return sort === 'desc' ? dateComparison : -dateComparison;
        });
    
        // Calculate totals
        const total_spent = all_invoices.reduce((sum, invoice) => 
          sum + (invoice.total_amount || 0), 0
        );
    
        const active_subscriptions = processed_subscriptions.filter(sub => 
          sub.status === 'active'
        );
    
        // Group invoices by type
        const invoices_by_type = all_invoices.reduce((acc, invoice) => {
          const type = invoice.type;
          if (!acc[type]) {
            acc[type] = [];
          }
          acc[type].push(invoice);
          return acc;
        }, {});
    
        return res.status(200).json({
          error: false,
          invoices: all_invoices,
          invoices_by_type,
          total_spent: total_spent / 100, // Convert from cents to dollars
          active_subscriptions: active_subscriptions,
          total_invoices: all_invoices.length,
          filters: {
            sort,
            invoice_type: invoice_type || 'all',
            first_name,
            last_name,
            email
          }
        });
    
      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });
    app.get(_base + "/invoices", adminMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
    
        // Get query parameters
        const { sort = 'desc', club_id, first_name, last_name, email, invoice_type, invoice_id, receipt_id, sport_id, booking_type, from: date_start, until: date_end=new Date() } = req.query;
        
        // Validate sort parameter
        if (sort !== 'asc' && sort !== 'desc') {
          throw new Error("Sort parameter must be 'asc' or 'desc'");
        }
        // Get one-time payments from bookings
        let bookings_sql = `
          SELECT 
            u.first_name,
            u.last_name,
            u.email,
            b.id,
            b.id as invoice_id,
            b.price as amount,
            b.club_fee,
            b.service_fee,
            b.sport_id,
            s.name as sport_name,
            b.coach_fee,
            b.clinic_fee,
            b.receipt_id,
            b.last_4,
            b.court_fee,
            b.payment_intent,
            b.date,
            b.create_at,
            b.status,
            b.reservation_type,
            b.update_at,
            'Checkout' as payment_method,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS type,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS invoice_type,
            COALESCE(b.price, 0) + 
            COALESCE(b.club_fee, 0) + 
            COALESCE(b.service_fee, 0) + 
            COALESCE(b.coach_fee, 0) + 
            COALESCE(b.clinic_fee, 0) +
            COALESCE(b.court_fee, 0) AS total_amount
          FROM 
            courtmatchup_booking b
          JOIN
            courtmatchup_user u ON b.user_id = u.id
          LEFT JOIN
            courtmatchup_sports s ON b.sport_id = s.id
          WHERE 
            b.status = ${BOOKING_STATUSES.SUCCESS}
            AND b.payment_intent IS NOT NULL
            AND u.club_id = ${club_id}
            ${date_start && date_end ? `AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''}
            ${booking_type ? `AND b.reservation_type = ${booking_type}` : ''}
            ${sport_id ? `AND b.sport_id = ${sport_id}` : ''}
            ${invoice_id ? `AND b.id = '${invoice_id}'` : ''}
            ${receipt_id ? `AND b.receipt_id = '${receipt_id}'` : ''}
            ${first_name ? `AND u.first_name LIKE '%${first_name}%'` : ''}
            ${last_name ? `AND u.last_name LIKE '%${last_name}%'` : ''}
            ${email ? `AND u.email LIKE '%${email}%'` : ''}
        `;
    
        // Add invoice_type filter for bookings if specified
        if (invoice_type) {
          switch(invoice_type.toLowerCase()) {
            case 'court':
              bookings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
              break;
            case 'lesson':
              bookings_sql += ` AND b.lesson_id IS NOT NULL`;
              break;
            case 'clinic':
              bookings_sql += ` AND b.clinic_id IS NOT NULL`;
              break;
          }
        }
        // Get subscription payments
        const subscriptions_sql = `
          SELECT 
            s.id,
            s.id as invoice_id,
            u.first_name,
            u.last_name,
            u.email,
            s.stripe_id,
            'null' as sport_name,
            'null' as sport_id,
            s.price_id,
            s.status,
            s.create_at,
            s.object,
            'Subscription' as invoice_type,
            s.stripe_id as receipt_id,
            s.update_at
          FROM 
            courtmatchup_stripe_subscription s
          LEFT JOIN
            courtmatchup_user u ON s.user_id = u.id
          WHERE 
             s.object IS NOT NULL
             AND u.club_id = ${club_id}
             ${
              date_start && date_end ? `AND s.create_at BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'` : ''
             }
             ${invoice_id ? `AND s.id = '${invoice_id}'` : ''}
             ${receipt_id ? `AND s.stripe_id = '${receipt_id}'` : ''}
             ${first_name ? `AND u.first_name LIKE '%${first_name}%'` : ''}
             ${last_name ? `AND u.last_name LIKE '%${last_name}%'` : ''}
             ${email ? `AND u.email LIKE '%${email}%'` : ''}
        `;
    
        let bookings = [], subscriptions = [];
        
        // Only fetch relevant data based on invoice_type filter
        if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
          bookings = await sdk.rawQuery(bookings_sql);
        }
        
        if (!invoice_type || invoice_type.toLowerCase() === 'subscription' || !sport_id || !booking_type) {
          subscriptions = await sdk.rawQuery(subscriptions_sql);
        }
    
        // Process subscription data to extract amount and other details
        const processed_subscriptions = subscriptions.map(sub => {
          const obj = JSON.parse(sub.object);
          const paymentMethod = obj.default_payment_method;
          let paymentMethodDisplay = 'Unknown Payment Method';
          
          if (paymentMethod && typeof paymentMethod === 'string') {
            // If it's just the ID, show generic "Credit Card"
            paymentMethodDisplay = 'Credit Card';
          } else if (paymentMethod && paymentMethod.card) {
            // If we have card details, show last 4
            paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
          }
  
          return {
            id: sub.id,
            stripe_id: sub.stripe_id,
            subscription_id: obj.id,
            price_id: sub.price_id,
            invoice_id: sub.invoice_id,
            receipt_id: sub.receipt_id,
            status: sub.status,
            create_at: sub.create_at,
            invoice_type: 'Subscription',
            type: obj.plan?.nickname || 'Subscription Plan',
            plan_name: obj.plan?.product || 'Standard Plan',
            total_amount: obj.plan?.amount || 0,
            currency: obj.currency || 'usd',
            interval: obj.plan?.interval || 'month',
            payment_method: paymentMethodDisplay,
            update_at: sub.update_at,
            valid_until: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            current_period_start: obj.current_period_start ? 
              new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
            current_period_end: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            canceled_at: obj.canceled_at ? 
              new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
          };
        });
    
        // Format booking data to match structure
        const processed_bookings = bookings.map(booking => ({
          ...booking,
          currency: 'usd',
          total_amount: parseFloat(booking.amount),
          status: booking.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
          valid_until: booking.date, // For bookings, valid until is the booking date
          update_at: booking.update_at
        }));
    
        // Combine and sort all invoices by create_at date
        const all_invoices = [...processed_bookings, ...processed_subscriptions].sort((a, b) => {
          const dateComparison = new Date(b.create_at) - new Date(a.create_at);
          return sort === 'desc' ? dateComparison : -dateComparison;
        });
    
        // Calculate totals
        const total_spent = all_invoices.reduce((sum, invoice) => 
          sum + (invoice.total_amount || 0), 0
        );
    
        const active_subscriptions = processed_subscriptions.filter(sub => 
          sub.status === 'active'
        );
    
        // Group invoices by type
        const invoices_by_type = all_invoices.reduce((acc, invoice) => {
          const type = invoice.type;
          if (!acc[type]) {
            acc[type] = [];
          }
          acc[type].push(invoice);
          return acc;
        }, {});
    
        return res.status(200).json({
          error: false,
          invoices: all_invoices,
          invoices_by_type,
          total_spent: total_spent / 100, // Convert from cents to dollars
          active_subscriptions: active_subscriptions,
          total_invoices: all_invoices.length,
          filters: {
            sort,
            invoice_type: invoice_type || 'all',
            first_name,
            last_name,
            email
          }
        });
    
      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });

    // fetch coach invoices with filters
    app.get(`${base}/coach-invoices`, combinedMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);

        let { coach_id, from: date_start, until: date_end=new Date() } = req.query;
        const { sort = 'desc', invoice_id, receipt_id, invoice_type, first_name, last_name, email, sport_id, booking_type, limit, offset } = req.query;

        date_end = date_end ? new Date(date_end) : (new Date()).getDate() + 30;

        let sql = `
          SELECT * FROM courtmatchup_booking WHERE coach_id is not null ${coach_id ? `AND coach_id = ${coach_id}` : ''} ${date_start && date_end ? `AND date BETWEEN '${sqlDateFormat(date_start)}' AND '${sqlDateFormat(date_end)}}` : ''}
        `;

        if (invoice_type) {
          sql += ` AND invoice_type = ${invoice_type}`;
        }

        if (sport_id) {
          sql += ` AND sport_id = ${sport_id}`;
        }

        if (booking_type) {
          sql += ` AND booking_type = ${booking_type}`;
        }

        if (invoice_id) {
          sql += ` AND id = ${invoice_id}`;
        }

        if (receipt_id) {
          sql += ` AND receipt_id = ${receipt_id}`;
        }

        if (sort) {
          sql += ` ORDER BY date ${sort}`;
        }

        if (limit) {
          sql += ` LIMIT ${limit}`;
        }

        if (offset) {
          sql += ` OFFSET ${offset}`;
        }

        const coach_invoices = await sdk.rawQuery(sql);

        const processed_coach_invoices = coach_invoices.map(invoice => ({
          ...invoice,
          total_amount: parseFloat(invoice.amount),
          status: invoice.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        }));

        return res.status(200).json({
          error: false,
          start_date: date_start,
          end_date: date_end,
          invoices: processed_coach_invoices
        });

      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });

  // Get single invoice details
  app.get(`${base}/invoices/:id`, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      // Get the club ID from the authenticated user
      sdk.setTable("clubs");
      const clubs = await sdk.get({ user_id: req.user_id });
      
      if (!clubs.length) {
        return res.status(404).json({
          error: true,
          message: "Club not found for this user"
        });
      }
      
      const club_id = clubs[0].id;
      const invoiceId = req.params.id;
      
      if (!invoiceId) {
        return res.status(400).json({
          error: true,
          message: "Invoice ID is required"
        });
      }
      
      // Get invoice details
      const invoiceSql = `
        SELECT 
          b.id AS invoice_id,
          b.payment_intent,
          b.price AS amount,
          b.club_fee,
          b.service_fee,
          b.coach_fee,
          b.clinic_fee,
          b.court_fee,
          b.date AS invoice_date,
          b.create_at,
          b.status,
          b.start_time,
          b.end_time,
          b.duration,
          b.reservation_type,
          b.notes,
          b.player_ids,
          b.type AS booking_type,
          b.subtype AS booking_subtype,
          b.court_id,
          b.coach_id,
          b.clinic_id,
          b.last_4,
          b.custom_request,
          
          u.id AS customer_id,
          u.first_name AS customer_first_name,
          u.last_name AS customer_last_name,
          u.email AS customer_email,
          u.phone AS customer_phone,
          
          s.id AS sport_id,
          s.name AS sport_name,
          
          cc.name AS court_name,
          
          coach.id AS coach_id,
          coach_user.first_name AS coach_first_name,
          coach_user.last_name AS coach_last_name,
          
          clinic.name AS clinic_name,
          
          (COALESCE(b.price, 0) + 
          COALESCE(b.club_fee, 0) + 
          COALESCE(b.service_fee, 0) + 
          COALESCE(b.coach_fee, 0) + 
          COALESCE(b.clinic_fee, 0) +
          COALESCE(b.court_fee, 0)) AS total_amount,
          
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS invoice_type
        FROM 
          courtmatchup_booking b
        JOIN 
          courtmatchup_user u ON b.user_id = u.id
        LEFT JOIN
          courtmatchup_sports s ON b.sport_id = s.id
        LEFT JOIN
          courtmatchup_club_court cc ON b.court_id = cc.id
        LEFT JOIN
          courtmatchup_coach coach ON b.coach_id = coach.id
        LEFT JOIN
          courtmatchup_user coach_user ON coach.user_id = coach_user.id
        LEFT JOIN
          courtmatchup_clinics clinic ON b.clinic_id = clinic.id
        LEFT JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.id = ${invoiceId}
          AND b.payment_intent IS NOT NULL
      `;
      
      const invoice = await sdk.rawQuery(invoiceSql);
      
      if (!invoice.length) {
        return res.status(404).json({
          error: true,
          message: "Invoice not found or does not belong to this club"
        });
      }
      
      // Get reservation details
      const reservationSql = `
        SELECT 
          r.id AS reservation_id,
          r.user_id,
          r.status AS reservation_status,
          r.create_at,
          r.update_at,
          r.notes,
          r.buddy_id,
          r.space_assigned,
          
          u.first_name,
          u.last_name,
          u.email,
          u.phone
        FROM 
          courtmatchup_reservation r
        JOIN
          courtmatchup_user u ON r.user_id = u.id
        WHERE 
          r.booking_id = ${invoiceId}
      `;
      
      const reservations = await sdk.rawQuery(reservationSql);
      
      return res.status(200).json({
        error: false,
        invoice: invoice[0],
        reservations
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get financial report
  app.get(`${base}/reports/financial`, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      // Get the club ID from the authenticated user
      sdk.setTable("clubs");
      const clubs = await sdk.get({ user_id: req.user_id });
      
      if (!clubs.length) {
        return res.status(404).json({
          error: true,
          message: "Club not found for this user"
        });
      }
      
      const club_id = clubs[0].id;
      const { 
        date_start, 
        date_end, 
        group_by = 'day' // day, week, month
      } = req.query;
      
      if (!date_start || !date_end) {
        return res.status(400).json({
          error: true,
          message: "Start and end dates are required"
        });
      }
      
      // Determine grouping format
      let timeFormat;
      if (group_by === 'month') {
        timeFormat = '%Y-%m';
      } else if (group_by === 'week') {
        timeFormat = '%Y-%u'; // Year-Week number
      } else {
        timeFormat = '%Y-%m-%d';
      }
      
      // Revenue by time period
      const revenueByPeriodSql = `
        SELECT 
          DATE_FORMAT(b.date, '${timeFormat}') AS period,
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS revenue,
          COUNT(*) AS bookings_count,
          COUNT(DISTINCT b.user_id) AS unique_customers
        FROM 
          courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND b.status = 1 -- Successful payments only
        GROUP BY 
          period
        ORDER BY 
          period ASC
      `;
      
      const revenueByPeriod = await sdk.rawQuery(revenueByPeriodSql);
      
      // Revenue by service type
      const revenueByTypeSql = `
        SELECT 
          CASE
            WHEN b.reservation_type = 1 THEN 'Club Court'
            WHEN b.reservation_type = 2 THEN 'Clinic'
            WHEN b.reservation_type = 3 THEN 'Lesson'
            WHEN b.reservation_type = 4 THEN 'Coach'
            ELSE 'Other'
          END AS service_type,
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS revenue,
          COUNT(*) AS bookings_count
        FROM 
          courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND b.status = 1 -- Successful payments only
        GROUP BY 
          service_type
        ORDER BY 
          revenue DESC
      `;
      
      const revenueByType = await sdk.rawQuery(revenueByTypeSql);
      
      // Court utilization
      const courtUtilizationSql = `
        SELECT 
          cc.id AS court_id,
          cc.name AS court_name,
          s.name AS sport_name,
          COUNT(b.id) AS bookings_count,
          SUM(b.duration) AS hours_used,
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS revenue
        FROM 
          courtmatchup_club_court cc
        LEFT JOIN
          courtmatchup_booking b ON cc.id = b.court_id AND 
                                    b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
                                    AND b.status = 1
        LEFT JOIN
          courtmatchup_sports s ON cc.sport_id = s.id
        WHERE 
          cc.club_id = ${club_id}
        GROUP BY 
          cc.id, cc.name, s.name
        ORDER BY 
          hours_used DESC
      `;
      
      const courtUtilization = await sdk.rawQuery(courtUtilizationSql);
      
      // Summary statistics
      const summarySql = `
        SELECT 
          SUM(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS total_revenue,
          COUNT(*) AS total_bookings,
          COUNT(DISTINCT b.user_id) AS unique_customers,
          SUM(b.duration) AS total_hours_booked,
          AVG(COALESCE(b.price, 0) + 
              COALESCE(b.club_fee, 0) + 
              COALESCE(b.service_fee, 0) + 
              COALESCE(b.coach_fee, 0) + 
              COALESCE(b.clinic_fee, 0) +
              COALESCE(b.court_fee, 0)) AS average_booking_value
        FROM 
          courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND b.date BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
          AND b.status = 1 -- Successful payments only
      `;
      
      const summary = await sdk.rawQuery(summarySql);
      
      // Get subscription revenue by period
      const subscriptionRevenueByPeriodSql = `
        SELECT 
          DATE_FORMAT(s.current_period_start, '${timeFormat}') AS period,
          SUM(s.amount) AS subscription_revenue,
          COUNT(DISTINCT s.id) AS subscription_count
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.club_id = ${club_id}
          AND s.status = 'active'
          AND s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}'
        GROUP BY 
          period
        ORDER BY 
          MIN(s.current_period_start)
      `;
      
      const subscriptionRevenueByPeriod = await sdk.rawQuery(subscriptionRevenueByPeriodSql);
      
      return res.status(200).json({
        error: false,
        revenue_by_period: revenueByPeriod,
        subscription_revenue_by_period: subscriptionRevenueByPeriod,
        revenue_by_type: revenueByType,
        court_utilization: courtUtilization,
        summary: summary[0] || { 
          total_revenue: 0, 
          total_bookings: 0, 
          unique_customers: 0,
          total_hours_booked: 0,
          average_booking_value: 0 
        }
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get club subscriptions
  app.get(`${base}/subscriptions`, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      // Get the club ID from the authenticated user
      sdk.setTable("clubs");
      const clubs = await sdk.get({ user_id: req.user_id });
      
      if (!clubs.length) {
        return res.status(404).json({
          error: true,
          message: "Club not found for this user"
        });
      }
      
      const club_id = clubs[0].id;
      
      const { 
        status, 
        date_start, 
        date_end, 
        user_id,
        limit = 20,
        offset = 0
      } = req.query;
      
      // Build SQL query with filters
      let sql = `
        SELECT 
          s.id,
          s.stripe_id,
          s.price_id,
          s.status,
          s.create_at,
          s.update_at,
          s.user_id,
          s.cancel_at,
          s.current_period_start,
          s.current_period_end,
          s.plan_id,
          s.price_type,
          s.amount,
          s.interval,
          s.interval_count,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          p.name AS plan_name,
          p.description AS plan_description,
          'Subscription' as invoice_type
        FROM 
          courtmatchup_stripe_subscription s
        JOIN 
          courtmatchup_user u ON s.user_id = u.id
        LEFT JOIN
          courtmatchup_plans p ON s.plan_id = p.id
        WHERE 
          s.club_id = ${club_id}
          AND s.object IS NOT NULL
      `;
      
      // Add filters
      if (status) {
        sql += ` AND s.status = '${status}'`;
      }
      
      if (date_start && date_end) {
        sql += ` AND (
          (s.current_period_start BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
          (s.current_period_end BETWEEN '${sqlDateFormat(new Date(date_start))}' AND '${sqlDateFormat(new Date(date_end))}') OR
          (s.current_period_start <= '${sqlDateFormat(new Date(date_start))}' AND s.current_period_end >= '${sqlDateFormat(new Date(date_end))}')
        )`;
      }
      
      if (user_id) {
        sql += ` AND s.user_id = ${parseInt(user_id)}`;
      }
      
      // Count total
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_query`;
      const totalCount = await sdk.rawQuery(countSql);
      
      // Add limit and offset
      sql += ` ORDER BY s.create_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
      
      // Execute query
      const subscriptions = await sdk.rawQuery(sql);
      
      // Get subscription metrics
      const metricsSql = `
        SELECT 
          SUM(s.amount) AS total_subscription_revenue,
          COUNT(*) AS total_subscriptions,
          COUNT(DISTINCT s.user_id) AS unique_subscribers,
          COUNT(CASE WHEN s.status = 'active' THEN 1 ELSE NULL END) AS active_subscriptions,
          COUNT(CASE WHEN s.status = 'canceled' THEN 1 ELSE NULL END) AS canceled_subscriptions
        FROM 
          courtmatchup_stripe_subscription s
        WHERE 
          s.club_id = ${club_id}
      `;
      
      const metrics = await sdk.rawQuery(metricsSql);
      
      return res.status(200).json({
        error: false,
        subscriptions,
        count: totalCount[0].total,
        metrics: metrics[0]
      });
      
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Club Billing API - Get Invoices",
      url: "/v3/api/custom/courtmatchup/club/billing/invoices",
      successPayload: '{"error":false,"invoices":[],"count":0,"summary":{"total_revenue":0,"total_invoices":0}}',
      needToken: true
    },
    {
      method: "GET",
      name: "Club Billing API - Get Invoice Details",
      url: "/v3/api/custom/courtmatchup/club/billing/invoices/:id",
      successPayload: '{"error":false,"invoice":{},"reservations":[]}',
      needToken: true
    },
    {
      method: "GET",
      name: "Club Billing API - Financial Report",
      url: "/v3/api/custom/courtmatchup/club/billing/reports/financial",
      successPayload: '{"error":false,"revenue_by_period":[],"revenue_by_type":[],"court_utilization":[]}',
      needToken: true
    },
    {
      method: "GET",
      name: "Club Billing API - Get Subscriptions",
      url: "/v3/api/custom/courtmatchup/club/billing/subscriptions",
      successPayload: '{"error":false,"subscriptions":[],"count":0,"metrics":{"total_subscription_revenue":0,"total_subscriptions":0,"unique_subscribers":0,"active_subscriptions":0,"canceled_subscriptions":0}}',
      needToken: true
    }
  ];
}; 