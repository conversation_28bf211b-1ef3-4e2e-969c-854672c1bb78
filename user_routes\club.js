const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  // TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/club";

module.exports = function (app) {

  // Get club data
  app.get(base + "/:club_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {club_id} = req.params;
      const {user_id} = req;

      sdk.setTable("clubs")
      const club = await sdk.get({
        id:club_id
      })

      if (!club.length) {
        return res.status(404).json({
          error: true,
          message: "Club not found"
        });
      }

      for (let i = 0; i < club.length; i++) {
        const element = club[i];
        club[i].custom_fields = JSON.parse(element.custom_fields || "{}");
        club[i].availability = JSON.parse(element.availability || "[]");
        club[i].account_details = JSON.parse(element.account_details || "{}");
      }
      let sql = `
      SELECT courtmatchup_sports.id, courtmatchup_sports.status,courtmatchup_sports.name as name, courtmatchup_sports.cancel_hours_before, courtmatchup_sports.allow_cancel_reservation, courtmatchup_sports.club_id, 
      GROUP_CONCAT(cst.name, ';', cst.subtype , ';', cst.id SEPARATOR '==') AS sport_types FROM courtmatchup_sports
        LEFT JOIN courtmatchup_club_sport_type cst ON cst.sport_id = courtmatchup_sports.id
          where courtmatchup_sports.club_id=${club[0].id}
          group by courtmatchup_sports.id
       `;
      const sports = await sdk.rawQuery(sql);

      for (let i = 0; i < sports.length; i++) {
        if (!sports[i].sport_types) {
          // sports[i].sport_types = [{
          //   type: '',
          //   subtype: [],
          //   club_sport_type_id: ''
          // }];
          sports[i].sport_types = null
          continue;
        }
        const sport_types = sports[i].sport_types.split('==');
        const data  = []
        sport_types.forEach((sport_type, index) => {
          const [type, subtype, club_sport_type_id] = sport_type.split(';');
          data.push({
            type: type,
            subtype: subtype ? JSON.parse(subtype || '[]') : null,
            club_sport_type_id: club_sport_type_id
          });
        })
        sports[i].sport_types = data;
      }

      sdk.setTable("club_court")
      const courts = (await sdk.get({club_id: club_id}))

      sdk.setTable("club_pricing")
      const pricing = await sdk.get({club_id: club_id})

      return res.status(200).json({
        error: false,
        model: club[0] || {},
        sports,
        courts,
        pricing
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
