const PasswordService = require("../../../services/PasswordService");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const time_left = ( ) => {}

const reservation_hours_left = async (sdk, user_id) => {

    // check hours used since beginning of the week

    const now = new Date();
    const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 1);
    const endOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 7);

    sdk.getDatabase();
    sdk.setTable("reservation")

    const query = `SELECT COUNT(courtmatchup_reservation_logs.duration) as num FROM courtmatchup_reservation_logs 
                    LEFT JOIN courtmatchup_reservation ON courtmatchup_reservation.id = courtmatchup_reservation_logs.reservation_id
                    LEFT JOIN courtmatchup_booking as b ON b.id = courtmatchup_reservation.booking_id
                    WHERE courtmatchup_reservation.user_id = ${user_id} AND b.date BETWEEN '${sqlDateFormat(startOfWeek)}' AND '${sqlDateFormat(endOfWeek)}'`;
    const num = (await sdk.rawQuery(query))[0].num;
    console.log(num,'<the hours')
    return num;
}


const log_reservation = async (sdk, user_id, reservation_id) => {
    const now = new Date();

    sdk.getDatabase();
    sdk.setTable("reservation_logs")
    await sdk.insert({
        user_id,
        reservation_id,
        create_at: sqlDateFormat(now),
        update_at: sqlDateTimeFormat(now)
    })
    return;
}

const log_activity = async (sdk, user_id, action, details = '') => {
    sdk.getDatabase();
    sdk.setTable("club_admin_activity")
    await sdk.insert({
        user_id,
        action,
        details,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
    })
    return;
}

const validate_account = (acc) => {

    if (!acc) {
        console.log("invalid1");
        throw new Error("Wrong parameter. Expecting object");
    }

    
    
    for (const account of acc) {
        console.log(account);
        if (!account.account_nickname || !(/^[\w\s]+$/.test(account.account_nickname))) {
            throw new Error("Wrong parameter. Account nickname. Expecting string");
        }        
        
        if (!account.account_number || !(/^\d+$/.test(account.account_number))) {
            throw new Error("Wrong parameter. Account number. Expecting string");
        }
        
        if (!account.account_routing_number || !(/^\d+$/.test(account.account_routing_number))) {
            throw new Error("Wrong parameter. Account routing number. Expecting string");
        }
        
        if (!account.account_type || !(/^[\w\s]+$/.test(account.account_type))) {
            throw new Error("Wrong parameter. Account type. Expecting string");
        }
    }
    

    return true;

}

const validate_availability = (availability) => {

    if (!Array.isArray(availability)) {
        throw new Error("Wrong parameter. Expecting array");
    }

    for (const day of availability) {
        if (!day.timeslots || !Array.isArray(day.timeslots)) {
            throw new Error("Wrong parameter. Timeslots. Expecting array");
        }

        // check timeslots using regex
        if (!day.timeslots.every((timeslot) => (/^\d{2}:\d{2}:\d{2}$/.test(timeslot)))) {
            throw new Error("Wrong parameter. Timeslots. Expecting array of time strings");
        }

        if (!day.day || !(/^\w+$/.test(day.day))) {
            throw new Error("Wrong parameter. Day. Expecting string");
        }
    }

    return true;

}
 

const build_coach_availability_query = ({ sport_id, date, start_time, end_time,surface_id=1,type=1, name=null }) => {
    let query = '';
    if (!date){
        query =  `SELECT 
        first_name,last_name, courtmatchup_coach.* From courtmatchup_coach left join courtmatchup_user as u on u.id=courtmatchup_coach.user_id WHERE sport_id = ${sport_id} AND is_public = 1 AND courtmatchup_coach.type = "${type}"`
        if (name){
            query += ` AND (first_name like '%${name}%' OR last_name like '%${name}%')`
        }
    }else{
        // Convert the date to get the day of the week
        const dayOfWeek = new Date(date).toLocaleString('en-us', { weekday: 'long' }).toLowerCase();
    
        // Build the query
        // const query = `
        //   SELECT * FROM courtmatchup_coach
        //   WHERE JSON_CONTAINS(availability, JSON_OBJECT('day', '${dayOfWeek}'), '$')
        //     AND sport_id = '${sport_id}'
        //     AND is_public = 1
        //     AND JSON_CONTAINS(
        //       JSON_EXTRACT(availability, '$[*].timeslots'),
        //       JSON_ARRAY('${start_time}', '${end_time}')
        //     )
        // `;
    
        query = `SELECT 
        first_name,last_name, courtmatchup_coach.*,
                JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day') AS search_result,
                SUBSTRING_INDEX(
                    SUBSTRING_INDEX(
                        JSON_UNQUOTE(
                            JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day')
                        ),
                        '[', -1
                    ),
                    ']', 1
                ) AS extracted_index,
                JSON_EXTRACT(
                    availability,
                    CONCAT('$[', 
                        SUBSTRING_INDEX(
                            SUBSTRING_INDEX(
                                JSON_UNQUOTE(
                                    JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day')
                                ),
                                '[', -1
                            ),
                            ']', 1
                        ),
                        '].timeslots'
                    )
                ) AS extracted_timeslots
            FROM courtmatchup_coach
            left join courtmatchup_user as u on u.id=courtmatchup_coach.user_id
            where JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day') is not null
            and  JSON_CONTAINS(JSON_EXTRACT(
                    availability,
                    CONCAT('$[', 
                        SUBSTRING_INDEX(
                            SUBSTRING_INDEX(
                                JSON_UNQUOTE(
                                    JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day')
                                ),
                                '[', -1
                            ),
                            ']', 1
                        ),
                        '].timeslots'
                    )
                ), '"${start_time}"')
            and  JSON_CONTAINS(JSON_EXTRACT(
                    availability,
                    CONCAT('$[', 
                        SUBSTRING_INDEX(
                            SUBSTRING_INDEX(
                                JSON_UNQUOTE(
                                    JSON_SEARCH(availability, 'one', '${dayOfWeek}', NULL, '$[*].day')
                                ),
                                '[', -1
                            ),
                            ']', 1
                        ),
                        '].timeslots'
                    )
                ), '"${end_time}"')
                
                ;`
            
    
    }
    return query;
  };

  function mergeAvailabilities(availabilities) {
    const mergedAvailability = {};
  
    // Parse each availability JSON and combine timeslots by day
    availabilities.forEach(availability => {
      const daySlots = (availability);
  
      daySlots.forEach(({ day, timeslots }) => {
        // Initialize day if not present
        if (!mergedAvailability[day]) {
          mergedAvailability[day] = new Set();
        }
  
        // Add timeslots, using a Set to avoid duplicates
        timeslots.forEach(slot => mergedAvailability[day].add(slot));
      });
    });
  
    // Convert sets back to arrays and sort times
    const finalAvailability = Object.entries(mergedAvailability).map(([day, timeslotSet]) => ({
      day,
      timeslots: Array.from(timeslotSet).sort()
    }));
  
    return finalAvailability;
  }
  const generateAvailabilityForNextMonth = (availableHours) => {
    const availability = [];
    const now = new Date();
    const nextHour = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours() + 1, 0, 0);
    const nextMonth = now.getMonth() + 1; // get next month
    const year = now.getFullYear() + (nextMonth > 11 ? 1 : 0);
    const targetMonth = nextMonth % 12;
    const targetDate = new Date(year, targetMonth, now.getDate());
    
    // Handle cases where the target month has fewer days
    const endOfNextMonth = new Date(year, targetMonth + 1, 0); // Last day of the next month
    const endDate = targetDate > endOfNextMonth ? endOfNextMonth : targetDate;

    // Days of the week mapping
    const dayMap = {
        sunday: 0,
        monday: 1,
        tuesday: 2,
        wednesday: 3,
        thursday: 4,
        friday: 5,
        saturday: 6
    };
    console.log(availableHours, typeof availableHours)

    // Go through each day and timeslot
    availableHours.forEach(daySchedule => {
        const dayOfWeek = dayMap[daySchedule.day.toLowerCase()];

        // Loop through each day from today to the end date
        let date = new Date(nextHour);
        while (date <= endDate) {
            if (date.getDay() === dayOfWeek) {
                daySchedule.timeslots.forEach(time => {
                    const [hour, minute] = time.split(":").map(Number);
                    const timeSlotDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), hour, minute);

                    // Only include slots from the next hour onward
                    const endDateTime = new Date(timeSlotDate.getTime() + 60 * 60 * 1000); // Add 1 hour
                    availability.push({
                        start: timeSlotDate.toISOString(),
                        end: endDateTime.toISOString()
                    });
                });
            }
            date.setDate(date.getDate() + 1); // Move to the next day
        }
    });

    return availability;
};

function mapTimeslotsWithDuration(availability) {
    return availability.map(dayObj => {
        const mappedTimeslots = dayObj.timeslots.map(time => {
            // Convert time string to hour, minute, and second parts
            const [hour, minute, second] = time.split(":").map(Number);

            // Add 1 hour to the current hour
            // const nextHour = (hour + 1).toString().padStart(2, "0");

            // Construct the time range in the desired format
            return `${time}`;
            // return `${time}-${nextHour}:${minute.toString().padStart(2, "0")}:${second.toString().padStart(2, "0")}`;
        });

        // Return the updated object with mapped timeslots
        return {
            day: dayObj.day,
            timeslots: mappedTimeslots,
        };
    });
}



// Function to format time in MySQL TIME format (HH:MM:SS)
function formatTimeForMySQL(timeString) {
    // Split the time string into hours and minutes
    const [hours, minutes] = timeString.split(":");
    // Return the formatted time string
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:00`;
}

const getDateRange = (option) =>{
    const today = new Date();
    let startDate, endDate;

    switch (option) {
        case "Today":
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case "Past Week":
            startDate = new Date(today.setDate(today.getDate() - 7)).toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case "Past Month":
            startDate = new Date(today.setMonth(today.getMonth() - 1)).toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case "This Year":
            startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
    }

    return { startDate, endDate };
}


const generate_statistics_queries = (filters) => {
    const { start_date: startDate, end_date: endDate, start_time: startTime, end_time: endTime, filter } = filters;
    const queries = {};
    const {coach_id=null, clinic_id=null, court_id=null, club_id=null} = filter;

    // 1. Clinic Reservations (Hours, Revenue)
    queries.clinicReservations = `
        SELECT 
            SUM(TIMESTAMPDIFF(HOUR, c.start_time, c.end_time)) AS total_hours,
            SUM(cost_per_head * max_participants) AS total_revenue
        FROM courtmatchup_clinics as c
        LEFT JOIN courtmatchup_clinic_coaches as b ON c.id=b.clinic_id
        WHERE c.date BETWEEN '${startDate}' AND '${endDate}'
        AND c.start_time >= '${startTime}' AND c.end_time <= '${endTime}'
        ${clinic_id ? `AND c.id = ${clinic_id}` : ''}
        ${coach_id ? `AND b.coach_id = ${coach_id}` : '' }
        ;
    `;

    // 2. Coach Reservations (Hours, Revenue)
    queries.coachReservations = `
        SELECT 
            SUM(duration) AS total_hours,
            SUM(price) AS total_revenue
        FROM courtmatchup_booking
        WHERE coach_id IS NOT NULL
        AND date BETWEEN '${startDate}' AND '${endDate}'
        AND start_time >= '${startTime}' AND end_time <= '${endTime}'
        ${coach_id ? `AND coach_id = ${coach_id}` : ''}
        ${court_id ? `AND court_id = ${court_id}` : ''}
        ;
    `;

    // 3. Total Reservations (Hours, Revenue)
    queries.totalReservations = `
        SELECT 
            (clinic_hours.total_hours + coach_hours.total_hours) AS total_hours,
            (clinic_hours.total_revenue + coach_hours.total_revenue) AS total_revenue
        FROM (
            SELECT 
                SUM(TIMESTAMPDIFF(HOUR, c.start_time, c.end_time)) AS total_hours,
                SUM(cost_per_head * max_participants) AS total_revenue
            FROM courtmatchup_clinics as c
            LEFT JOIN courtmatchup_clinic_coaches as b ON c.id=b.clinic_id
            WHERE c.date BETWEEN '${startDate}' AND '${endDate}'
            AND c.start_time >= '${startTime}' AND c.end_time <= '${endTime}'
            ${clinic_id ? `AND c.id = ${clinic_id}` : ''}
            ${coach_id ? `AND b.coach_id = ${coach_id}` : '' }
            
        ) AS clinic_hours,
        (
            SELECT SUM(duration) AS total_hours,
                   SUM(price) AS total_revenue
            FROM courtmatchup_booking
            WHERE coach_id IS NOT NULL
            AND date BETWEEN '${startDate}' AND '${endDate}'
            AND start_time >= '${startTime}' AND end_time <= '${endTime}'
            ${coach_id ? `AND coach_id = ${coach_id}` : ''}
            ${court_id ? `AND court_id = ${court_id}` : ''}
        ) AS coach_hours;
    `;

    // 4. Percentage of Revenue Made Through Each Module (Pie Chart)
    queries.revenueByModule = `
        SELECT 
            'Clinics' AS module,
            SUM(cost_per_head * max_participants) AS revenue
        FROM courtmatchup_clinics as c
        LEFT JOIN courtmatchup_clinic_coaches as b ON c.id=b.clinic_id
        WHERE date BETWEEN '${startDate}' AND '${endDate}'
        ${clinic_id ? `AND c.id = ${clinic_id}` : ''}
        ${coach_id ? `AND b.coach_id = ${coach_id}` : ''}
        UNION
        SELECT 
            'Coaching' AS module,
            SUM(price) AS revenue
        FROM courtmatchup_booking
        WHERE coach_id IS NOT NULL
        AND date BETWEEN '${startDate}' AND '${endDate}'
        ${coach_id ? `AND coach_id = ${coach_id}` : ''};
    `;

    // 5. Revenue by Date Range (Bar Chart)
    queries.revenueByDateRange = `
        SELECT 
            clinics.date,
            SUM(cost_per_head * max_participants) AS clinic_revenue,
            SUM(price) AS coach_revenue
        FROM courtmatchup_clinics AS clinics
        LEFT JOIN courtmatchup_booking AS booking ON clinics.id = booking.clinic_id
        WHERE clinics.date BETWEEN '${startDate}' AND '${endDate}'
        ${clinic_id ? `AND id = ${clinic_id}` : ''}
        GROUP BY date;
    `;

    // 6. Revenue Heat Map (Date Range)
    queries.revenueHeatMap = `
        SELECT 
            combined.date, 
            SUM(revenue) AS total_revenue
        FROM (
            SELECT date, cost_per_head * max_participants AS revenue
            FROM courtmatchup_clinics
            WHERE date BETWEEN '${startDate}' AND '${endDate}'
            ${clinic_id ? `AND id = ${clinic_id}` : ''}
            UNION ALL
            SELECT date, price AS revenue
            FROM courtmatchup_booking
            WHERE date BETWEEN '${startDate}' AND '${endDate}'
            ${coach_id ? `AND coach_id = ${coach_id}` : ''}
            
        ) AS combined
        GROUP BY date;
    `;

    // 1. Find Buddy Module Statistics
    queries.buddyStatistics = `
        SELECT 
            SUM(TIMESTAMPDIFF(HOUR, b.start_time, b.end_time)) AS total_hours,
            SUM(bk.price) AS total_revenue
        FROM courtmatchup_buddy b
        LEFT JOIN courtmatchup_booking bk ON b.reservation_id = bk.id
        JOIN
          courtmatchup_reservation r ON bk.id = r.booking_id
        WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
        AND b.start_time >= '${startTime}' AND b.end_time <= '${endTime}'
        ${club_id ? `AND r.club_id = ${club_id}` : ''};
    `;

    // 2. Total Expenses
    queries.totalExpenses = `
        SELECT 
            SUM(TIMESTAMPDIFF(HOUR, b.start_time, b.end_time)) AS total_hours,
            SUM(b.price) AS total_expense
        FROM courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
        AND b.start_time >= '${startTime}' AND b.end_time <= '${endTime}'
        AND b.status = 1
        ${club_id ? `AND r.club_id = ${club_id}` : ''};
    `;

    // 3. Total Revenue (All Sources)
    queries.totalRevenue = `
        SELECT 
            SUM(TIMESTAMPDIFF(HOUR, b.start_time, b.end_time)) AS total_hours,
            SUM(b.price) AS total_revenue
        FROM courtmatchup_booking b
        JOIN
          courtmatchup_reservation r ON b.id = r.booking_id
        WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
        AND b.start_time >= '${startTime}' AND b.end_time <= '${endTime}'
        AND b.status = 1
        ${club_id ? `AND r.club_id = ${club_id}` : ''};
    `;

    // 4. Total Profit (Revenue - Expenses)
    queries.totalProfit = `
        WITH revenue AS (
            SELECT SUM(b.price) as total_revenue
            FROM courtmatchup_booking b
            JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
            AND b.status = 1
            ${club_id ? `AND r.club_id = ${club_id}` : ''}
        ),
        expenses AS (
            SELECT SUM(b.price) as total_expense
            FROM courtmatchup_booking b
            JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
            AND b.status = 1
            ${club_id ? `AND r.club_id = ${club_id}` : ''}
        )
        SELECT 
            (revenue.total_revenue - COALESCE(expenses.total_expense, 0)) as total_profit,
            revenue.total_revenue,
            COALESCE(expenses.total_expense, 0) as total_expense
        FROM revenue, expenses;
    `;

    // 5. Court Utilization
    queries.courtUtilization = `
        WITH available_hours AS (
            SELECT 
                COUNT(DISTINCT id) * 
                TIMESTAMPDIFF(HOUR, '${startTime}', '${endTime}') * 
                DATEDIFF('${endDate}', '${startDate}') as total_available_hours
            FROM courtmatchup_club_court
            WHERE ${club_id ? `club_id = ${club_id}` : '1=1'}
        ),
        used_hours AS (
            SELECT SUM(duration) as total_used_hours
            FROM courtmatchup_booking b
            JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            WHERE b.date BETWEEN '${startDate}' AND '${endDate}'
            AND b.start_time >= '${startTime}' AND b.end_time <= '${endTime}'
            AND b.status = 1
            ${court_id ? `AND b.court_id = ${court_id}` : ''}
            ${club_id ? `AND r.club_id = ${club_id}` : ''}
        )
        SELECT 
            (used_hours.total_used_hours / available_hours.total_available_hours) * 100 as utilization_percentage,
            used_hours.total_used_hours,
            available_hours.total_available_hours
        FROM available_hours, used_hours;
    `;

    // 6. Lesson Reservations
    queries.lessonReservations = `
        SELECT 
            SUM(duration) AS total_hours,
            SUM(price) AS total_revenue
        FROM courtmatchup_booking
        LEFT JOIN courtmatchup_reservation r ON courtmatchup_booking.id = r.booking_id
        WHERE lesson_id IS NOT NULL
        AND date BETWEEN '${startDate}' AND '${endDate}'
        AND start_time >= '${startTime}' AND end_time <= '${endTime}'
        AND courtmatchup_booking.status = 1
        ${coach_id ? `AND coach_id = ${coach_id}` : ''}
        ${club_id ? `AND r.club_id = ${club_id}` : ''};
    `;
    
    // 7. Revenue by Day of Week for Bar Chart (Clinic vs Coach)
    queries.revenueByDay = `
        SELECT 
            DAYNAME(b.date) as day,
            'clinic' as module,
            SUM(COALESCE(c.cost_per_head * c.max_participants, 0)) as revenue
        FROM 
            courtmatchup_booking b
        LEFT JOIN
            courtmatchup_clinics c ON b.clinic_id = c.id
        LEFT JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
            b.date BETWEEN '${startDate}' AND '${endDate}'
            AND b.clinic_id IS NOT NULL
            ${club_id ? `AND r.club_id = ${club_id}` : ''}
            AND b.status = 1
        GROUP BY 
            DAYNAME(b.date)
        
        UNION ALL
        
        SELECT 
            DAYNAME(b.date) as day,
            'coach' as module,
            SUM(COALESCE(b.price, 0)) as revenue
        FROM 
            courtmatchup_booking b
        LEFT JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
        WHERE 
            b.date BETWEEN '${startDate}' AND '${endDate}'
            AND b.coach_id IS NOT NULL
            ${club_id ? `AND r.club_id = ${club_id}` : ''}
            AND b.status = 1
        GROUP BY 
            DAYNAME(b.date)
        
        ORDER BY 
            FIELD(day, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
            module
    `;
    
    // Return generated queries
    return queries;
}
  

// Check if staff has permission for a specific action
async function check_staff_permission(sdk, user_id, action) {
  // Get staff profile to determine club_id and permission_level
  sdk.setTable("staff");
  const staff = await sdk.get({
    user_id: user_id,
  });
  
  if (!staff.length) {
    return false;
  }
  
  const staffData = staff[0];
  const role = 'staff'; 
  
  // First check custom permissions for this staff member
  if (staffData.permission_level) {
    try {
      const permissions = JSON.parse(staffData.permission_level);
      if (typeof permissions === 'object' && permissions !== null) {
        // Check if permission exists for their role
        if (permissions[role] && typeof permissions[role] === 'object') {
          return !!permissions[role][action];
        }
      }
    } catch (e) {
      console.error("Error parsing staff permissions:", e);
    }
  }
  
  // If no individual permissions or not found, check club-wide permissions
  sdk.setTable("club_permissions");
  const clubPermissions = await sdk.get({
    club_id: staffData.club_id,
    role: role
  });
  
  if (clubPermissions.length > 0 && clubPermissions[0].permission) {
    try {
      const permData = JSON.parse(clubPermissions[0].permission);
      // Check if permission exists for their role
      if (permData[role] && typeof permData[role] === 'object') {
        return !!permData[role][action];
      }
    } catch (e) {
      console.error("Error parsing club permissions:", e);
    }
  }
  
  return false;
}

/**
 * Generates availability and unavailability data for coaches, club courts or staff
 * 
 * @param {Object} params - Function parameters
 * @param {string} params.type - The type of entity ('coach', 'club_court', or 'staff')
 * @param {Object} params.entity - The entity object containing availability data
 * @param {Array} params.bookings - Array of existing bookings for the entity
 * @param {Date|string} [params.from_date] - Optional start date for availability window
 * @param {Date|string} [params.until_date] - Optional end date for availability window
 * @param {string} [params.from_time] - Optional start time filter (HH:MM:SS)
 * @param {string} [params.until_time] - Optional end time filter (HH:MM:SS)
 * @returns {Object} Object containing availability and unavailability arrays
 */
const generateAvailabilityData = (params) => {
    const { type, entity, bookings = [], from_date, until_date, from_time, until_time } = params;

    // Parse availability JSON if it's a string
    let availabilityData = [];
    try {
        if (entity.availability) {
            availabilityData = typeof entity.availability === 'string' 
                ? JSON.parse(entity.availability) 
                : entity.availability;
                
            // Ensure availabilityData is an array
            if (!Array.isArray(availabilityData)) {
                console.error(`Availability data for ${type} is not an array:`, availabilityData);
                availabilityData = [];
            }
        } else {
            console.log(`No availability data found for ${type} with ID: ${entity.id || 'unknown'}`);
        }
    } catch (error) {
        console.error(`Error parsing availability for ${type}:`, error);
        availabilityData = [];
    }

    // Set date range
    const now = new Date();
    const startDate = from_date ? new Date(from_date) : new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // Default to 30 days in the future if until_date not provided
    let endDate;
    if (until_date) {
        endDate = new Date(until_date);
    } else {
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 30);
    }

    // Set time range
    const startTimeObj = from_time ? parseTimeString(from_time) : { hour: 0, minute: 0 };
    const endTimeObj = until_time ? parseTimeString(until_time) : { hour: 23, minute: 59 };

    // Create a map of booked dates and times
    const bookedSlots = new Map();
    if (bookings && bookings.length > 0) {
        bookings.forEach(booking => {
            if (!booking.date || !booking.start_time || !booking.end_time) {
                return; // Skip invalid bookings
            }
            
            const bookingDate = new Date(booking.date);
            const dateKey = bookingDate.toISOString().split('T')[0];
            
            if (!bookedSlots.has(dateKey)) {
                bookedSlots.set(dateKey, []);
            }
            
            bookedSlots.get(dateKey).push({
                start: booking.start_time,
                end: booking.end_time
            });
        });
    }

    // Days of the week mapping
    const dayMap = {
        sunday: 0,
        monday: 1,
        tuesday: 2,
        wednesday: 3,
        thursday: 4,
        friday: 5,
        saturday: 6
    };

    const availability = [];
    const unavailability = [];

    // Loop through each date in the range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const dayOfWeek = currentDate.getDay();
        
        // Find the corresponding day in the availability data (safely)
        const daySchedule = availabilityData.find(
            d => d && d.day && dayMap[d.day.toLowerCase()] === dayOfWeek
        );
        
        if (daySchedule && daySchedule.timeslots && Array.isArray(daySchedule.timeslots) && daySchedule.timeslots.length > 0) {
            // This day has some availability slots
            daySchedule.timeslots.forEach(timeSlot => {
                if (!timeSlot) return; // Skip invalid timeslots
                
                let timeObj;
                try {
                    timeObj = parseTimeString(timeSlot);
                } catch (e) {
                    console.error(`Error parsing time: ${timeSlot}`, e);
                    return; // Skip this timeslot
                }
                
                // Skip this slot if it's outside the requested time range
                if (isTimeOutsideRange(timeObj, startTimeObj, endTimeObj)) {
                    return;
                }
                
                const slotDate = new Date(
                    currentDate.getFullYear(),
                    currentDate.getMonth(),
                    currentDate.getDate(),
                    timeObj.hour,
                    timeObj.minute
                );
                
                // Skip past slots
                if (slotDate < now) {
                    return;
                }
                
                // End time is 1 hour after start (or custom duration based on type)
                const endSlotDate = new Date(slotDate.getTime() + 60 * 60 * 1000);
                
                // Check if this slot is booked
                const isBooked = isSlotBooked(dateStr, timeSlot, bookedSlots);
                
                const slot = {
                    start: slotDate.toISOString(),
                    end: endSlotDate.toISOString(),
                    date: dateStr,
                    time: timeSlot
                };
                
                if (isBooked) {
                    unavailability.push(slot);
                } else {
                    availability.push(slot);
                }
            });
        }
        
        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return { availability, unavailability };
};

/**
 * Parse a time string in format HH:MM:SS into an object with hour and minute
 * Handles various time formats
 */
function parseTimeString(timeStr) {
    // Ensure timeStr is a string
    if (typeof timeStr !== 'string') {
        console.error('Invalid time string:', timeStr);
        return { hour: 0, minute: 0 };
    }
    
    // Handle different time formats
    const parts = timeStr.split(':');
    if (parts.length >= 2) {
        const hour = parseInt(parts[0], 10) || 0;
        const minute = parseInt(parts[1], 10) || 0;
        return { hour, minute };
    }
    
    // Default fallback
    console.error('Unrecognized time format:', timeStr);
    return { hour: 0, minute: 0 };
}

/**
 * Check if a time is outside the specified range
 */
function isTimeOutsideRange(timeObj, startTimeObj, endTimeObj) {
    const timeValue = timeObj.hour * 60 + timeObj.minute;
    const startValue = startTimeObj.hour * 60 + startTimeObj.minute;
    const endValue = endTimeObj.hour * 60 + endTimeObj.minute;
    
    return timeValue < startValue || timeValue > endValue;
}

/**
 * Check if a time slot is booked
 */
function isSlotBooked(dateStr, timeSlot, bookedSlots) {
    if (!bookedSlots.has(dateStr)) {
        return false;
    }
    
    const [slotHour, slotMinute] = timeSlot.split(':').map(Number);
    const slotStart = slotHour * 60 + slotMinute;
    const slotEnd = slotStart + 60; // Assuming 1 hour slots
    
    return bookedSlots.get(dateStr).some(booking => {
        const [bookingStartHour, bookingStartMinute] = booking.start.split(':').map(Number);
        const [bookingEndHour, bookingEndMinute] = booking.end.split(':').map(Number);
        
        const bookingStart = bookingStartHour * 60 + bookingStartMinute;
        const bookingEnd = bookingEndHour * 60 + bookingEndMinute;
        
        // Check for overlap
        return (slotStart < bookingEnd && slotEnd > bookingStart);
    });
}
const empty = function (value) {
    return value === "" || value === null || value === undefined || value === "undefined" || value === "null";
  };
  
function filterEmptyFieldsAllowEmptyString(object, fields = ['type', 'sub_type', 'subtype']) {
    Object.keys(object).forEach((key) => {
      // For specified fields, only filter null/undefined
      if (fields.includes(key)) {
        if (object[key] === null || object[key] === undefined || object[key] === "undefined" || object[key] === "null") {
          delete object[key];
        }
      } else {
        // For other fields, use normal empty check
        if (empty(object[key])) {
          delete object[key];
        }
      }
    });
    return object;
  };

async function reset(sdk, projectId, token, password) {
    sdk.getDatabase();
    sdk.setTable("user");
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.get({
        id: token.user_id
      });

      if (exist) {
        // Instead of comparing hashes directly, verify if the new password matches the old one
        if (await PasswordService.compareHash(password, exist[0].password)) {
            throw new Error("New password cannot be the same as the old password");
        }
        const hashPassword = await PasswordService.hash(password);
        await sdk.update(
          {
            password: hashPassword,
            update_at: sqlDateTimeFormat(new Date())
          },
          exist[0].id
        );

        if (exist[0].id) {
          sdk.setTable("token");
          await sdk.delete({}, token.id);
          return exist[0].id;
        } else {
          throw new Error("Password Update Failed");
        }
      } else {
        throw new Error("Invalid User");
      }
    } catch (error) {
      console.log("error", error);
      return error.message;
    }
  }

/**
 * Generates default availability based on club opening and closing times
 * @param {string} openingTime - Club opening time in HH:MM:SS format
 * @param {string} closingTime - Club closing time in HH:MM:SS format
 * @param {Array<string>} daysOff - Array of days off (e.g. ["Sunday", "Saturday"])
 * @param {number} intervalMinutes - Interval between slots in minutes (default: 60)
 * @returns {Array} - Array of availability objects with day and timeslots
 */
const generateDefaultAvailability = (openingTime, closingTime, daysOff = [], intervalMinutes = 60) => {
  // Days of the week
  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
  
  // Filter out days off
  const availableDays = days.filter(day => !daysOff.includes(day));
  
  // Parse opening and closing times
  const [openHour, openMinute] = openingTime.split(":").map(Number);
  const [closeHour, closeMinute] = closingTime.split(":").map(Number);
  
  // Convert to minutes for easier calculation
  const openingMinutes = openHour * 60 + openMinute;
  const closingMinutes = closeHour * 60 + closeMinute;
  
  // Generate availability for each day
  const availability = availableDays.map(day => {
    const timeslots = [];
    
    // Generate slots from opening to closing time at specified intervals
    for (let minutes = openingMinutes; minutes < closingMinutes; minutes += intervalMinutes) {
      const hour = Math.floor(minutes / 60);
      const minute = minutes % 60;
      
      // Format as HH:MM:SS
      const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
      timeslots.push(timeSlot);
    }
    
    return {
      day,
      timeslots
    };
  });
  
  return availability;
};

module.exports = {
    reservation_hours_left,
    log_reservation,
    log_activity,
    validate_account,
    validate_availability,
    build_coach_availability_query,
    generateAvailabilityForNextMonth,
    getDateRange,
    generate_statistics_queries,
    formatTimeForMySQL,
    mergeAvailabilities,
    mapTimeslotsWithDuration,
    check_staff_permission,
    reset,
    generateAvailabilityData,
    filterEmptyFieldsAllowEmptyString,
    generateDefaultAvailability,
};