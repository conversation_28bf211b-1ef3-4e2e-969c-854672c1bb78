const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];


module.exports = function (app) {
  app.get("/v3/api/custom/cedric/customer/profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      console.log(req.user_id);
      const result = await sdk.get({
        id: req.user_id,
      });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      sdk.setTable("customer_profile")
      const profile = await sdk.get({
        user_id: req.user_id,
      })
      if (typeof profile == "string") {
        return res.status(403).json({
          error: true,
          message: profile,
        });
      }

      const profileObject = { ...profile[0] }

      delete profileObject.id
      delete profileObject.user_id
      delete profileObject.hidden
      // delete profileObject.categories
      // delete profileObject.islands
      // delete profileObject.favorites

      return res.status(200).json({
        first_name: result[0].first_name,
        email: result[0].email,
        role: result[0].role,
        main_role: result[0].main_role,
        last_name: result[0].last_name,
        phone: result[0].phone ?? "",
        photo: result[0].photo ?? "",
        ...profileObject,
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  app.post("/v3/api/custom/customer/profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      console.log(result, 'customer result');

      const { categories, islands } = req.body;

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      })
      let updateResult = null
      if (user.length > 0) {
        updateResult = await sdk.update(filterEmptyFields({
          categories: JSON.stringify(categories),
          islands: JSON.stringify(islands),
        }), user[0].id);
      } else {
        updateResult = await sdk.insert(filterEmptyFields({
          categories: JSON.stringify(categories),
          islands: JSON.stringify(islands),
          user_id: req.user_id,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }));
      }


      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  app.post("/v3/api/custom/customer-profile", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      console.log(result, 'customer result');

      const { categories, islands } = req.body;

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("customer_profile");
      const user = await sdk.get({
        user_id: req.user_id
      })
      let updateResult = null
      if (user.length > 0) {
        updateResult = await sdk.update(filterEmptyFields({
          categories: JSON.stringify(categories),
          islands: JSON.stringify(islands),
        }), user[0].id);
      } else {
        updateResult = await sdk.insert(filterEmptyFields({
          categories: JSON.stringify(categories),
          islands: JSON.stringify(islands),
          user_id: req.user_id,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }));
      }


      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  app.post("/v3/api/custom/cedric/update/password", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }
      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }
      const hash = result[0].password;
      const passwordValid = await PasswordService.compareHash(req.body.current_password, hash);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Password"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated Password"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/custom/customer/profile-edit", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id,
      });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      sdk.setTable("customer_profile");

      const updateResult = await sdk.insert({
        first_name: req.body.payload.first_name,
        last_name: req.body.payload.last_name,
        photo: req.body.payload.photo ?? null
      });

      if (updateResult == null) {
        return res.status(403).json({
          error: true,
          message: updateResult,
        });
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Profile API",
      url: "/v3/api/custom/profile",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
      ],
      needToken: true
    }
  ];
};
