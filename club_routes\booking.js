const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const StripeService = require("../../../services/StripeService.js");
const { reservation_hours_left, log_reservation } = require("../utils/util.js");
const ValidationService = require("../../../services/ValidationService.js");
const SyncStripeWebhook = require("../../../middleware/SyncStripeWebhook");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  SyncStripeWebhook
];

const base = "/v3/api/custom/courtmatchup/club/";

module.exports = function (app) {
  const stripe = new StripeService();
  
  app.post("/v3/api/custom/courtmatchup/stripe/product", middlewares, TokenMiddleware({ role: "club" }), async function (req, res) {
    try {
      /**
       * create product
       */
      const sdk = req.sdk;
      const { name, description } = req.body;
      const validationResult = await ValidationService.validateObject({ name: "required" }, { name });

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }

      sdk.setProjectId(req.projectId);
      sdk.setTable("stripe_product");

      const product = await stripe.createStripeProduct({
        name: name,
        description: description,
        metadata: {
          projectId: sdk.getProjectId()
        }
      });

      const data = await sdk.insert({
        stripe_id: product.id,
        name: name,
        club_id: req.body.club_id,
        object: JSON.stringify(product),
        status: 1,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        model: data,
        message: "Product created successfully"
      });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message ?? "Something went wrong"
      };
      res.status(500).json(payload);
    }
  });
  app.post("/v3/api/custom/courtmatchup/stripe/price", middlewares, TokenMiddleware({ role: "club" }), async function (req, res) {
    try {
      const sdk = req.sdk;
      /**
       * create plan/price
       * req.body.product_id is the db id of product
       */
      // pay per seat metadata_obj.multitenant = 1
      // pay per usage metadata_obj.multitenant_usage = 1
      let { product_id, name, pay_per_usage, pay_per_seat, amount, type, interval, interval_count, trial_days, usage_type, usage_limit } = req.body;
      const validationResult = await ValidationService.validateObject(
        { product_id: "required", name: "required", amount: "required", type: "required" },
        { product_id, name, amount, type }
      );

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }
      sdk.setProjectId(req.projectId);
      sdk.setTable("stripe_product");

      const product = await sdk.get({ id: product_id });

      if (!product.length) {
        return res.status(404).json({ error: true, message: "Product not found" });
      }

      const product_stripe_id = product[0]?.stripe_id;

      sdk.setTable("stripe_setting");
      const setting = await sdk.get({ key: "currency" });

      let currency = stripe.getConfig().currency ?? "usd";
      if (setting[0]) currency = setting[0].value;
      currency = currency.toLowerCase();

      // if type recurring check if it is metered or not
      let price, model;
      sdk.setTable("stripe_price");

      const metadata = {
        projectId: sdk.getProjectId(),
        ...(pay_per_seat && { multitenant: 1 }),
        ...(pay_per_usage && { multitenant_usage: 1 })
      };

      if (type === "one_time") {
        price = await stripe.createStripeOnetimePrice({
          productId: product_stripe_id,
          name,
          amount,
          currency,
          metadata
        });

        model = await sdk.insert({
          name: name,
          amount: parseFloat(amount),
          stripe_id: price.id,
          type: "one_time",
          product_id: product[0].id,
          object: JSON.stringify(price),
          status: 1,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      } else if (type === "recurring") {
        if (usage_type === "licenced") {
          price = await stripe.createStripeRecurringPrice({
            productId: product_stripe_id,
            name,
            amount,
            currency,
            interval,
            interval_count,
            trial_days,
            metadata
          });
        } else if (usage_type === "metered") {
          price = await stripe.createStripeRecurringMeteredPrice({
            productId: product_stripe_id,
            name,
            amount,
            currency,
            usage_limit,
            usage_type,
            interval,
            interval_count,
            trial_days,
            metadata
          });
        }

        model = await sdk.insert({
          stripe_id: price.id,
          product_id: product[0].id,
          name: name,
          object: JSON.stringify(price),
          amount: parseFloat(amount),
          type: interval === "lifetime" ? "lifetime" : type,
          is_usage_metered: usage_type === "metered" ? true : false,
          usage_limit: type === "one_time" ? null : !isNaN(+usage_limit) && usage_type === "metered" ? +usage_limit : null,
          status: 1,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      }

      res.status(200).json({
        error: false,
        model: model
      });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message ?? "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
  ];
};
