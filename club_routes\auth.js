const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const MailService = require("../../../services/MailService");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const MkdEventService = require("../../../services/MkdEventService");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const ValidationService = require("../../../services/ValidationService");
const {
  sqlDateFormat,
  sqlDateTimeFormat,
} = require("../../../services/UtilService");
const { ERROR_MESSAGES } = require("../utils/constants");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware
];

let logService = new DevLogService();

module.exports = function (app) {
  app.post("/v3/api/custom/courtmatchup/club/register", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      let verify = false;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;
      if (!req.body.email) {
        return res.status(403).json({
          error: true,
          message: "Email Missing",
          validation: [{ field: "email", message: "Email missing" }]
        });
      }
      if (!req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Role Missing",
          validation: [{ field: "role", message: "Role missing" }]
        });
      }
      if (req.body.role === "admin") {
        verify = true;
        const userData = JwtService.verifyAccessToken(JwtService.getToken(req), config.jwt_key);
        if (!userData || userData.role != "admin") {
          return res.status(403).json({
            error: true,
            message: "Admin can't be registered using this API",
            validation: [{ field: "role", message: "Role (admin) is not allowed" }]
          });
        }
      }
      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password Missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      let service = new AuthService();

      logService.log(req.projectId, req.body.email, req.body.password, req.body.role);
      const result = await service.register(
        sdk,
        req.projectId,
        req.body.email,
        req.body.password,
        req.body.role,
        verify,
        req.body.first_name,
        // req.body.username,
        req.body.last_name,
        req.body.photo,
        req.body.phone ?? null
      );

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      } else {
        const sdk = req.sdk
        sdk.setProjectId(req.projectId);
        let {
          name,
          bio,
          opening_time,
          closing_time,
          splash_screen,
          show_clinic,
          show_buddy,
          show_coach,
          show_groups,
          slug,
          show_court,
          times = [],
          buddy_description,
          court_description,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details,
        } = req.body;
        if (name) {
          name = name.trim();
          sdk.setTable('clubs');
          const clubs = await sdk.get({
            name
          });
          if (clubs.length > 0) {
            return res.status(403).json({
              error: true,
              message: "Name already exists"
            });
          }
        }
        
        sdk.setTable('clubs');
        const club_id = await sdk.insert(filterEmptyFields({
          user_id: result,
          name,
          bio,
          opening_time,
          closing_time,
          splash_screen,
          slug,
          show_clinic,
          show_buddy,
          show_coach,
          show_groups,
          show_court,
          buddy_description,
          court_description,
          coach_description,
          clinic_description,
          lesson_description,
          custom_request_threshold,
          max_players,
          account_details: JSON.stringify( account_details || "[]"),
          times: JSON.stringify(times),
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        }))

        const initialSports = [
          {
            name: "Tennis",
            type: JSON.stringify([]),
            sub_type: JSON.stringify([]),
            status: 0,
            club_id: club_id,
          },
          {
            name: "Pickleball",
            type: JSON.stringify([]),
            sub_type: JSON.stringify([]),
            status: 0,
            club_id: club_id,
          },
        ];
        sdk.setTable('sports');
        for (const sport of initialSports) {
          await sdk.insert(sport);
        }

        const activationToken = JwtService.createAccessToken(
          {
            user_id: result,
            role: req.body.role,
            club_id,
            activation_token: true
          },
          config.jwt_expire,
          config.jwt_key
        );
  
        const MKDSDK = new ManaKnightSDK();
        let project = {};
  
        if (config.env == "production") {
          project = require("../project");
        } else {
          MKDSDK.setProjectId("manaknight");
          MKDSDK.setTable("projects");
  
          project = (
            await MKDSDK.get({
              project_id: req.projectId
            })
          )[0];
        }
  
        const eventService = new MkdEventService(sdk, req.projectId, req.headers);
        const mailResult = await eventService.sendMail(
          {
            email: config.mail_user,
            to: req.body.email,
            from: config.from_mail,
            link: `https://${project.hostname}/${req.body.role}/verify-email?token=${activationToken}`
          },
          "signup-verification"
        );
  
        if (typeof mailResult === "string")
          return res.status(403).json({
            error: true,
            message: mailResult
          });
  
        if (mailResult.error) return res.status(403).json({ error: true, mailResult });

        if (needRefreshToken) {
          refreshToken = JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role,
              club_id
            },
            config.refresh_jwt_expire,
            config.jwt_key
          );
          let expireDate = new Date();
          expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
          await service.saveRefreshToken(req.sdk, req.projectId, result, refreshToken, expireDate);
        }

        return res.status(200).json({
          error: false,
          role: req.body.role,
          token: JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role,
              club_id
            },
            config.jwt_expire,
            config.jwt_key
          ),
          refresh_token: refreshToken,
          expire_at: config.jwt_expire,
          user_id: result
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // custom login
  app.post("/v3/api/custom/courtmatchup/club/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password, role } = req.body;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);

      logService.log(req.projectId, email, password);
      const result = await service.login(req.sdk, req.projectId, email, password, role);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result === "User does not exist" ? "Invalid Username" : result
        });
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      sdk.setTable("clubs")
      const club = (await sdk.get({
        user_id:result.id,
      }))[0]

      if (!club) throw new Error(ERROR_MESSAGES.CLUB_NOT_FOUND)
      
        const club_id = club.id;

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            club_id,
            role: role
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        role,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role,
            club_id
          },
          config.jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: result.id,
        first_name: result.first_name ?? "",
        last_name: result.last_name ?? "",
        photo: result.photo ?? "",
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });



  return [
    {
      method: "POST",
      name: "Register API",
      url: "/v3/api/custom/courtmatchup/register",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
        {
          name: "403",
          body: '{"role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Role Missing","validation": [{ "field": "role", "message": "Role missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    }
  ];
};
