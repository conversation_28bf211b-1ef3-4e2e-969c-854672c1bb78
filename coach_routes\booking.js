const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const StripeService = require("../../../services/StripeService.js");
const stripe = new StripeService();
const { BOOKING_STATUSES, RESERVATION_TYPES } = require("../utils/constants.js");
const { reservation_hours_left, log_reservation } = require("../utils/util.js");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "coach" }),
];

const base = "/v3/api/custom/courtmatchup/coach/bookings";

module.exports = function (app) {
  
  // Get bookings for coach
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { from_time, until_time, from_date, until_date } = req.query;
      
      const now = new Date();
      // Fetch reservations for the user
      sdk.setTable("coach");
      const coach = (await sdk.get({ user_id: req.user_id })) [0];
      if (!coach) {
        return res.status(403).json({ error: true, message: "Coach not found" });
      }
      const effectiveFromDate = from_date ? new Date(from_date) : now;
      const effectiveUntilDate = until_date ? new Date(until_date) : new Date(now.setDate(now.getDate() + 30));
      const fromDateSQL = sqlDateFormat(effectiveFromDate);
      const untilDateSQL = sqlDateFormat(effectiveUntilDate);
      // Time filter conditions
      const timeFilterSQL = (from_time || until_time) ? 
      `${from_time ? `AND start_time >= '${from_time}'` : ''} 
      ${until_time ? `AND end_time <= '${until_time}'` : ''}` : '';
      
      const sql = `
        SELECT 
          b.*,
          b.player_ids,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'id', u.id,
              'first_name', u.first_name,
              'last_name', u.last_name,
              'email', u.email,
              'photo', u.photo
            )
          ) as players
          FROM 
            courtmatchup_booking b
          JOIN
            courtmatchup_reservation r ON b.id = r.booking_id
          LEFT JOIN
            courtmatchup_user u ON FIND_IN_SET(u.id, b.player_ids)
          WHERE
          b.coach_id = ${coach.id}
          AND b.date BETWEEN '${fromDateSQL}' AND '${untilDateSQL}'
          ${timeFilterSQL}
          GROUP BY b.id
      `;

      const reservations = await sdk.rawQuery(sql);
      return res.status(200).json({
        error: false,
        list: reservations.map(r => ({
          ...r,
          players: JSON.parse(r.players || "[]")
        })),
        from_date: effectiveFromDate,
        until_date: effectiveUntilDate,
        from_time,
        until_time,
      });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });

    // Get coach/staff billing history/invoices
    app.get(base + "/billing/coach-invoices", middlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
    
        // Get query parameters
        const { sort = 'desc', invoice_type } = req.query;
        
        // Validate sort parameter
        if (sort !== 'asc' && sort !== 'desc') {
          throw new Error("Sort parameter must be 'asc' or 'desc'");
        }
  
        // First get the user's stripe_uid
        sdk.setTable("user");
        const user = await sdk.get({ id: req.user_id });
        if (!user.length) {
          throw new Error("User not found");
        }
        const stripe_uid = user[0].stripe_uid;
    
        // Get coach/staff earnings from bookings
        let earnings_sql = `
          SELECT 
            b.id,
            b.receipt_id,
            b.coach_fee as amount,
            b.date,
            b.create_at,
            b.status,
            b.reservation_type,
            'Checkout' as payment_method,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS type,
            CASE
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
              WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
              WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
              WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
              ELSE 'unknown'
            END AS invoice_type,
            b.coach_fee AS total_amount,
            c.name as club_name,
            u.first_name as user_first_name,
            u.last_name as user_last_name
          FROM 
            courtmatchup_booking b
          LEFT JOIN
            courtmatchup_clubs c ON b.club_id = c.id
          LEFT JOIN
            courtmatchup_user u ON b.user_id = u.id
          WHERE 
            b.coach_id IN (
              SELECT id FROM courtmatchup_coach WHERE user_id = ${req.user_id}
            )
            AND b.status = ${BOOKING_STATUSES.SUCCESS}
            AND b.coach_fee > 0
        `;
    
        // Add invoice_type filter for bookings if specified
        if (invoice_type) {
          switch(invoice_type.toLowerCase()) {
            case 'court':
              earnings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
              break;
            case 'lesson':
              earnings_sql += ` AND b.lesson_id IS NOT NULL`;
              break;
            case 'clinic':
              earnings_sql += ` AND b.clinic_id IS NOT NULL`;
              break;
          }
        }
  
    
        let earnings = [], subscriptions = [];
        
        // Only fetch relevant data based on invoice_type filter
        if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
          earnings = await sdk.rawQuery(earnings_sql);
        }
        
        // if (!invoice_type || invoice_type.toLowerCase() === 'subscription') {
        //   subscriptions = await sdk.rawQuery(subscriptions_sql);
        // }
    
        // Process subscription data to extract amount and other details
        const processed_subscriptions = subscriptions.map(sub => {
          const obj = JSON.parse(sub.object);
          const paymentMethod = obj.default_payment_method;
          let paymentMethodDisplay = 'Unknown Payment Method';
          
          if (paymentMethod && typeof paymentMethod === 'string') {
            paymentMethodDisplay = 'Credit Card';
          } else if (paymentMethod && paymentMethod.card) {
            paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
          }
  
          return {
            id: sub.id,
            stripe_id: sub.stripe_id,
            subscription_id: obj.id,
            price_id: sub.price_id,
            status: sub.status,
            create_at: sub.create_at,
            invoice_type: 'Subscription',
            type: obj.plan?.nickname || 'Subscription Plan',
            plan_name: obj.plan?.product || 'Standard Plan',
            total_amount: obj.plan?.amount || 0,
            currency: obj.currency || 'usd',
            interval: obj.plan?.interval || 'month',
            payment_method: paymentMethodDisplay,
            club_name: sub.club_name,
            valid_until: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            current_period_start: obj.current_period_start ? 
              new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
            current_period_end: obj.current_period_end ? 
              new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
            canceled_at: obj.canceled_at ? 
              new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
          };
        });
    
        // Format earnings data to match structure
        const processed_earnings = earnings.map(earning => ({
          ...earning,
          currency: 'usd',
          total_amount: parseFloat(earning.amount),
          status: earning.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
          valid_until: earning.date
        }));
    
        // Combine and sort all invoices by create_at date
        const all_invoices = [...processed_earnings, ...processed_subscriptions].sort((a, b) => {
          const dateComparison = new Date(b.create_at) - new Date(a.create_at);
          return sort === 'desc' ? dateComparison : -dateComparison;
        });
    
        // Calculate totals
        const total_earnings = all_invoices.reduce((sum, invoice) => 
          sum + (invoice.total_amount || 0), 0
        );
    
        const active_subscriptions = processed_subscriptions.filter(sub => 
          sub.status === 'active'
        );
    
        // Group invoices by type
        const invoices_by_type = all_invoices.reduce((acc, invoice) => {
          const type = invoice.type;
          if (!acc[type]) {
            acc[type] = [];
          }
          acc[type].push(invoice);
          return acc;
        }, {});
    
        return res.status(200).json({
          error: false,
          invoices: all_invoices,
          invoices_by_type,
          total_earnings: total_earnings / 100, // Convert from cents to dollars
          active_subscriptions: active_subscriptions,
          total_invoices: all_invoices.length,
          filters: {
            sort,
            invoice_type: invoice_type || 'all'
          }
        });
    
      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });

  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
  ];
};
