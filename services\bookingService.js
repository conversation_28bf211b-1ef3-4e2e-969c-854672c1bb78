const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const stripe = new StripeService();
const { BOOKING_TYPES, BOOKING_STATUSES, ERROR_MESSAGES } = require('../utils/constants');


class BookingService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  async createBooking(req,user_id) {
    try {
      let { sport_id, type=1, payment_status=0, clinic_id=null, coach_id=null, court_id=null, lesson_id=null, duration=0, player_ids, date, start_time, end_time, custom_request=null } = req.body;
      let request_id = null;
      // coach booking
      if (type === BOOKING_TYPES.COACH) {
        if (!coach_id) throw new Error(ERROR_MESSAGES.COACH_NOT_FOUND);
      }

      // court booking
      if (type === BOOKING_TYPES.CLUB_COURT) {
        if (!court_id) throw new Error(ERROR_MESSAGES.COURT_NOT_FOUND);
      }

      // clinic booking
      if (type === BOOKING_TYPES.CLINIC) {
        if (!clinic_id) throw new Error(ERROR_MESSAGES.CLINIC_NOT_FOUND);

        this.sdk.setTable("clinic");
        const clinic = (await this.sdk.get({ id: clinic_id }))[0];
        if (!clinic) throw new Error(ERROR_MESSAGES.CLINIC_NOT_FOUND);

        date = clinic.date;
        start_time = clinic.start_time;
        end_time = clinic.end_time;
        sport_id = clinic.sport_id;
        // type = clinic.type;
        // calculate duration based on time
        try{
          const [startHours, startMinutes] = start_time.split(':').map(Number);
          const [endHours, endMinutes] = end_time.split(':').map(Number);
          duration = (endHours * 60 + endMinutes) - (startHours * 60 + startMinutes);
          duration = duration / 60;
        }catch(err){
          console.error(err);
          duration = 0;
        }
      }

      if (!player_ids) throw new Error(ERROR_MESSAGES.PLAYER_IDS_NOT_FOUND);

      this.sdk.setTable("booking");
      const booking_id = await this.sdk.insert({
        user_id,
        sport_id,
        type,
        status: payment_status || BOOKING_STATUSES.PENDING,
        clinic_id,
        coach_id,
        court_id,
        lesson_id,
        custom_request,
        duration,
        payment_intent: null,
        player_ids: JSON.stringify(player_ids),
        date: sqlDateFormat(new Date(date)),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      if (custom_request) {
        this.sdk.setTable("coach_requests");
        request_id = await this.sdk.insert({
          user_id,
          booking_id,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });
      }

      return { success: true, booking_id };
    } catch (error) {
      console.error(ERROR_MESSAGES.BOOKING_ERROR, error);
      throw new Error(ERROR_MESSAGES.BOOKING_ERROR);
    }
  }

  async createPaymentIntent(req, amount) {
    try {
      this.sdk.setTable("user");
      const user = (await this.sdk.get({ id: req.user_id }))[0];

      if (!user) throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);

      let stripe_customer = user.stripe_uid;
      if (!stripe_customer) {
        const customer = await stripe.createStripeCustomer({ email: user.email });
        stripe_customer = customer.id;
        await this.sdk.update({ id: user.id, stripe_uid: stripe_customer });
      }

      const paymentIntent = await stripe.createPaymentIntentAutomatic({
        amount: amount * 100,
        currency: "usd",
        customer: stripe_customer,
      });

      return { success: true, client_secret: paymentIntent.client_secret, payment_intent: paymentIntent.id };
    } catch (error) {
      console.error(ERROR_MESSAGES.PAYMENT_ERROR, error);
      throw new Error(ERROR_MESSAGES.PAYMENT_ERROR);
    }
  }

  async verifyPayment(payment_intent_id, status, booking_id) {
    try {
      this.sdk.setTable("booking");
      await this.sdk.update({
        payment_intent: payment_intent_id,
        status: status === BOOKING_STATUSES.SUCCESS ? BOOKING_STATUSES.SUCCESS : BOOKING_STATUSES.FAIL,
        update_at: sqlDateTimeFormat(new Date()),
      }, booking_id);
      return { success: true };
    } catch (error) {
      console.error(ERROR_MESSAGES.PAYMENT_VERIFICATION_ERROR, error);
      throw new Error(ERROR_MESSAGES.PAYMENT_VERIFICATION_ERROR);
    }
  }
}

module.exports = BookingService;
