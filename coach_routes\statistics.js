const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../middlewares/TokenMiddleware.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query, generateAvailabilityForNextMonth, generate_statistics_queries } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "coach" }),
];

const base = "/v3/api/custom/courtmatchup/coach/statistics";

module.exports = function (app) {

      // fetch coach invoices with filters
      app.get(`${base}/coach-invoices/:coach_id`, middlewares, async function (req, res) {
        try {
          let sdk = req.sdk;
          sdk.getDatabase();
          sdk.setProjectId(req.projectId);
          const coach_id = req.params.coach_id;
  
          let { from: date_start, until: date_end=new Date() } = req.query;
          const { sort = 'desc', invoice_id, receipt_id, invoice_type,  sport_id, booking_type, limit, offset } = req.query;
  
          date_end = date_end ? new Date(date_end) : (new Date()).getDate() + 30;
  
          let sql = `
            SELECT * FROM courtmatchup_booking WHERE coach_id is not null ${coach_id ? `AND coach_id = ${coach_id}` : ''} ${date_start && date_end ? `AND date BETWEEN '${sqlDateFormat(date_start)}' AND '${sqlDateFormat(date_end)}}` : ''}
          `;
          
          if (invoice_type) {
            sql += ` AND invoice_type = ${invoice_type}`;
          }
  
          if (sport_id) {
            sql += ` AND sport_id = ${sport_id}`;
          }
  
          if (booking_type) {
            sql += ` AND booking_type = ${booking_type}`;
          }
  
          if (invoice_id) {
            sql += ` AND id = ${invoice_id}`;
          }
  
          if (receipt_id) {
            sql += ` AND receipt_id = ${receipt_id}`;
          }
  
          if (sort) {
            sql += ` ORDER BY date ${sort}`;
          }
  
          if (limit) {
            sql += ` LIMIT ${limit}`;
          }
  
          if (offset) {
            sql += ` OFFSET ${offset}`;
          }
  
          const coach_invoices = await sdk.rawQuery(sql);
  
          const processed_coach_invoices = coach_invoices.map(invoice => ({
            ...invoice,
            total_amount: parseFloat(invoice.amount),
            status: invoice.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
          }));
  
          return res.status(200).json({
            error: false,
            start_date: date_start,
            end_date: date_end,
            invoices: processed_coach_invoices
          });
  
        } catch (err) {
          console.error(err);
          res.status(403).json({
            error: true,
            message: err.message
          });
        }
      });
  
  // get statistics for coach
  app.get(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { start_date, end_date, start_time, end_time } = req.query;

      if (!start_date || !end_date || !start_time || !end_time) {
        throw new Error("Missing required parameters: start_date, end_date, start_time, end_time");
      }

      const { 
        clinicReservations,
        coachReservations,
        totalReservations,
        revenueHeatMap,
        revenueByDateRange,
        revenueByModule,
        buddyStatistics,
        totalExpenses,
        totalRevenue,
        totalProfit,
        courtUtilization,
        lessonReservations
      } = generate_statistics_queries({start_date, end_date, start_time, end_time, filter:{
        coach_id : req.coach_id,
      }});


      const results = await Promise.all([
        sdk.rawQuery(clinicReservations),
        sdk.rawQuery(coachReservations),
        sdk.rawQuery(totalReservations),
        sdk.rawQuery(revenueHeatMap),
        sdk.rawQuery(revenueByDateRange),
        sdk.rawQuery(revenueByModule),
        sdk.rawQuery(buddyStatistics),
        sdk.rawQuery(totalExpenses),
        sdk.rawQuery(totalRevenue),
        sdk.rawQuery(totalProfit),
        sdk.rawQuery(courtUtilization),
        sdk.rawQuery(lessonReservations)
      ]);
      
      // Map the results to an object
      const model = {
        clinicReservations: results[0],
        coachReservations: results[1],
        totalReservations: results[2],
        revenueHeatMap: results[3],
        revenueByDateRange: results[4],
        revenueByModule: results[5],
        buddyStatistics: results[6],
        totalExpenses: results[7],
        totalRevenue: results[8],
        totalProfit: results[9],
        courtUtilization: results[10],
        lessonReservations: results[11]
      };

      return res.status(200).json({
        error: false,
        model
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
