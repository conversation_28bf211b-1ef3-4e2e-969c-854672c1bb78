{"info": {"name": "CourtMatchup API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Book Clinic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/clinics/book-clinic", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "clinics", "book-clinic"]}, "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user123\",\n  \"clinic_id\": \"clinic456\"\n}"}}}, {"name": "Get Club Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/club/:club_id", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "club", ":club_id"]}, "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user123\"\n}"}}}, {"name": "Book Coach", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/coach/book-coach", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "coach", "book-coach"]}, "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user123\",\n  \"coach_id\": \"coach456\"\n}"}}}, {"name": "Get Coach Availability", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/coach/availability/:coach_id", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "coach", "availability", ":coach_id"]}}}, {"name": "Search Coach by Time Slots", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/coach/search-time-slots", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "coach", "search-time-slots"]}, "body": {"mode": "raw", "raw": "{\n  \"sport_id\": \"1\",\n  \"type\": \"training\",\n  \"date\": \"2024-10-15\",\n  \"start_time\": \"10:00\",\n  \"end_time\": \"12:00\"\n}"}}}, {"name": "Create Group", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/groups/create", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "groups", "create"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Group\",\n  \"members\": [\"user123\", \"user456\"],\n  \"type\": 0\n}"}}}, {"name": "Get Groups", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/groups", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "groups"]}, "body": {"mode": "raw", "raw": "{\n  \"group_id\": \"group123\",\n  \"user_id\": \"user123\"\n}"}}}, {"name": "Join Group", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/v3/api/custom/courtmatchup/user/groups/join-group", "host": ["{{baseUrl}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "groups", "join-group"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000"}]}