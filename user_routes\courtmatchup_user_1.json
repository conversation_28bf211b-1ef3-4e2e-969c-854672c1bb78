{"info": {"name": "CourtMatchup APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"\",\n  \"password\": \"\",\n  \"role\": \"\",\n  \"verify\": false,\n  \"is_refresh\": false,\n  \"first_name\": \"\",\n  \"last_name\": \"\",\n  \"photo\": \"\",\n  \"phone\": \"\",\n  \"address\": \"\",\n  \"city\": \"\",\n  \"state\": \"\",\n  \"country\": \"\",\n  \"gender\": \"\",\n  \"zip_code\": \"\",\n  \"house_no\": \"\",\n  \"date_of_birth\": \"\"\n}"}, "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/register", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "register"]}}}, {"name": "User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"\",\n  \"password\": \"\",\n  \"role\": \"\",\n  \"is_refresh\": false\n}"}, "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/login", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "login"]}}}, {"name": "Get Reservations", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/reservations", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "reservations"]}}}, {"name": "Create Reservation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sport_id\": 1,\n  \"type\": \"\",\n  \"date\": \"\",\n  \"start_time\": \"\",\n  \"end_time\": \"\",\n  \"duration\": 1,\n  \"court_id\": 1,\n  \"price\": 0,\n  \"player_ids\": [],\n  \"buddy_request\": 0,\n  \"buddy_details\": null,\n  \"payment_status\": 0,\n  \"payment_intent\": null\n}"}, "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/reservations", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "reservations"]}}}, {"name": "Get Reservation Availability", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/reservations/availability/:court_id", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "reservations", "availability", ":court_id"]}}}, {"name": "Create Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100\n}"}, "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/reservations/payment-intent/create", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "reservations", "payment-intent", "create"]}}}, {"name": "Verify Payment Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"\",\n  \"booking_id\": 1,\n  \"payment_intent\": null\n}"}, "url": {"raw": "{{base_url}}/v3/api/custom/courtmatchup/user/reservations/payment/verify", "host": ["{{base_url}}"], "path": ["v3", "api", "custom", "courtmatchup", "user", "reservations", "payment", "verify"]}}}]}