const express = require('express');
const router = express.Router();
const CourtMatchupBotService = require('../services/botService');
const TokenMiddleware = require('../../../middleware/TokenMiddleware');
const ProjectMiddleware = require('../../../middleware/ProjectMiddleware');
const UrlMiddleware = require('../../../middleware/UrlMiddleware');
const HostMiddleware = require('../../../middleware/HostMiddleware');
const { sqlDateFormat, sqlDateTimeFormat } = require('../../../services/UtilService');


const middlewares = [
    ProjectMiddleware,
    UrlMiddleware,
    HostMiddleware,
    TokenMiddleware({}),
  ];
/**
 * @route POST /api/user/bot/chat
 * @description Start or continue a chat session with the bot
 * @access Private (if user_id is provided)
 */
const chat = async (req, res) => {
    try {
        const { club_id, chat_id, message } = req.body;
        const user_id = req.user_id || null;
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        if (!club_id) {
            return res.status(400).json({ error: 'club_id is required' });
        }

        if (!message) {
            return res.status(400).json({ error: 'message is required' });
        }

        // Get or create chat session
        const chat = await CourtMatchupBotService.getOrCreateChat(club_id, user_id, chat_id, sdk);

        // Process the message and get response
        const response = await CourtMatchupBotService.processMessage(chat.id, message, club_id, sdk);

        res.json({
            chat_id: chat.id,
            response,
            user_id: chat.user_id,
            club_id: chat.club_id
        });
    } catch (error) {
        console.error('Error in bot chat:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

/**
 * @route GET /api/user/bot/chat/:chat_id
 * @description Get chat history
 * @access Private (if chat is associated with a user)
 */
const getChatHistory = async (req, res) => {
    try {
        const { chat_id } = req.query;
        const { club_id } = req.query;
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        const user_id = req.user_id || null;

        const chat = await sdk.rawQuery(
            `SELECT * FROM courtmatchup_bot WHERE (user_id = ${user_id})  ${chat_id ? `AND id = ${chat_id}` : ''} ${club_id ? `AND club_id = ${club_id}` : ''}`
        );

        if (!chat.length) {
            return res.status(404).json({ error: 'Chat not found' });
        }

        let history = [];
        for (const item of chat) {
            if (item.history) {
                try {
                    history.push({...item, history: JSON.parse(item.history)})
                } catch (e) {
                    console.error('Error parsing chat history:', e);
                }
            }
        }

        res.json({
            user_id: chat[0].user_id,
            club_id: chat[0].club_id,
            history
        });
    } catch (error) {
        console.error('Error getting chat history:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

/**
 * @route POST /api/user/bot/ping
 * @description Update user's online timestamp across all rooms
 * @access Private
 */
const ping = async (req, res) => {
    try {
        const user_id = req.user_id;
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        sdk.setTable("room");

        // Get all rooms where the user is either user_id or other_user_id
        // const rooms = await sdk.get({
        //     $or: [
        //         { user_id: user_id },
        //         { other_user_id: user_id }
        //     ]
        // });
        const rooms = await sdk.rawQuery(`SELECT * FROM courtmatchup_room WHERE (user_id = ${user_id} OR other_user_id = ${user_id})`);

        if (typeof rooms == "string") {
            return res.status(403).json({
                error: true,
                message: rooms
            });
        }

        if (rooms.length == 0) {
            return res.status(200).json({
                error: false,
                message: "No rooms found for user",
                timestamp: new Date().toISOString()
            });
        }

        // Update timestamps in all rooms for this user
        const currentTime = sqlDateTimeFormat(new Date());
        let updatedCount = 0;

        for (const room of rooms) {
            const updateData = {};
            
            if (room.user_id == user_id) {
                updateData.user_update_at = currentTime;
            } else if (room.other_user_id == user_id) {
                updateData.other_user_update_at = currentTime;
            }

            if (Object.keys(updateData).length > 0) {
                const result = await sdk.update(updateData, room.id);
                if (typeof result !== "string") {
                    updatedCount++;
                }
            }
        }

        return res.status(200).json({
            error: false,
            message: "Ping successful",
            rooms_updated: updatedCount,
            total_rooms: rooms.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error in ping:', error);
        res.status(500).json({
            error: true,
            message: error.message
        });
    }
};

module.exports = (app) => {
    app.post('/api/v3/custom/courtmatchup/user/bot/chat', middlewares, chat);
    app.get('/api/v3/custom/courtmatchup/user/bot/chat', middlewares, getChatHistory);
    app.post('/api/v3/custom/courtmatchup/user/bot/ping', middlewares, ping);

    app.post("/v3/api/custom/courtmatchup/user/realtime/room", middlewares, async function (req, res) {
        try {
          let sdk = req.sdk;
          sdk.getDatabase();
          sdk.setProjectId(req.projectId);
          sdk.setTable("room");
    
          if (!req.body.user_id) {
            return res.status(403).json({
              error: true,
              message: "User is required",
              validation: [{ field: "user_id", message: "User is required" }]
            });
          }
    
          if (!req.body.other_user_id) {
            return res.status(403).json({
              error: true,
              message: "User is required",
              validation: [{ field: "other_user_id", message: "User is required" }]
            });
          }
    
          let isExist = await sdk.get({ user_id: req.body.user_id, other_user_id: req.body.other_user_id, resolved: 0 });
    
          if (isExist.length == 0) {
            isExist = await sdk.get({ user_id: req.body.other_user_id, other_user_id: req.body.user_id, resolved:0 });
          }
    
          if (isExist.length != 0) {
            return res.status(200).json({
              error: false,
              room_id: isExist[0].id,
              chat_id: isExist.chat_id
            });
          }
    
          const result = await sdk.insert({
            user_id: req.body.user_id,
            other_user_id: req.body.other_user_id,
            chat_id: -1,
            unread: 0,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date()),
            user_update_at: sqlDateTimeFormat(new Date()),
            other_user_update_at: sqlDateTimeFormat(new Date())
          });
    
          if (typeof result == "string") {
            return res.status(403).json({
              error: true,
              message: result
            });
          }
    
          return res.status(200).json({
            error: false,
            room_id: result,
            chat_id: -1
          });
        } catch (err) {
          res.status(403);
          res.json({
            error: true,
            message: err.message
          });
        }
      });
}; 