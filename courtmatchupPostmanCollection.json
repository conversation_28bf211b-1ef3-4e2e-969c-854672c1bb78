{"info": {"name": " courtmatchup Collection", "description": "courtmatchup collection BAAS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_variable_scope": "environment", "_postman_exported_at": "2024-10-28T11:46:44.412Z", "_postman_exported_using": "BAAS"}, "item": [{"name": "Clubs", "request": {"method": "GET", "header": [{"key": "x-project", "value": "Y291cnRtYXRjaHVwOmczbmp1OTlpZjR3enk4Z3V0bHEwYmUwZDI1Nzk1MTNsbTg=", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/courtmatchup/clubs", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/courtmatchup/clubs"]}, "body": {}, "description": ""}, "response": []}], "variable": [{"key": "baseurl", "value": "https://courtmatchup.mkdlabs.com", "type": "string"}]}