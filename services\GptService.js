const config = require('../utils/config')
const OpenAI = require('openai');
module.exports = {
    /**
     * Get chat response using OpenAI API.
     * @param {Array} messages - Array of messages exchanged in the conversation.
     * @param {Object} customConfig - Custom configuration object (optional).
     * @param {string} customConfig.apiKey - Custom API key (optional).
     * @param {string} customConfig.model - Custom model name (optional).
     * @returns {Promise<string>} - Resolves to the response generated by OpenAI.
     */
    async getChatResponse(messages, customConfig = {temperature: 0.5, top_p: 1}) {
        const apiKey = config.openai_api_key
        
        const openai = new OpenAI({
            apiKey: customConfig.apiKey || apiKey
        });

        const chatCompletion = await openai.chat.completions.create({
            model: customConfig.model ?? config.openai_model ?? "gpt-3.5-turbo",
            messages: messages,
            temperature: customConfig.temperature ?? 0.5,
            top_p: customConfig.top_p ?? 1
        });    

        return chatCompletion.choices[0].message;
    },
    
    /**
     * Get image response using OpenAI API.
     * @param {string} prompt - Prompt for the image generation.
     * @param {Object} customConfig - Custom configuration object (optional).
     * @param {string} customConfig.apiKey - Custom API key (optional).
     * @returns {Promise<string>} - Resolves to the URL of the generated image.
     */
    async createImagePrompt(prompt, customConfig = {}) {
        const apiKey = config.openai_api_key
        const openai = new OpenAI({
            apiKey: customConfig.apiKey || apiKey
        });

        const imageGenerationResponse = await openai.images.generate({
            prompt,
            n: 1,
            size: "1024x1024"
        });

        return imageGenerationResponse.data;
    }
}