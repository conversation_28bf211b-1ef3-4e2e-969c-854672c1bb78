const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

let logService = new DevLogService();

module.exports = function (app) {
  app.post("/v3/api/custom/register/customer", middlewares, async function (req, res) {
    try {
      // let verify = req.body.verify ? req.body.verify : false;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;
      if (!req.body.email) {
        return res.status(403).json({
          error: true,
          message: "Email Missing",
          validation: [{ field: "email", message: "Email missing" }]
        });
      }
      if (!req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Role Missing",
          validation: [{ field: "role", message: "role missing" }]
        });
      }
      verify = true;
      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password Missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }
      const sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      sdk.setTable("user");
      const user = await sdk.get({
        email: req.body.email
      });
      if (user.length > 0) {
        if (user[0].lng == "fr") {
          return res.status(403).json({
            error: true,
            message: "le compte existe déjà",
            complete: user[0].complete === 1 ? true : false
          });
        } else {
          return res.status(403).json({
            error: true,
            message: "User exists",
            complete: user[0].complete === 1 ? true : false
          });
        }
      }

      let service = new AuthService();

      logService.log(req.projectId, req.body.email, req.body.password, req.body.role);

      const result = await service.register(
        sdk,
        req.projectId,
        req.body.email,
        req.body.password,
        req.body.role,
        verify,
        req.body.first_name,
        req.body.last_name,
        req.body.photo
      );

      sdk.setTable("professional_profile");
      await sdk.insert({
        user_id: result,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
      sdk.setTable("customer_profile");
      await sdk.insert({
        user_id: result,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      if (typeof result == "string") {
        sdk.setTable("user");
        const user = await sdk.get({
          email: req.body.email
        });

        return res.status(403).json({
          error: true,
          message: result,
          complete: user[0].complete === 1 ? true : false
        });
      } else {
        if (needRefreshToken) {
          refreshToken = JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role
            },
            config.refresh_jwt_expire,
            config.jwt_key
          );
          let expireDate = new Date();
          expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
          await service.saveRefreshToken(req.sdk, req.projectId, result, refreshToken, expireDate);
        }

        return res.status(200).json({
          error: false,
          role: req.body.role,
          token: JwtService.createAccessToken(
            {
              user_id: result,
              role: req.body.role
            },
            config.jwt_expire,
            config.jwt_key
          ),
          complete: false,
          refresh_token: refreshToken,
          expire_at: config.jwt_expire,
          user_id: result
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  return [
    {
      method: "POST",
      name: "Register API",
      url: "/v2/api/lambda/register",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
        {
          name: "403",
          body: '{"role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Role Missing","validation": [{ "field": "role", "message": "Role missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    }
  ];
};
