const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const ValidationService = require("../../../services/ValidationService");
const RateLimitMiddleware = require("../../../middleware/RateLimitMiddleware");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  RateLimitMiddleware,
];

let logService = new DevLogService();

module.exports = function (app) {
  app.post("/v3/api/cedric/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password } = req.body;
      let subscribed = false;
      let free_trial_ended = false;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          // role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          // role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);
      // if(role === "professional"){
      //   //check for existing subscription using customer id
      //   //check if created_at < 24h for free trial end period
      // }
      logService.log(req.projectId, email, password);
      let result = []
      result = await service.login(req.sdk, req.projectId, email, password, "customer");

      if (typeof result == "string") {
        result = await service.login(req.sdk, req.projectId, email, password, "professional");
        if (typeof result == "string") {
          return res.status(403).json({
            error: true,
            message: result
          });
        }
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is inactive"
        });
      }
      if (result.status == 2) {
        return res.status(403).json({
          error: true,
          message: "Your account is Suspend"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            role: result.role
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role: result.role
          },
          config.jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: result.id,
        role: result.role,
        complete_c: result.complete_c,
        complete_p: result.complete_p,
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/cedric/login", middlewares, async function (req, res) {
    try {
      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;

      const { email, password, role } = req.body;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);

      logService.log(req.projectId, email, password);
      const result = await service.login(req.sdk, req.projectId, email, password, role);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      //TODO: Use the secret from project
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: result.id,
            role: role
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
        await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
      }
      return res.status(200).json({
        error: false,
        role,
        token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role
          },
          config.jwt_expire,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.jwt_expire,
        user_id: result.id,
        first_name: result.first_name ?? "",
        last_name: result.last_name ?? "",
        complete: result.complete === 0 ? false : true,
        photo: result.photo ?? "",
        two_factor_enabled: result.two_factor_authentication === 1 ? true : false
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/courtmatchup/staff/login", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      
      const result = await sdk.get({
        email: req.body.email,
        role: "staff"
      });
      
      if (result.length !== 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      
      const user = result[0];
      const hashPassword = user.password;
      
      // Verify password
      const passwordValid = await PasswordService.compareHash(req.body.password, hashPassword);
      if (!passwordValid) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }
      
      // Get staff profile
      sdk.setTable("staff");
      const staffProfile = await sdk.get({
        user_id: user.id
      });
      
      if (!staffProfile.length) {
        return res.status(401).json({
          error: true,
          message: "Staff profile not found",
        });
      }
      
      // Generate token
      const token = JwtService.getToken(
        {
          user_id: user.id,
          role: user.role,
          main_role: user.main_role || user.role,
        },
        config.jwt_expire
      );
      
      // Get permissions
      let permissions = {};
      if (staffProfile[0].permission_level) {
        try {
          permissions = JSON.parse(staffProfile[0].permission_level);
        } catch (e) {
          console.error("Error parsing permission_level:", e);
        }
      } else {
        // Check club permissions
        sdk.setTable("club_permissions");
        const clubPermissions = await sdk.get({
          club_id: staffProfile[0].club_id,
          role: "staff"
        });
        
        if (clubPermissions.length > 0 && clubPermissions[0].permission) {
          try {
            const permData = JSON.parse(clubPermissions[0].permission);
            permissions = permData.staff || {};
          } catch (e) {
            console.error("Error parsing club permissions:", e);
          }
        }
      }
      
      // Update last login
      sdk.setTable("user");
      await sdk.update(
        {
          last_login: sqlDateTimeFormat(new Date()),
        },
        user.id
      );
      
      // Return successful response with token
      return res.json({
        error: false,
        role: user.role,
        main_role: user.main_role || user.role,
        token,
        expire_at: config.jwt_expire,
        user_id: user.id,
        permissions,
        club_id: staffProfile[0].club_id
      });
      
    } catch (err) {
      DevLogService.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Staff Login API",
      url: "/v3/api/custom/courtmatchup/staff/login",
      successBody: '{ "email": "<EMAIL>", "password": "password123" }',
      successPayload: '{"error":false,"role":"staff","token":"JWT Token","expire_at":3600,"user_id":123,"permissions":{}}',
      errors: [
        {
          status: 401,
          message: "Invalid Credentials"
        },
        {
          status: 403,
          message: "Forbidden"
        }
      ]
    }
  ];
};
