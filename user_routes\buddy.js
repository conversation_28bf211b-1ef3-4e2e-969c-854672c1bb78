const AuthService = require("../../../services/AuthService.js");
const JwtService = require("../../../services/JwtService.js");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware.js");
const UrlMiddleware = require("../../../middleware/UrlMiddleware.js");
const HostMiddleware = require("../../../middleware/HostMiddleware.js");
const DevLogService = require("../../../services/DevLogService.js");
const config = require("../../../config.js");
const TokenMiddleware = require("../../../middleware/TokenMiddleware.js");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService.js");
const { reservation_hours_left, log_reservation, build_coach_availability_query } = require("../utils/util.js");
const BookingService = require("../services/bookingService.js");
const { ERROR_MESSAGES } = require("../utils/constants.js");
const ValidationService = require("../../../services/ValidationService.js");
const MailService = require("../../../services/MailService.js");
const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "user" }),
];

const base = "/v3/api/custom/courtmatchup/user/buddy";
const _base = "/v3/api/custom/courtmatchup/user";

module.exports = function (app) {
  // View my requests
  app.get(base + "/my-requests", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const buddy_id = req.query.buddy_id;

      const buddy_requests = await sdk.join("buddy_request", "buddy", "buddy_id", "id", "*", { "buddy.user_id": req.user_id });
      // const requests_sent = await sdk.join("buddy_request", "buddy", "buddy_id", "id", "*,buddy_request.id as request_id ", { "buddy_request.user_id": req.user_id });
      const {
        date,
        num_needed,
        ntrp,
        max_ntrp,
        week
      } = req.query;

      let where = [`cb.user_id = ${req.user_id}`];
      if(buddy_id){
        where.push(`cb.id = ${buddy_id}`);
      }

      if (date) {
        const d = new Date(date);
        where.push(`cb.date = '${sqlDateFormat(d)}'`);
      }
      // else{
      //   where.push(`cb.date >= CURDATE()`);
      // }

      if (num_needed) {
        where.push(`cb.num_needed = ${num_needed}`);
      }

      if (ntrp) {
        where.push(`(cb.ntrp = ${ntrp})`);
      }

      if (max_ntrp) {
        where.push(`(cb.max_ntrp = ${max_ntrp})`);
      }

      if (week) {
        if(week.includes("-")){
          const weekOffset = parseInt(week, 10); // convert week to number
          where.push(`WEEK(COALESCE(cb.date, CURDATE())) = WEEK(CURDATE()) ${weekOffset}`);
        }else{
          const weekOffset = parseInt(week, 10); // convert week to number
          where.push(`WEEK(COALESCE(cb.date, CURDATE())) = WEEK(CURDATE()) + ${weekOffset}`);
        }
      }
      

      if (where.length > 0) {
        where = `WHERE ${where.join(" AND ")}`;
      }

      const requests = await sdk.rawQuery(`
              SELECT 
                  cb.id AS buddy_id,
                  cb.sport_id,
                  cb.type,
                  cb.sub_type,
                  cs.name AS sport_name,
                  cb.surface_id,
                  csr.name AS surface_name,
                  cb.num_players,
                  cb.num_needed,
                  cb.ntrp,
                  cb.max_ntrp,
                  cb.slots,
                  cb.need_coach,
                  cb.notes,
                  cb.player_ids,
                  cb.date,
                  cb.start_time,
                  cb.end_time,
                  cb.create_at,
                  cu_main.first_name AS owner_first_name,
                  cu_main.last_name AS owner_last_name,
                  cu_main.email AS owner_email,
                  cu_main.photo AS owner_photo,
                  -- Deduplicated player details
                  (SELECT 
                      GROUP_CONCAT(
                          DISTINCT CONCAT(
                              p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                          ) SEPARATOR ';;'
                      )
                  FROM 
                      courtmatchup_user p
                  LEFT JOIN 
                      courtmatchup_profile prof ON prof.user_id = p.id
                  WHERE 
                      FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
                  ) AS player_details,
                  -- Requests to join
                  GROUP_CONCAT(
                      DISTINCT CONCAT(
                          br.id, '=', br.ntrp, '=', br.num_players, '=', br.status, '=', user.first_name, '=', 
                          user.last_name, '=', user.email, '=', user.photo, '=', br.create_at
                      ) SEPARATOR ';;'
                  ) AS requests_to_join
              FROM 
                  courtmatchup_buddy cb
              LEFT JOIN 
                  courtmatchup_user cu_main ON cb.user_id = cu_main.id
              LEFT JOIN
                  courtmatchup_sports cs ON cb.sport_id = cs.id
              LEFT JOIN
                  courtmatchup_surface csr ON cb.surface_id = csr.id
              LEFT JOIN 
                  courtmatchup_buddy_request br ON br.buddy_id = cb.id
              LEFT JOIN
                  courtmatchup_user user ON br.user_id = user.id
               
                ${where}
              GROUP BY 
                  cb.id, cu_main.id;
        
              `)
      const requests_sent = await sdk.rawQuery(`
       SELECT 
        br.id AS request_id,
        cb.id AS buddy_id,
        cb.sport_id,
        cb.type,
        cb.sub_type,
        cs.name AS sport_name,
        cb.surface_id,
        csr.name AS surface_name,
        cb.num_players,
        cb.num_needed,
        cb.ntrp,
        cb.max_ntrp,
        cb.slots,
        cb.need_coach,
        cb.notes,
        cb.player_ids,
        cb.date,
        cb.start_time,
        cb.end_time,
        cb.create_at,
        cu_main.first_name AS owner_first_name,
        cu_main.last_name AS owner_last_name,
        cu_main.email AS owner_email,
        cu_main.photo AS owner_photo,
        GROUP_CONCAT(CONCAT(cu_player.first_name, '=', cu_player.last_name, '=', cu_player.email) SEPARATOR ';;') AS player_details
    FROM 
        courtmatchup_buddy cb
    LEFT JOIN 
        courtmatchup_user cu_main ON cb.user_id = cu_main.id
    LEFT JOIN
        courtmatchup_sports cs ON cb.sport_id = cs.id
    LEFT JOIN
        courtmatchup_surface csr ON cb.surface_id = csr.id
    LEFT JOIN 
        courtmatchup_user cu_player ON FIND_IN_SET(cu_player.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
    LEFT JOIN 
        courtmatchup_buddy_request br ON br.buddy_id = cb.id
    LEFT JOIN
        courtmatchup_user user ON br.user_id = user.id
    WHERE br.user_id = ${req.user_id}
    GROUP BY 
        cb.id, cu_main.id;
        `)

      requests.forEach(request => {
        const player_details = request?.player_details?.split(";;");
        const requests_to_join = request?.requests_to_join?.split(";;");

        request.player_details = player_details ? player_details?.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=");
          return { first_name, last_name, email, ntrp, photo };
        }) : [];

        request.requests_to_join = requests_to_join ?requests_to_join?.map(request_to_join => {
          const [id, ntrp, num_players, status, first_name, last_name, email, photo,create_at] = request_to_join?.split("=");
          return { request_id:id, ntrp, num_players, request_status:status, first_name, last_name, email, photo, create_at };
        }) : [];
      })
      requests_sent.forEach(request => {
        const player_details = request?.player_details?.split(";;");
        request.player_details = player_details ? player_details?.map(player => {
          const [first_name, last_name, email] = player?.split("=");
          return { first_name, last_name, email };
        }) : [];
      })

      
      return res.status(200).json({
        error: false,
        my_requests:requests,
        requests_to_join:buddy_requests,
        requests_sent
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/all-requests", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {
        date,
        num_needed,
        ntrp,
        max_ntrp,
        week
      } = req.query;

      let where = [];

      if (date) {
        const d = new Date(date);
        where.push(`cb.date = '${sqlDateFormat(new Date(d))}'`);
      }

      if (num_needed) {
        where.push(`cb.num_needed = ${num_needed}`);
      }

      if (ntrp) {
        where.push(`(cb.ntrp >= ${ntrp} OR cb.max_ntrp = ${ntrp})`);
      }

      if (max_ntrp) {
        where.push(`(cb.max_ntrp = ${max_ntrp})`);
      }

      if (week) {
        if(week.includes("-")){
          const weekOffset = parseInt(week, 10); // convert week to number
          where.push(`WEEK(COALESCE(cb.date, CURDATE())) = WEEK(CURDATE()) ${weekOffset}`);
        }else{
          const weekOffset = parseInt(week, 10); // convert week to number
          where.push(`WEEK(COALESCE(cb.date, CURDATE())) = WEEK(CURDATE()) + ${weekOffset}`);
        }
      }
      

      if (where.length > 0) {
        where = `WHERE ${where.join(" AND ")}`;
      }


      const requests = await sdk.rawQuery(`
          SELECT 
              cb.id AS buddy_id,
              br.id AS request_id,
              cb.sport_id,
              cb.type,
              cb.sub_type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.num_players,
              cb.num_needed,
              cb.ntrp,
              cb.max_ntrp,
              cb.slots,
              cb.need_coach,
              cb.notes,
              cb.player_ids,
              cb.date,
              cb.start_time,
              cb.end_time,
              cb.create_at,
              cu_main.id AS owner_user_id,
              cu_main.first_name AS owner_first_name,
              cu_main.last_name AS owner_last_name,
              cu_main.email AS owner_email,
              cu_main.photo AS owner_photo,
                  (SELECT 
                      GROUP_CONCAT(
                          DISTINCT CONCAT(
                              p.first_name, '=', p.last_name, '=', p.email, '=', prof.ntrp, '=', p.photo
                          ) SEPARATOR ';;'
                      )
                  FROM 
                      courtmatchup_user p
                  LEFT JOIN 
                      courtmatchup_profile prof ON prof.user_id = p.id
                  WHERE 
                      FIND_IN_SET(p.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
                  ) AS player_details,
              CASE 
                  WHEN EXISTS (
                      SELECT 1 
                      FROM courtmatchup_buddy_request br_sent 
                      WHERE br_sent.user_id = ${req.user_id} AND br_sent.buddy_id = cb.id
                  ) THEN 1
                  ELSE 0
              END AS sent
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          LEFT JOIN 
              courtmatchup_user cu_player ON FIND_IN_SET(cu_player.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
          LEFT JOIN 
              courtmatchup_profile cu_profile ON FIND_IN_SET(cu_profile.user_id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
          LEFT JOIN 
              courtmatchup_buddy_request br ON br.buddy_id = cb.id
          LEFT JOIN
              courtmatchup_user user ON br.user_id = user.id
              ${where}
          GROUP BY 
              cb.id, cu_main.id;

        `)

      requests.forEach(request => {
        const player_details = request.player_details?.split(";;") || [];
        request.sent = request?.sent === 1 ? true : false;
        request.player_details = player_details.map(player => {
          const [first_name, last_name, email, ntrp, photo] = player?.split("=") || {};
          return { first_name, last_name, email, ntrp, photo };
        });
      })
      
      return res.status(200).json({
        error: false,
        list: requests
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/:buddy_id/send-mail", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { buddy_id } = req.params;
      const {email=""} = req.query;
      sdk.setTable("buddy");
      const buddy = await sdk.rawQuery(`
              SELECT 
              cb.id AS buddy_id,
              cb.sport_id,
              cb.type,
              cs.name AS sport_name,
              cb.surface_id,
              csr.name AS surface_name,
              cb.date,
              cb.start_time,
              cb.end_time,
              cu_main.email AS owner_email,
              GROUP_CONCAT(CONCAT(cu_player.first_name, '=', cu_player.last_name, '=', cu_player.email) SEPARATOR ';;') AS player_details
          FROM 
              courtmatchup_buddy cb
          LEFT JOIN 
              courtmatchup_user cu_main ON cb.user_id = cu_main.id
          LEFT JOIN
              courtmatchup_sports cs ON cb.sport_id = cs.id
          LEFT JOIN
              courtmatchup_surface csr ON cb.surface_id = csr.id
          LEFT JOIN 
              courtmatchup_user cu_player ON FIND_IN_SET(cu_player.id, REPLACE(REPLACE(cb.player_ids, '[', ''), ']', ''))
          LEFT JOIN
              courtmatchup_user user ON cb.user_id = user.id
          WHERE 
              cb.id = ${buddy_id}
          AND 
              cb.user_id = ${req.user_id}
          GROUP BY 
              cb.id, cu_main.id;`)
      if (!buddy.length) {
        return res.status(403).json({
          error: true,
          message: "Buddy not found",
        });
      }

      const player_details = buddy[0].player_details?.split(";;") || [];
      buddy[0].player_details = player_details.map(player => {
        const [first_name, last_name, email] = player?.split("=") || {};
        return { first_name, last_name, email };
      });
      // construct email from date start_time, end_time, sport_name
      const date = new Date(buddy[0].date);
      const day = date.getDate().toString().padStart(2, "0");
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const year = date.getFullYear();
      // date in full eg 19th may 2025
      const formattedDate =  `${day}th ${date.toLocaleString("en-US", { month: "long" })} ${year}`;
      const formattedStartTime = buddy[0].start_time;
      const formattedEndTime = buddy[0].end_time;
      const formattedSport = buddy[0].sport_name;
      const formattedSurface = buddy[0].surface_name;
      const formattedPlayers = buddy[0].player_details.map(player => `${player.first_name} ${player.last_name}`).join(",\n ");
      const emailsList = buddy[0].player_details.map(player => player.email);
      const formattedEmails = buddy[0].player_details.map(player => player.email).join(", ");
      const formattedSubject = `Email reminder`;
      const formattedBody = `Buddy Request: ${formattedDate} ${formattedStartTime} - ${formattedEndTime} ${formattedSport} ${formattedSurface} with \n${formattedPlayers}`;
      

      MailService.initialize(config);
      // const result = await MailService.send(req.body.from, req.body.to, req.body.subject, req.body.body);
      console.log(formattedSubject, formattedBody);
      if (email) {
        console.log(email);
        const result = await MailService.send(config.from_mail, email, formattedSubject, formattedBody);
        console.log(result);
        if (result.error ){
          throw new Error(result.message);
        }
      }else{
        for (let i = 0; i < emailsList.length; i++) {
          console.log(emailsList[i]);
          const result = await MailService.send(config.from_mail, emailsList[i], formattedSubject, formattedBody);
          console.log(result);
        }
      }

      return res.status(200).json({
        error: false,
        message: "Email reminders complete",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
  // join request
  app.post(base + "/join-team", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { buddy_id, ntrp, player_ids=[] } = req.body;

      // if (!ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);

      if (!buddy_id) throw new Error(ERROR_MESSAGES.BUDDY_ID_NOT_FOUND);

      const num_players = player_ids.length;

      sdk.setTable("buddy");
      const buddy = (await sdk.get({ id: buddy_id }))[0];
      if (!buddy) throw new Error(ERROR_MESSAGES.BUDDY_NOT_FOUND);
      const {num_needed, ntrp: buddy_ntrp} = buddy;

      if (num_players > num_needed) throw new Error(ERROR_MESSAGES.NUM_PLAYERS_EXCEEDED);

      // if (player_ids.length < num_players) throw new Error(ERROR_MESSAGES.PLAYER_IDS_NOT_FOUND);

      // check if player has joined before
      const existing_ids = JSON.parse(buddy.player_ids);
      sdk.setTable("buddy_request");
      for (let i = 0; i < player_ids.length; i++) {
        const player_id = player_ids[i];
        const player = (await sdk.get({ user_id: player_id, buddy_id }))[0];
        if (!player) continue;
        if (player.buddy_id) throw new Error(ERROR_MESSAGES.PLAYER_ALREADY_JOINED);
      }


      sdk.setTable("buddy_request");
      await sdk.insert({
        user_id: req.user_id,
        other_ids: JSON.stringify(player_ids),
        buddy_id: buddy_id,
        ntrp,
        num_players: num_players,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });
      
      return res.status(200).json({
        error: false,
        message: "Request sent successfully"
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // accept or reject request
  app.post(base + "/update-request", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { request_id, status } = req.body;

      if (!request_id) throw new Error(ERROR_MESSAGES.REQUEST_ID_NOT_FOUND);

      if (!status) throw new Error(ERROR_MESSAGES.STATUS_NOT_FOUND);
      let request = null;

      sdk.setTable("buddy_request");
      if (status === 3){
        const request = (await sdk.get({ id: request_id, status:0, user_id: req.user_id }))[0];
        if (!request) throw new Error(ERROR_MESSAGES.REQUEST_NOT_FOUND);

        await sdk.update({
          status,
          update_at: sqlDateTimeFormat(new Date()),
        }, request_id);
        return res.status(200).json({
          error: false,
          message: "Request cancelled successfully"
        })
      }else if (status === 4){
        // undo accept
        const request = (await sdk.get({ id: request_id }))[0];
        if (!request) throw new Error(ERROR_MESSAGES.REQUEST_NOT_FOUND);
        if (request.status === 1){
          await sdk.update({
            status: 0,
            update_at: sqlDateTimeFormat(new Date()),
          }, request_id);
        }
        sdk.setTable("buddy");
        const buddy = (await sdk.get({ id: request.buddy_id }))[0];
        if (!buddy) throw new Error(ERROR_MESSAGES.BUDDY_NOT_FOUND);
        const new_ids = JSON.parse(request.other_ids) || [];
        console.log(new_ids);
        const {num_needed, ntrp: buddy_ntrp,id,num_players} = buddy;
  
        const reservation_id = buddy.reservation_id;
        let old_ids = JSON.parse(buddy.player_ids) || [];
        // remove the ones in new_ids from old_ids

        old_ids = old_ids.filter(id => !new_ids.includes(id));

        if (reservation_id) {
          sdk.setTable("reservation");
          const reservation = (await sdk.get({ id: reservation_id }))[0];
          if (!reservation) throw new Error(ERROR_MESSAGES.RESERVATION_NOT_FOUND);
          const booking_id = reservation.booking_id;
          if (!booking_id) throw new Error(ERROR_MESSAGES.BOOKING_ID_NOT_FOUND);
          sdk.setTable("booking");
          const booking = (await sdk.get({ id: booking_id }))[0];
          if (!booking) throw new Error(ERROR_MESSAGES.BOOKING_NOT_FOUND);
          const old_booking_ids = JSON.parse(booking.player_ids) || [];
          const new_booking_ids = old_ids 
          await sdk.updateWhere({
            player_ids: JSON.stringify(new_booking_ids),
            update_at: sqlDateTimeFormat(new Date()),
          }, { id: booking_id });
  
          for (let i = 0; i < new_ids.length; i++) {
            const player_id = new_ids[i];
            sdk.setTable("reservation_team")
            await sdk.deleteWhere({ booking_id, user_id: player_id });
          }
        }
  
        sdk.setTable("buddy");
        await sdk.updateWhere({
          num_needed: num_needed + new_ids.length,
          num_players: num_players - new_ids.length,
          // ntrp: buddy_ntrp,
          update_at: sqlDateTimeFormat(new Date()),
        }, {
          id
        })
        return res.status(200).json({
          error: false,
          message: "Request undo was successful"
        })
      }else{
        request = (await sdk.get({ id: request_id, status:0 }))[0];
      }
      if (!request) throw new Error(ERROR_MESSAGES.REQUEST_NOT_FOUND);
      const new_ids = JSON.parse(request.other_ids) || [];
      console.log(new_ids);

      if (status === 2) {
        await sdk.update({
          status,
          update_at: sqlDateTimeFormat(new Date()),
        }, request_id);
        return res.status(200).json({
          error: false,
          message: "Request rejected successfully"
        })
      }

      // update buddy group - check if reservation exists - update reservation team and booking ids

      sdk.setTable("buddy");
      const buddy = (await sdk.get({ id: request.buddy_id }))[0];
      const {num_needed, ntrp: buddy_ntrp,id,num_players} = buddy;
      if (new_ids.length > num_needed) throw new Error("Number of players exceeded");
      if (!buddy) throw new Error(ERROR_MESSAGES.BUDDY_NOT_FOUND);

      const reservation_id = buddy.reservation_id;
      const old_ids = JSON.parse(buddy.player_ids) || [];
      const player_ids = old_ids.concat(new_ids);
      if (reservation_id) {
        sdk.setTable("reservation");
        const reservation = (await sdk.get({ id: reservation_id }))[0];
        if (!reservation) throw new Error(ERROR_MESSAGES.RESERVATION_NOT_FOUND);
        const booking_id = reservation.booking_id;
        if (!booking_id) throw new Error(ERROR_MESSAGES.BOOKING_ID_NOT_FOUND);
        sdk.setTable("booking");
        const booking = (await sdk.get({ id: booking_id }))[0];
        if (!booking) throw new Error(ERROR_MESSAGES.BOOKING_NOT_FOUND);
        const old_booking_ids = JSON.parse(booking.player_ids) || [];
        const new_booking_ids = old_booking_ids.concat(new_ids);;
        await sdk.updateWhere({
          player_ids: JSON.stringify(new_booking_ids),
          update_at: sqlDateTimeFormat(new Date()),
        }, { id: booking_id });

        for (let i = 0; i < new_ids.length; i++) {
          const player_id = new_ids[i];
          sdk.setTable("reservation_team")
          await sdk.insert({
            booking_id,
            reservation_id,
            user_id: player_id,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date()),
          });
        }
      }

      sdk.setTable("buddy");
      await sdk.updateWhere({
        num_needed: num_needed - new_ids.length,
        num_players: num_players + new_ids.length,
        // ntrp: buddy_ntrp,
        update_at: sqlDateTimeFormat(new Date()),
      }, {
        id
      })

      

      sdk.setTable("buddy_request");
      await sdk.updateWhere({
        status,
        update_at: sqlDateTimeFormat(new Date()),
      }, { id: request_id });

      
      return res.status(200).json({
        error: false,
        message: "Request accepted successfully"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // cancel request
  app.get(base + "/cancel-request/:request_id", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { request_id } = req.params;


      sdk.setTable("buddy_request");
      const request = (await sdk.get({ id: request_id, user_id: req.user_id, status:0 }))[0];
      if (!request) throw new Error(ERROR_MESSAGES.REQUEST_NOT_FOUND);
      
      sdk.setTable("buddy_request");
      await sdk.delete({}, request_id);

      
      return res.status(200).json({
        error: false,
        message: "Request cancelled successfully"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // Create request
  app.post(base + "/create-request",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, slots, ntrp, num_players, num_needed, type=0, need_coach = 0, notes = "", date, start_time, end_time, player_ids=[], max_ntrp } = req.body;

      // if (!ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!num_players) throw new Error(ERROR_MESSAGES.NUM_PLAYERS_NOT_FOUND);
      if (!num_needed) throw new Error(ERROR_MESSAGES.NUM_NEEDED_NOT_FOUND);
      // if (!type) throw new Error(ERROR_MESSAGES.TYPE_NOT_FOUND);
      if(!slots && !slots.length) {
        if (!start_time) throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND);
        if (!end_time) throw new Error(ERROR_MESSAGES.END_TIME_NOT_FOUND);
      }else if (slots && !slots.length) throw new Error(ERROR_MESSAGES.SLOTS_NOT_FOUND)
      else if(!start_time || !end_time) {
        throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND + " or end_time missing");
      }
      if (!date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);

      let total = 0;

      if (start_time && end_time){
        const [startHours, startMinutes] = start_time.split(':').map(Number);
        const [endHours, endMinutes] = end_time.split(':').map(Number);
        const duration = (endHours - startHours) * 60 + (endMinutes - startMinutes);
        const hour_duration = duration / 60;
        total += hour_duration;
      }
      // slots = [{
      //   start_time: "11:10",
      //   end_time: "13:20"
      // }]

      if (slots && slots.length) {
        for (let i = 0; i < slots.length; i++) {
          const slot = slots[i];
          const {start_time, end_time} = slot;
          const [startHours, startMinutes] = start_time.split(':').map(Number);
          const [endHours, endMinutes] = end_time.split(':').map(Number);
          const duration = (endHours - startHours) * 60 + (endMinutes - startMinutes);
          const hour_duration = duration / 60;
          total += hour_duration;
        }
      }

      // if (await reservation_hours_left(sdk, req.user_id) < total) throw new Error(ERROR_MESSAGES.WEEKLY_RESERVATION_LIMIT_REACHED);
      // await log_reservation(sdk, user_id, reservation_id);
      
      sdk.setTable("buddy");
      const buddy_id = await sdk.insert(filterEmptyFields({
        sport_id,
        type,
        ntrp,
        user_id:req.user_id,
        // reservation_id,
        num_players,
        max_ntrp,
        num_needed,
        slots: JSON.stringify(slots) || "[]",
        notes,
        need_coach,
        date: sqlDateFormat(new Date(date)),
        player_ids: JSON.stringify(player_ids),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      }));

      
      return res.status(200).json({
        error: false,
        message: "Request created successfully",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // Edit request
  app.post(base + "/edit-request/:buddy_id",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, min_ntrp, max_ntrp, slots, ntrp, num_players, num_needed, type=0, need_coach = 0, notes = "", date, start_time, end_time, player_ids=[] } = req.body;
      const {buddy_id:request_id} = req.params;

      // slots = [{
      //   start_time: "11:10",
      //   end_time: "13:20"
      // }]

      if (slots && slots.length) {
        for (let i = 0; i < slots.length; i++) {
          const slot = slots[i];
          const {start_time, end_time} = slot;
          const [startHours, startMinutes] = start_time.split(':').map(Number);
          const [endHours, endMinutes] = end_time.split(':').map(Number);
          const duration = (endHours - startHours) * 60 + (endMinutes - startMinutes);
          const hour_duration = duration / 60;
          // total += hour_duration;
        }
      }

      // if (await reservation_hours_left(sdk, req.user_id) < total) throw new Error(ERROR_MESSAGES.WEEKLY_RESERVATION_LIMIT_REACHED);
      // await log_reservation(sdk, user_id, reservation_id);
      
      sdk.setTable("buddy");
      await sdk.update(filterEmptyFields({
        ntrp,
        num_players,
        num_needed,
        slots: slots ? JSON.stringify(slots) : null,
        notes,
        need_coach,
        min_ntrp,
        max_ntrp,
        date: sqlDateFormat(new Date(date)),
        player_ids: player_ids ? JSON.stringify(player_ids) : null,
        start_time,
        end_time,
        update_at: sqlDateTimeFormat(new Date()),
      }), request_id);

      
      return res.status(200).json({
        error: false,
        message: "Request updated successfully",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // subscribe to date range
  app.post(base + "/subscribe",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, sub_type,surface_id, min_ntrp, max_ntrp, days ,type=0, need_coach = 0, notes = "", start_date, end_date, start_time, end_time } = req.body;

      if (!sport_id) throw new Error(ERROR_MESSAGES.SPORT_ID_NOT_FOUND);
      if (!start_date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);
      if (!end_date) throw new Error(ERROR_MESSAGES.DATE_NOT_FOUND);
      if (!min_ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!max_ntrp) throw new Error(ERROR_MESSAGES.NTRP_NOT_FOUND);
      if (!type) throw new Error(ERROR_MESSAGES.TYPE_NOT_FOUND);
      if (!start_time) throw new Error(ERROR_MESSAGES.START_TIME_NOT_FOUND);
      if (!end_time) throw new Error(ERROR_MESSAGES.END_TIME_NOT_FOUND);
      
      sdk.setTable("buddy_subscription");
      const subscription_id = await sdk.insert({
        sport_id,
        user_id: req.user_id,
        type,
        min_ntrp,
        surface_id,
        sub_type,
        max_ntrp,
        days: JSON.stringify(days),
        start_date: sqlDateFormat(new Date(start_date)),
        end_date: sqlDateFormat(new Date(end_date)),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      
      return res.status(200).json({
        error: false,
        message: "Subscribed to date range",
        subscription_id
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post(_base + "/subscribe/edit/:subscription_id",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const {sport_id, sub_type,min_ntrp,surface_id, max_ntrp, days ,type=0, need_coach = 0, notes = "", start_date, end_date, start_time, end_time } = req.body;

      
      sdk.setTable("buddy_subscription");
      const subscription_exist = (await sdk.get({id: req.params.subscription_id}))[0];
      if (!subscription_exist) throw new Error(ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND);
      const subscription_id = await sdk.update(filterEmptyFields({
        sport_id,
        user_id: req.user_id,
        type,
        min_ntrp,
        max_ntrp,
        surface_id,
        sub_type,
        days: days ? JSON.stringify(days) : null,
        start_date: start_date ? sqlDateFormat(new Date(start_date)) : null,
        end_date: end_date ? sqlDateFormat(new Date(end_date)) : null,
        start_time,
        end_time,
        update_at: sqlDateTimeFormat(new Date()),
      }), req.params.subscription_id,);

      
      return res.status(200).json({
        error: false,
        message: "Subscription updated successfully",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.get(_base + "/subscribe/delete/:subscription_id",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      sdk.setTable("buddy_subscription");
      const subscription_exist = (await sdk.get({id: req.params.subscription_id}))[0];
      if (!subscription_exist) throw new Error(ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND);
      const subscription_id = await sdk.delete({}, req.params.subscription_id);
      
      return res.status(200).json({
        error: false,
        message: "Subscription deleted successfully",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // get subscriptions
  app.get(base + "/get-subscriptions",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const {subscription_id} = req.query;
      sdk.setTable("buddy_subscription");
      // const subscriptions = await sdk.get(filterEmptyFields({user_id: req.user_id, id: subscription_id}));
      const subscriptions = await sdk.rawQuery(`
        SELECT courtmatchup_buddy_subscription.*, cs.name as sport_name, csr.name as surface_name FROM courtmatchup_buddy_subscription
        LEFT JOIN courtmatchup_sports cs ON cs.id = courtmatchup_buddy_subscription.sport_id
        LEFT JOIN courtmatchup_surface csr ON csr.id = courtmatchup_buddy_subscription.surface_id
        WHERE courtmatchup_buddy_subscription.user_id = ${req.user_id} ${subscription_id ? `AND courtmatchup_buddy_subscription.id = ${subscription_id}` : ""}
        Group by courtmatchup_buddy_subscription.id
        `)

      subscriptions.forEach(function (subscription) {
        // give the subscription an index eg 1,2 etc
        subscription.position = subscriptions.indexOf(subscription) + 1
      })
      
      return res.status(200).json({
        error: false,
        subscriptions
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get(base + "/view-subscriptions",middlewares,async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      
      sdk.setTable("buddy_subscription");
      const subscriptions = await sdk.get({
        user_id: req.user_id
      });

      
      return res.status(200).json({
        error: false,
        list: subscriptions
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });



  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true
    }
  ];
};
