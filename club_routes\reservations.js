const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const MailService = require("../services/MailService");
const config = require("../../../config");
const TokenMiddleware = require("../middlewares/TokenMiddleware");
const { filterEmptyFields, sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");
const stripe = new StripeService();
const { reservation_hours_left, log_reservation, formatTimeForMySQL } = require("../utils/util.js");
const { BOOKING_STATUSES, RESERVATION_TYPES } = require("../utils/constants.js");
const ValidationService = require("../../../services/ValidationService.js");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "club" }),
];

const adminMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "admin|admin_staff" }),
];
const combinedMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware({ role: "admin|club|staff|admin_staff" }),
];
const base = "/v3/api/custom/courtmatchup/club/reservations";
const _base = "/v3/api/custom/courtmatchup/reservations";

module.exports = function (app) {


  // Create reservation
  app.post(base, middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      let {
        sport_id,
        type,
        date,
        start_time,
        coaches = null,
        end_time,
        duration,
        sub_type:subtype,
        court_id,
        court_ids=[],
        coach_ids=[],
        coach_id=null,
        clinic_id=null,
        court_fee,
        recurring=0,
        custom_request=0,
        reservation_type=1,
        space_assigned=[],
        notes="",
        player_ids,
        buddy_request = 0,
        buddy_details = null,
        payment_status = 0,
        payment_details = null,
      } = req.body;

      // if (await reservation_hours_left(sdk, req.user_id) + duration > 20) {
      //   throw new Error("Reservation hours exceeded for the week");
      // }

      if (!sport_id || !type || !date || !start_time || !end_time || !player_ids) {
        throw new Error("All fields are required");
      }

      if (!duration) {
        // Calculate duration based on start and end time in hours
        const [startHours, startMinutes] = start_time.split(':').map(Number);
        const [endHours, endMinutes] = end_time.split(':').map(Number);
        
        // Convert start and end times to total minutes
        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;
      
        // Calculate the difference in minutes and convert to hours
        duration = (endTotalMinutes - startTotalMinutes) / 60;
      }
      

      if (buddy_request === 1 && !buddy_details) {
        throw new Error("Please enter buddy details");
      }

      if (reservation_type === 3 && custom_request === 0 && !coach_id) {
        throw new Error("Please select a coach");
      }

      if (reservation_type === 2 && !clinic_id) {
        throw new Error("Please select a clinic");
      }else if (reservation_type === 2 && clinic_id) {
        sdk.setTable("clinics");
        const clinic = (await sdk.get({ id: clinic_id }))[0];
        if (!clinic) {
          throw new Error("Invalid clinic ID");
        }
        const clinic_date = new Date(clinic.date);
        if (clinic.recurring == 0 && clinic_date < new Date()) {
          throw new Error("Invalid clinic date");
        }

        start_time = (clinic.start_time)
        end_time = (clinic.end_time)

      }


      if (court_id) {
        const checkSql = `
          SELECT 
            b.id,
            b.date,
            b.start_time,
            b.end_time,
            b.court_id
          FROM 
            courtmatchup_booking b
          WHERE 
            b.court_id = ${court_id}
            AND b.date = '${sqlDateFormat(new Date(date))}'
            AND (
              (b.start_time <= '${formatTimeForMySQL(start_time)}' AND b.end_time > '${formatTimeForMySQL(start_time)}')
              OR 
              (b.start_time < '${formatTimeForMySQL(end_time)}' AND b.end_time >= '${formatTimeForMySQL(end_time)}')
              OR 
              (b.start_time >= '${formatTimeForMySQL(start_time)}' AND b.end_time <= '${formatTimeForMySQL(end_time)}')
            )
        `;
        
        const existingEvents = await sdk.rawQuery(checkSql);
        
        if (existingEvents.length > 0) {
          throw new Error("Cannot create event on this court due to an event already existing at that date/time.");
        }
      }


      sdk.setTable("clubs")
      const club = (await sdk.get({ user_id: req.user_id }))[0];
      const club_id = club?.id;

      // Create booking
      sdk.setTable("booking");
      // random numbers of length 11 for receipt_id
      const receipt_id = `${Math.floor(Math.random() * 10000000000000000)}`.slice(-11);

      const booking_id = await sdk.insert(filterEmptyFields({
        user_id: req.user_id,
        sport_id,
        type,
        subtype,
        status: payment_status,
        clinic_id,
        coach_id,
        club_id,
        court_id,
        coaches: coaches ? JSON.stringify(coaches) : null,
        lesson_id: null,
        receipt_id,
        duration,
        reservation_type,
        custom_request,
        payment_intent: payment_details ? payment_details.payment_intent : null,
        court_fee,
        player_id: player_ids[0],
        player_ids: JSON.stringify(player_ids),
        court_ids: JSON.stringify(court_ids),
        coach_ids: JSON.stringify(coach_ids),
        date: sqlDateFormat(new Date(date)),
        start_time,
        end_time,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      }));

      // Create reservation
      sdk.setTable("reservation");
      const reservation_id = await sdk.insert({
        booking_id,
        buddy_id: null,
        user_id: req.user_id,
        club_id: club_id,
        recurring,
        space_assigned: JSON.stringify(space_assigned),
        notes,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      // Add players to reservation team
      sdk.setTable("reservation_team");
      for (let i = 0; i < player_ids.length; i++) {
        await sdk.insert({
          user_id: player_ids[i],
          reservation_id,
          booking_id,
          creator: req.user_id === player_ids[i] ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });
      }

      await log_reservation(sdk, req.user_id, reservation_id);

      // Handle buddy request
      if (buddy_request === 1) {
        const { ntrp, num_players, num_needed = 1, type, need_coach = 0, notes = "" } = buddy_details;
        sdk.setTable("buddy");
        const buddy_id = await sdk.insert({
          sport_id,
          type,
          ntrp,
          reservation_id,
          num_players,
          num_needed,
          need_coach,
          date: sqlDateFormat(new Date(date)),
          start_time,
          end_time,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        sdk.setTable("reservation");
        await sdk.update({ buddy_id: buddy_id }, reservation_id);
      }

      return res.status(200).json({ error: false, reservation_id });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });


  app.get(_base + "/billing/coach-invoices/:coach_id", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
  
      // Get query parameters
      const { sort = 'desc', invoice_type } = req.query;
      const { coach_id } = req.params;
      
      // Validate sort parameter
      if (sort !== 'asc' && sort !== 'desc') {
        throw new Error("Sort parameter must be 'asc' or 'desc'");
      }

      sdk.setTable("coach");
      const coach = await sdk.get({ id: coach_id });
      if (!coach.length) {
        throw new Error("Coach not found");
      }

      // First get the user's stripe_uid
      sdk.setTable("user");
      const user = await sdk.get({ id: coach[0].user_id });
      if (!user.length) {
        throw new Error("User not found");
      }
      const stripe_uid = user[0].stripe_uid;
      req.user_id = coach[0].user_id;
  
      // Get coach/staff earnings from bookings
      let earnings_sql = `
        SELECT 
          b.id,
          b.receipt_id,
          b.coach_fee as amount,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          b.coach_fee AS total_amount,
          c.name as club_name,
          u.first_name as user_first_name,
          u.last_name as user_last_name
        FROM 
          courtmatchup_booking b
        LEFT JOIN
          courtmatchup_clubs c ON b.club_id = c.id
        LEFT JOIN
          courtmatchup_user u ON b.user_id = u.id
        WHERE 
          b.coach_id IN (
            SELECT id FROM courtmatchup_coach WHERE user_id = ${req.user_id}
          )
          AND b.status = ${BOOKING_STATUSES.SUCCESS}
          AND b.coach_fee > 0
      `;
  
      // Add invoice_type filter for bookings if specified
      if (invoice_type) {
        switch(invoice_type.toLowerCase()) {
          case 'court':
            earnings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
            break;
          case 'lesson':
            earnings_sql += ` AND b.lesson_id IS NOT NULL`;
            break;
          case 'clinic':
            earnings_sql += ` AND b.clinic_id IS NOT NULL`;
            break;
        }
      }

  
      let earnings = [], subscriptions = [];
      
      // Only fetch relevant data based on invoice_type filter
      if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
        earnings = await sdk.rawQuery(earnings_sql);
      }
      
      // if (!invoice_type || invoice_type.toLowerCase() === 'subscription') {
      //   subscriptions = await sdk.rawQuery(subscriptions_sql);
      // }
  
      // Process subscription data to extract amount and other details
      const processed_subscriptions = subscriptions.map(sub => {
        const obj = JSON.parse(sub.object);
        const paymentMethod = obj.default_payment_method;
        let paymentMethodDisplay = 'Unknown Payment Method';
        
        if (paymentMethod && typeof paymentMethod === 'string') {
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethod && paymentMethod.card) {
          paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
        }

        return {
          id: sub.id,
          stripe_id: sub.stripe_id,
          subscription_id: obj.id,
          price_id: sub.price_id,
          status: sub.status,
          create_at: sub.create_at,
          invoice_type: 'Subscription',
          type: obj.plan?.nickname || 'Subscription Plan',
          plan_name: obj.plan?.product || 'Standard Plan',
          total_amount: obj.plan?.amount || 0,
          currency: obj.currency || 'usd',
          interval: obj.plan?.interval || 'month',
          payment_method: paymentMethodDisplay,
          club_name: sub.club_name,
          valid_until: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          current_period_start: obj.current_period_start ? 
            new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
          current_period_end: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          canceled_at: obj.canceled_at ? 
            new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
        };
      });
  
      // Format earnings data to match structure
      const processed_earnings = earnings.map(earning => ({
        ...earning,
        currency: 'usd',
        total_amount: parseFloat(earning.amount),
        status: earning.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        valid_until: earning.date
      }));
  
      // Combine and sort all invoices by create_at date
      const all_invoices = [...processed_earnings, ...processed_subscriptions].sort((a, b) => {
        const dateComparison = new Date(b.create_at) - new Date(a.create_at);
        return sort === 'desc' ? dateComparison : -dateComparison;
      });
  
      // Calculate totals
      const total_earnings = all_invoices.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      const active_subscriptions = processed_subscriptions.filter(sub => 
        sub.status === 'active'
      );
  
      // Group invoices by type
      const invoices_by_type = all_invoices.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: all_invoices,
        invoices_by_type,
        total_earnings: total_earnings / 100, // Convert from cents to dollars
        active_subscriptions: active_subscriptions,
        total_invoices: all_invoices.length,
        filters: {
          sort,
          invoice_type: invoice_type || 'all'
        }
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


    // Get staff billing history/invoices
    app.get(_base + "/billing/staff-invoices", combinedMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
    
        // Get query parameters
        const { sort = 'desc', invoice_type } = req.query;

        const {role, staff_id} = req;
        let club_id = req.query?.club_id || null;

        if(role == "club"){
          sdk.setTable("clubs");
          const club = await sdk.get({ user_id: req.user_id });
          if (!club.length) {
            throw new Error("Club not found");
          }
          club_id = club[0].id;
        }else if(role == "staff"){
          sdk.setTable("user");
          const user = await sdk.get({ id: req.user_id });
          if (!user.length) {
            throw new Error("User not found");
          }
          club_id = user[0].club_id;
        }

        if(!club_id){
          throw new Error("Club not found");
        }
        
        // Validate sort parameter
        if (sort !== 'asc' && sort !== 'desc') {
          throw new Error("Sort parameter must be 'asc' or 'desc'");
        }

    
        return res.status(200).json({
          error: false,
          invoices: [],
          invoices_by_type: {},
          total_earnings: 0,
          total_invoices: 0,
          filters: {
            sort,
            invoice_type: invoice_type || 'all'
          }
        });
    
      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });
    
  app.get(_base + "/billing/coach-invoices", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
  
      // Get query parameters
      const { sort = 'desc', invoice_type } = req.query;
      const {role} = req;
      let club_id = req.query?.club_id || null;

      if(role == "club"){
        sdk.setTable("clubs");
        const club = await sdk.get({ user_id: req.user_id });
        if (!club.length) {
          throw new Error("Club not found");
        }
        club_id = club[0].id;
      }else if(role == "staff"){
        sdk.setTable("user");
        const user = await sdk.get({ id: req.user_id });
        if (!user.length) {
          throw new Error("User not found");
        }
        club_id = user[0].club_id;
      }

      if(!club_id){
        throw new Error("Club not found");
      }
      
      
      // Validate sort parameter
      if (sort !== 'asc' && sort !== 'desc') {
        throw new Error("Sort parameter must be 'asc' or 'desc'");
      }

  
      // Get coach/staff earnings from bookings
      let earnings_sql = `
        SELECT 
          b.id,
          b.receipt_id,
          b.coach_fee as amount,
          b.date,
          b.create_at,
          b.status,
          b.reservation_type,
          'Checkout' as payment_method,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS type,
          CASE
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLUB_COURT} THEN 'Club Court'
            WHEN b.reservation_type = ${RESERVATION_TYPES.CLINIC} THEN 'Clinic'
            WHEN b.reservation_type = ${RESERVATION_TYPES.LESSONS} THEN 'Lesson'
            WHEN b.reservation_type = ${RESERVATION_TYPES.COACH} THEN 'Coach'
            ELSE 'unknown'
          END AS invoice_type,
          b.coach_fee AS total_amount,
          c.name as club_name,
          u.first_name as user_first_name,
          u.last_name as user_last_name
        FROM 
          courtmatchup_booking b
        LEFT JOIN
          courtmatchup_clubs c ON b.club_id = c.id
        LEFT JOIN
          courtmatchup_user u ON b.user_id = u.id
        WHERE 
          b.club_id = ${club_id}
          AND b.coach_id IS NOT NULL
          AND b.status = ${BOOKING_STATUSES.SUCCESS}
          AND b.coach_fee > 0
      `;
  
      // Add invoice_type filter for bookings if specified
      if (invoice_type) {
        switch(invoice_type.toLowerCase()) {
          case 'court':
            earnings_sql += ` AND b.court_id IS NOT NULL AND b.clinic_id IS NULL AND b.lesson_id IS NULL`;
            break;
          case 'lesson':
            earnings_sql += ` AND b.lesson_id IS NOT NULL`;
            break;
          case 'clinic':
            earnings_sql += ` AND b.clinic_id IS NOT NULL`;
            break;
        }
      }

  
      let earnings = [], subscriptions = [];
      
      // Only fetch relevant data based on invoice_type filter
      if (!invoice_type || invoice_type.toLowerCase() !== 'subscription') {
        earnings = await sdk.rawQuery(earnings_sql);
      }
      
      // if (!invoice_type || invoice_type.toLowerCase() === 'subscription') {
      //   subscriptions = await sdk.rawQuery(subscriptions_sql);
      // }
  
      // Process subscription data to extract amount and other details
      const processed_subscriptions = subscriptions.map(sub => {
        const obj = JSON.parse(sub.object);
        const paymentMethod = obj.default_payment_method;
        let paymentMethodDisplay = 'Unknown Payment Method';
        
        if (paymentMethod && typeof paymentMethod === 'string') {
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethod && paymentMethod.card) {
          paymentMethodDisplay = `Credit Card ***${paymentMethod.card.last4}`;
        }

        return {
          id: sub.id,
          stripe_id: sub.stripe_id,
          subscription_id: obj.id,
          price_id: sub.price_id,
          status: sub.status,
          create_at: sub.create_at,
          invoice_type: 'Subscription',
          type: obj.plan?.nickname || 'Subscription Plan',
          plan_name: obj.plan?.product || 'Standard Plan',
          total_amount: obj.plan?.amount || 0,
          currency: obj.currency || 'usd',
          interval: obj.plan?.interval || 'month',
          payment_method: paymentMethodDisplay,
          club_name: sub.club_name,
          valid_until: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          current_period_start: obj.current_period_start ? 
            new Date(obj.current_period_start * 1000).toISOString().split('T')[0] : null,
          current_period_end: obj.current_period_end ? 
            new Date(obj.current_period_end * 1000).toISOString().split('T')[0] : null,
          canceled_at: obj.canceled_at ? 
            new Date(obj.canceled_at * 1000).toISOString().split('T')[0] : null
        };
      });
  
      // Format earnings data to match structure
      const processed_earnings = earnings.map(earning => ({
        ...earning,
        currency: 'usd',
        total_amount: parseFloat(earning.amount),
        status: earning.status === BOOKING_STATUSES.SUCCESS ? 'completed' : 'failed',
        valid_until: earning.date
      }));
  
      // Combine and sort all invoices by create_at date
      const all_invoices = [...processed_earnings, ...processed_subscriptions].sort((a, b) => {
        const dateComparison = new Date(b.create_at) - new Date(a.create_at);
        return sort === 'desc' ? dateComparison : -dateComparison;
      });
  
      // Calculate totals
      const total_earnings = all_invoices.reduce((sum, invoice) => 
        sum + (invoice.total_amount || 0), 0
      );
  
      const active_subscriptions = processed_subscriptions.filter(sub => 
        sub.status === 'active'
      );
  
      // Group invoices by type
      const invoices_by_type = all_invoices.reduce((acc, invoice) => {
        const type = invoice.type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(invoice);
        return acc;
      }, {});
  
      return res.status(200).json({
        error: false,
        invoices: all_invoices,
        invoices_by_type,
        total_earnings: total_earnings / 100, // Convert from cents to dollars
        active_subscriptions: active_subscriptions,
        total_invoices: all_invoices.length,
        filters: {
          sort,
          invoice_type: invoice_type || 'all'
        }
      });
  
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


    // Get staff billing history/invoices
    app.get(_base + "/billing/staff-invoices/:staff_id", combinedMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
    
        // Get query parameters
        const { sort = 'desc', invoice_type } = req.query;
        const { staff_id } = req.params;
        
        // Validate sort parameter
        if (sort !== 'asc' && sort !== 'desc') {
          throw new Error("Sort parameter must be 'asc' or 'desc'");
        }

        sdk.setTable("staff");
        const staff = await sdk.get({ id: staff_id });
        if (!staff.length) {
          throw new Error("Staff not found");
        }

        // First get the user's stripe_uid
        sdk.setTable("user");
        const user = await sdk.get({ id: staff[0].user_id });
        if (!user.length) {
          throw new Error("User not found");
        }
    
        return res.status(200).json({
          error: false,
          invoices: [],
          invoices_by_type: {},
          total_earnings: 0,
          total_invoices: 0,
          filters: {
            sort,
            invoice_type: invoice_type || 'all'
          }
        });
    
      } catch (err) {
        console.error(err);
        res.status(403).json({
          error: true,
          message: err.message
        });
      }
    });
    

  app.post(base + "/mail/custom-request", combinedMiddlewares, async function (req, res) {
    try {
      const validationResult = await ValidationService.validateInputMethod(
        {
          to: "required",
          subject: "required",
          club_id: "required",
          user_id: "required",
          request: "required",
        },
        {
          to: "to is required",
          subject: "subject is required",
          request: "request is required",
          club_id: "club_id is required",
          user_id: "user_id is required",
        },
        req
      );

      if (validationResult.error) {
        return res.status(403).json(validationResult);
      }

      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      //TODO template selection
      MailService.initialize(config);
      const result = await MailService.send(config.from_mail, req.body.to, req.body.subject, req.body.request, req.body.cc);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result,
        });
      }

      console.log("Mail Response:", result);
      if (result.error) {
        return res.status(403).json(result);
      } else {
        sdk.setTable("club_requests");
        await sdk.insert(filterEmptyFields({
          club_id: req.body.club_id,
          email: req.body.to,
          user_id: req.body.user_id,
          booking_id: req.body.booking_id,
          reservation_id: req.body.reservation_id,
          subject: req.body.subject,
          request: req.body.request,
          created_by: req.user_id,
          role: req.role,
          status: 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        }));

        return res.status(200).json({
          error: false,
          message: "Email Sent",
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });


  // fetch custom request
  app.get(base + "/custom-request/:club_id", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { club_id } = req.params;
      const { user_id } = req;

      sdk.setTable("club_requests");
      const requests = await sdk.get({ club_id: club_id, user_id: user_id });

      return res.status(200).json({ error: false, requests });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });
  // fetch coachesd for a club
  app.get(_base + "/coaches/:club_id", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { club_id } = req.params;
      const { first_name, last_name, sport_id, status, phone } = req.query;

      // Build the SQL query with JOIN statements and filters
      let coachesQuery = `
        SELECT 
          c.id,
          c.user_id,
          c.bio,
          c.hourly_rate,
          c.availability,
          c.type,
          c.is_public,
          u.first_name,
          u.last_name,
          u.email,
          u.photo,
          u.phone,
          GROUP_CONCAT(
            DISTINCT CONCAT(
              cs.id, '=',
              cs.sport_id, '=',
              cs.type, '=',
              cs.sub_type, '=',
              cs.price, '=',
              s.name
            )
          ) as coach_sports
        FROM 
          courtmatchup_coach c
        JOIN 
          courtmatchup_user u ON c.user_id = u.id
        LEFT JOIN
          courtmatchup_coach_sports cs ON c.id = cs.coach_id
        LEFT JOIN
          courtmatchup_sports s ON cs.sport_id = s.id
        WHERE 
          c.club_id = ${club_id}
      `;

      // Add filters if provided
      const filters = [];
      
      if (first_name) {
        filters.push(`u.first_name LIKE '%${first_name}%'`);
      }
      
      if (last_name) {
        filters.push(`u.last_name LIKE '%${last_name}%'`);
      }
      
      if (sport_id) {
        filters.push(`cs.sport_id = ${sport_id}`);
      }

      if (phone) {
        filters.push(`u.phone LIKE '%${phone}%'`);
      }
      
      // if (status !== undefined && status !== null) {
      //   filters.push(`c.status = ${status}`);
      // }
      
      // Add filters to the query
      if (filters.length > 0) {
        coachesQuery += ` AND ${filters.join(' AND ')}`;
      }
      
      // Group by coach ID to avoid duplicates
      coachesQuery += `
        GROUP BY 
          c.id
        ORDER BY
          u.first_name ASC, 
          u.last_name ASC
      `;

      // Execute the query
      const coaches = await sdk.rawQuery(coachesQuery);

      // Process the results to parse the sports data
      const processedCoaches = coaches.map(coach => {
        // Parse availability JSON
        let availability = [];
        try {
          if (coach.availability) {
            availability = JSON.parse(coach.availability);
          }
        } catch (e) {
          console.error("Error parsing coach availability:", e);
        }

        // Process coach sports data
        const sports = [];
        if (coach.coach_sports) {
          const sportsArray = coach.coach_sports.split(',');
          sportsArray.forEach(sportData => {
            const [id, sport_id, type, sub_type, price, name] = sportData.split('=');
            sports.push({
              id: parseInt(id),
              sport_id: parseInt(sport_id),
              type,
              sub_type,
              price: parseInt(price),
              name
            });
          });
        }

        // Return processed coach data
        return {
          id: coach.id,
          user_id: coach.user_id,
          bio: coach.bio,
          hourly_rate: coach.hourly_rate,
          type: coach.type,
          is_public: coach.is_public,
          phone: coach.phone,
          // status: coach.status,
          availability,
          first_name: coach.first_name,
          last_name: coach.last_name,
          email: coach.email,
          photo: coach.photo,
          sports
        };
      });

      // Table: courtmatchup_coach_sports
      // Select data Show structure Alter table New item

      // Column	Type	Comment
      // id	int(11) Auto Increment	 
      // coach_id	int(11)	 
      // sport_id	int(11)	 
      // type	text NULL	 
      // sub_type	text NULL	 
      // price	int(11) NULL	 
      // create_at	date	 
      // update_at	datetime	 

      // Get all sports for filtering options
      const sportsQuery = `
        SELECT 
          cs.id, 
          s.name, 
          cs.sport_id,
          cs.type, 
          cs.sub_type,
          cs.price
        FROM 
          courtmatchup_coach_sports cs
        LEFT JOIN
          courtmatchup_sports s ON cs.sport_id = s.id
        ORDER BY 
          name ASC
      `;
    

      // Return the response
      return res.status(200).json({
        error: false,
        coaches: processedCoaches,
        total_coaches: processedCoaches.length,
        filters: {
          first_name: first_name || "",
          last_name: last_name || "",
          sport_id: sport_id || "",
          phone: phone || "",
        }
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({ error: true, message: err.message });
    }
  });


  // close  custom request
  app.post(base + "/custom-request/:club_id", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { club_id } = req.params;
      const { request_id, to, subject, body } = req.body;

      sdk.setTable("club_requests");
      await sdk.updateWhere({ status: 1 }, { id: request_id, club_id: club_id });

      if(to && subject && body){
        MailService.initialize(config);
        const result = await MailService.send(config.from_mail, to, subject, body);

        if (typeof result == "string") {
          return res.status(403).json({ error: true, message: result });
        }
      }

      return res.status(200).json({ error: false, message: "Request closed" });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });


  // fetch coach reservation
  app.get(_base + "/coach/:club_id", combinedMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { coach_id=null } = req.query;
      const { club_id } = req.params;

      sdk.setTable("clubs");
      const club = (await sdk.get({ id: club_id }))[0];
      if (!club) {
        throw new Error("Club not found");
      }
      if(coach_id){
        sdk.setTable("coach");
        const coach = (await sdk.get({ id: coach_id, club_id: club_id }))[0];
        if (!coach) {
          throw new Error("Coach not found");
        }
      }

      const sql = `
              SELECT 
              u.first_name,
              u.last_name,
              u.email,
              u.photo,
              co.bio,
              co.hourly_rate,
              co.availability,
              r.id AS reservation_id,
              r.booking_id,
              r.buddy_id,
              r.user_id AS reservation_user_id,
              r.status AS reservation_status,
              r.create_at AS reservation_created_at,
              r.update_at AS reservation_updated_at,
              b.user_id AS booking_user_id,
              b.sport_id,
              cs.name AS sport_name,
              b.type AS booking_type,
              b.date AS booking_date,
              b.start_time,
              b.end_time,
              b.court_id,
              b.coach_id,
              b.clinic_id,
              b.lesson_id,
              b.subtype as sub_type,
              b.type as type,
              b.notes,
              b.price,
              b.club_fee,
              b.service_fee,
              b.coach_fee,
              b.clinic_fee,
              b.court_fee,
              b.custom_request,
              b.payment_intent,
              b.player_ids,
              b.duration,
              b.status AS booking_status,
              c.name AS clinic_name,
              c.start_time AS clinic_start_time,
              c.end_time AS clinic_end_time,
              c.cost_per_head AS clinic_cost,
              c.max_participants,
              c.level AS clinic_level,
              CASE 
                  WHEN b.player_ids IS NOT NULL AND b.player_ids != '' THEN 
                      LENGTH(b.player_ids) - LENGTH(REPLACE(b.player_ids, ',', '')) + 1
                  ELSE 0
              END AS num_players,
              CASE
                  WHEN b.clinic_id IS NOT NULL THEN 'Clinic'
                  WHEN b.lesson_id IS NOT NULL THEN 'Lesson'
                  WHEN b.court_id IS NOT NULL THEN 'Court'
                  WHEN b.coach_id IS NOT NULL THEN 'Coach'
                  WHEN r.buddy_id IS NOT NULL THEN 'Find Buddy'
                  ELSE 'Court'
              END AS booking_type,
              'reservation' as entry_type,
              1 as reserved
          FROM 
              courtmatchup_reservation r
          LEFT JOIN 
              courtmatchup_booking b ON r.booking_id = b.id
          LEFT JOIN
              courtmatchup_coach co ON b.coach_id = co.id
          LEFT JOIN
              courtmatchup_user u ON u.id = co.user_id
          LEFT JOIN 
              courtmatchup_clinics c ON b.clinic_id = c.id
          LEFT JOIN
              courtmatchup_sports cs ON b.sport_id = cs.id
          WHERE 
              (
                  r.club_id = ${club_id}
                  ${coach_id ? `AND b.coach_id = ${coach_id}` : 'AND b.coach_id IS NOT NULL'}
              )
          GROUP BY
            b.id
      `;
      
      const booking = await sdk.rawQuery(sql);
      return res.status(200).json({ error: false, booking });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });

  // Create/update scheduled email
  app.post(base + "/scheduling", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      let {
        email_id = null,
        subject,
        message,
        date,
        repeat = 0,
        type = 0,
        days = [],
        end_date = null,
        condition = {},
        attachments = [],
        players=[]
      } = req.body;

      if (!email_id && (!subject || !message || !date || !days.length)) {
        throw new Error("All fields are required");
      }
      let _condition = false;

      if (Object.keys(condition).length > 0) {
        _condition = true;
        const { before, time, condition_type, time_type } = condition;
        if ((!before && before !== 0) || (!condition_type && condition_type !== 0) || (!time_type && time_type !== 0) || (!time && time !== 0)) {
          throw new Error("All condition fields are required");
        }
      }

      if (!email_id) {
        if (players) {
          players = Array.isArray(players) ? JSON.stringify(players) : players;
        } else {
          players = "ALL";
        }
        sdk.setTable("club_scheduled_email");
        await sdk.insert(filterEmptyFields({
          subject,
          date,
          message,
          recurring: repeat,
          days: JSON.stringify(days),
          end_date,
          attachments: JSON.stringify(attachments),
          players,
          before_action: condition?.before,
          time_action: condition.time,
          condition_type: condition.condition_type,
          time_type: condition.time_type,
        }));
      } else {
        if (players) {
          players = Array.isArray(players) ? JSON.stringify(players) : players;
        }
        sdk.setTable("club_scheduled_email");
        await sdk.update(filterEmptyFields({
          subject,
          date,
          message,
          recurring: repeat,
          days: JSON.stringify(days),
          end_date,
          attachments: JSON.stringify(attachments),
          players,
          before_action: condition?.before,
          time_action: condition?.time,
          condition_type: condition?.condition_type,
          time_type: condition?.time_type,
        }));
      }

      const currentDate = new Date();
      const emailDate = new Date(date);
      const emailEndDate = end_date ? new Date(end_date) : null;
      const currentDay = currentDate.toLocaleString('en-us', { weekday: 'long' }).toLowerCase();

      if (emailDate <= currentDate && (!emailEndDate || emailEndDate >= currentDate) && days.includes(currentDay)) {
        console.log("Sending email");
        MailService.initialize(config);

        const mailAttachments = attachments.map(link => ({
          path: link
        }));

        console.log(players);
        const p = Array.isArray(players)

        if (p) {
          console.log("Sending to multiple players");
          for (const playerEmail of players) {
            const response = await MailService.send(config.from_mail, playerEmail, subject, message, mailAttachments);
            console.log(response);
          }
        }
      }


      return res.status(200).json({ 
        error:false,
        message: "Scheduled",
        mapping: {
          recurring: {
            0: false,
            1: true
          },
          condition_type: {
            0: "reservation",
            1: "clinic"
          },
          before_action: {
            0: "before",
            1: "after"
          },
          time_type: {
            0: "minutes",
            1: "hours",
            2: "days",
            3: "weeks"
          }
        }
      });
    } catch (err) {
      res.status(403).json({ error: true, message: err.message });
    }
  });

  app.get("/v3/api/custom/courtmatchup/club/reservations/availability", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);

      const { user_id } = req;
      const { date, time, sport_id, from_time, until_time, from_date, until_date } = req.query;
      sdk.setTable("clubs");
      const club = (await sdk.get({ user_id: user_id }))[0];
      const club_id = club?.id;

      // Validate club_id
      if (!club_id) {
        throw new Error("Club ID is required");
      }

      // Set default date ranges if not provided
      const now = new Date();
      const effectiveFromDate = from_date ? new Date(from_date) : now;
      const effectiveUntilDate = until_date ? new Date(until_date) : new Date(now.setDate(now.getDate() + 30));
      
      // Format dates for SQL queries
      const fromDateSQL = sqlDateFormat(effectiveFromDate);
      const untilDateSQL = sqlDateFormat(effectiveUntilDate);
      
      // Time filter conditions
      const timeFilterSQL = (from_time || until_time) ? 
        `${from_time ? `AND start_time >= '${from_time}'` : ''} 
         ${until_time ? `AND end_time <= '${until_time}'` : ''}` : '';

      // Get bookings for courts
      const courtBookingsSql = `
        SELECT 
          court_id, 
          date, 
          start_time, 
          end_time 
        FROM 
          courtmatchup_booking 
        LEFT JOIN
          courtmatchup_reservation r ON courtmatchup_booking.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND date BETWEEN '${fromDateSQL}' AND '${untilDateSQL}'
          ${sport_id ? `AND sport_id = ${sport_id}` : ''}
          ${timeFilterSQL}
      `;

      // Get courts with their availability
      const courtsSql = `
        SELECT 
          cc.id,
          cc.name,
          cc.type,
          cc.sub_type as subtype,
          cc.sport_id,
          cc.surface_id,
          cc.availability,
          cs.name as sport_name,
          csr.name as surface_name
        FROM 
          courtmatchup_club_court cc
        LEFT JOIN
          courtmatchup_sports cs ON cc.sport_id = cs.id
        LEFT JOIN
          courtmatchup_surface csr ON cc.surface_id = csr.id
        WHERE 
          cc.club_id = ${club_id}
          ${sport_id ? `AND cc.sport_id = ${sport_id}` : ''}
        GROUP BY 
          cc.id
      `;

      // Get bookings for coaches
      const coachBookingsSql = `
        SELECT 
          coach_id, 
          date, 
          start_time, 
          end_time 
        FROM 
          courtmatchup_booking 
        LEFT JOIN
          courtmatchup_reservation r ON courtmatchup_booking.id = r.booking_id
        WHERE 
          coach_id IS NOT NULL
          AND date BETWEEN '${fromDateSQL}' AND '${untilDateSQL}'
          AND r.club_id = ${club_id}
          ${sport_id ? `AND sport_id = ${sport_id}` : ''}
          ${timeFilterSQL}
      `;

      // Get coaches with their availability and sports
      const coachesSql = `
        SELECT 
          c.id as coach_id,
          c.user_id,
          c.bio,
          c.hourly_rate,
          c.availability,
          u.first_name,
          u.last_name,
          u.email,
          u.photo,
          GROUP_CONCAT(
            DISTINCT CONCAT(
              cs.sport_id, '=',
              cs.type, '=',
              cs.sub_type, '=',
              cs.price, '=',
              s.name
            )
          ) as coach_sports
        FROM 
          courtmatchup_coach c
        JOIN 
          courtmatchup_user u ON c.user_id = u.id
        LEFT JOIN
          courtmatchup_coach_sports cs ON c.id = cs.coach_id
        LEFT JOIN
          courtmatchup_sports s ON cs.sport_id = s.id
        WHERE 
          c.club_id = ${club_id}
          AND u.role = 'coach'
          ${sport_id ? `AND cs.sport_id = ${sport_id}` : ''}
        GROUP BY 
          c.id
      `;


      // Get available staff with their availability
      const staffSql = `
        SELECT 
          s.id as staff_id,
          s.user_id,
          s.availability,
          u.role as staff_role,
          u.first_name,
          u.last_name,
          u.email,
          u.photo
        FROM 
          courtmatchup_staff s
        JOIN 
          courtmatchup_user u ON s.user_id = u.id
        WHERE 
          s.club_id = ${club_id}
          AND u.role = 'staff'
      `;

      // Sports query
      const sportsSql = `
        SELECT 
          s.id,
          s.name,
          s.type,
          s.sub_type,
          COALESCE(
            GROUP_CONCAT(
              DISTINCT b.date
            ),
            ';;'
          ) as booked_dates
        FROM 
          courtmatchup_sports s
        LEFT JOIN
          courtmatchup_booking b ON s.id = b.sport_id
        WHERE
          s.id IN (
            SELECT 
              sport_id 
            FROM 
              courtmatchup_booking b
            JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            WHERE
              r.club_id = ${club_id}
              AND b.date >= '${fromDateSQL}'
              ${sport_id ? `AND b.sport_id = ${sport_id}` : ''}
          )
          GROUP BY
            s.id
      `;

      // Execute all queries in parallel
      const [
        courts, 
        courtBookings, 
        coaches, 
        coachBookings, 
        staff, 
        sports
      ] = await Promise.all([
        sdk.rawQuery(courtsSql),
        sdk.rawQuery(courtBookingsSql),
        sdk.rawQuery(coachesSql),
        sdk.rawQuery(coachBookingsSql),
        sdk.rawQuery(staffSql),
        sdk.rawQuery(sportsSql)
      ]);

      // Process data using generateAvailabilityData function
      const { generateAvailabilityData } = require("../utils/util");

      // Process courts data
      const processedCourts = courts.map(court => {
        // Group bookings by court
        const courtSpecificBookings = courtBookings.filter(booking => booking.court_id === court.id);
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'club_court',
          entity: court,
          bookings: courtSpecificBookings,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...court,
          availabilityData
        };
      });

      // Process coaches data
      const processedCoaches = coaches.map(coach => {
        // Parse coach sports
        const coach_sports = coach.coach_sports ? 
          coach.coach_sports.split(',').map(sport => {
            const [sport_id, type, sub_type, price, sport_name] = sport.split('=');
            return {
              sport_id: parseInt(sport_id),
              type,
              sub_type,
              price: parseInt(price),
              sport_name
            };
          }) : [];
        
        // Group bookings by coach
        const coachSpecificBookings = coachBookings.filter(booking => booking.coach_id === coach.coach_id);
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'coach',
          entity: coach,
          bookings: coachSpecificBookings,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...coach,
          sports: coach_sports,
          availabilityData
        };
      });

      // Process staff data
      const processedStaff = staff.map(staffMember => {
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'staff',
          entity: staffMember,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...staffMember,
          availabilityData
        };
      });

      // Process sports data
      const processedSports = sports.map(sport => {
        return {
          ...sport,
          booked_dates: sport.booked_dates.split(';;').filter(date => date)
        };
      });

      // Replace the generateTimeRanges function with this improved version
      const generateTimeRanges = (courts) => {
        // Collect all available time slots grouped by date
        const timeSlotsByDate = new Map();
        
        courts.forEach(court => {
          if (court.availabilityData && court.availabilityData.availability) {
            court.availabilityData.availability.forEach(slot => {
              if (slot.date && slot.time) {
                if (!timeSlotsByDate.has(slot.date)) {
                  timeSlotsByDate.set(slot.date, new Set());
                }
                timeSlotsByDate.get(slot.date).add(slot.time);
              }
            });
          }
        });
        
        // Convert the map to an array of date-based time ranges
        const dateTimeRanges = [];
        
        for (const [date, timeSlots] of timeSlotsByDate.entries()) {
          // Sort the time slots for this date
          const sortedTimeSlots = Array.from(timeSlots).sort((a, b) => {
            const [aHour, aMin] = a.split(':').map(Number);
            const [bHour, bMin] = b.split(':').map(Number);
            return aHour === bHour ? aMin - bMin : aHour - bHour;
          });
          
          if (sortedTimeSlots.length > 0) {
            dateTimeRanges.push({
              date,
              slots: sortedTimeSlots,
              from: sortedTimeSlots[0],
              until: sortedTimeSlots[sortedTimeSlots.length - 1]
            });
          }
        }
        
        // If no dates with time slots are found, create a default range
        if (dateTimeRanges.length === 0) {
          const startHour = from_time ? parseInt(from_time.split(':')[0]) : 6;
          const endHour = until_time ? parseInt(until_time.split(':')[0]) : 22;
          
          const defaultTimeSlots = [];
          for (let i = startHour; i <= endHour; i++) {
            defaultTimeSlots.push(`${i.toString().padStart(2, '0')}:00:00`);
          }
          
          // Use the from_date or current date for the default
          const defaultDate = from_date ? from_date : sqlDateFormat(new Date());
          
          dateTimeRanges.push({
            date: defaultDate,
            slots: defaultTimeSlots,
            from: defaultTimeSlots[0],
            until: defaultTimeSlots[defaultTimeSlots.length - 1]
          });
        }
        
        // Also create an aggregate view of all time slots across all dates
        const allTimeSlots = new Set();
        dateTimeRanges.forEach(dateRange => {
          dateRange.slots.forEach(slot => allTimeSlots.add(slot));
        });
        
        const sortedAllTimeSlots = Array.from(allTimeSlots).sort((a, b) => {
          const [aHour, aMin] = a.split(':').map(Number);
          const [bHour, bMin] = b.split(':').map(Number);
          return aHour === bHour ? aMin - bMin : aHour - bHour;
        });
        
        return {
          byDate: dateTimeRanges,
          aggregate: {
            slots: sortedAllTimeSlots,
            from: sortedAllTimeSlots[0] || (from_time || "06:00:00"),
            until: sortedAllTimeSlots[sortedAllTimeSlots.length - 1] || (until_time || "22:00:00")
          }
        };
      };

      // Get time ranges based on available courts
      const timeRanges = generateTimeRanges(processedCourts);

      // Return formatted response
      return res.status(200).json({
        error: false,
        courts: {
          available: processedCourts.filter(court => court.availabilityData.availability.length > 0),
          unavailable: (processedCourts.filter(court => court.availabilityData.unavailability.length > 0)).length > 0 ? processedCourts.filter(court => court.availabilityData.unavailability.length > 0) : null,
          total: courts.length
        },
        hours: {
          available: timeRanges.aggregate.slots,
          total: timeRanges.aggregate.slots.length,
          range: {
            from: timeRanges.aggregate.from,
            until: timeRanges.aggregate.until
          },
          byDate: timeRanges.byDate
        },
        coaches: {
          available: processedCoaches.filter(coach => coach.availabilityData.availability.length > 0),
          unavailable: (processedCoaches.filter(coach => coach.availabilityData.unavailability.length > 0)).length > 0 ? processedCoaches.filter(coach => coach.availabilityData.unavailability.length > 0) : null,
          total: coaches.length
        },
        staff: {
          available: processedStaff.filter(staff => staff.availabilityData.availability.length > 0),
          unavailable: (processedStaff.filter(staff => staff.availabilityData.unavailability.length > 0)).length > 0 ? processedStaff.filter(staff => staff.availabilityData.unavailability.length > 0) : null,
          total: staff.length
        },
        sports: processedSports,
        date_range: {
          from: fromDateSQL,
          until: untilDateSQL
        },
        time_range: {
          from: from_time || "00:00:00",
          until: until_time || "23:59:59"
        }
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/courtmatchup/admin/reservations/availability", adminMiddlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      const { date, time, club_id, sport_id, from_time, until_time, from_date, until_date } = req.query;
      sdk.setTable("clubs");

      // Validate club_id
      if (!club_id) {
        throw new Error("Club ID is required");
      }

      // Set default date ranges if not provided
      const now = new Date();
      const effectiveFromDate = from_date ? new Date(from_date) : now;
      const effectiveUntilDate = until_date ? new Date(until_date) : new Date(now.setDate(now.getDate() + 30));
      
      // Format dates for SQL queries
      const fromDateSQL = sqlDateFormat(effectiveFromDate);
      const untilDateSQL = sqlDateFormat(effectiveUntilDate);
      
      // Time filter conditions
      const timeFilterSQL = (from_time || until_time) ? 
        `${from_time ? `AND start_time >= '${from_time}'` : ''} 
         ${until_time ? `AND end_time <= '${until_time}'` : ''}` : '';

      // Get bookings for courts
      const courtBookingsSql = `
        SELECT 
          court_id, 
          date, 
          start_time, 
          end_time 
        FROM 
          courtmatchup_booking 
        LEFT JOIN
          courtmatchup_reservation r ON courtmatchup_booking.id = r.booking_id
        WHERE 
          r.club_id = ${club_id}
          AND date BETWEEN '${fromDateSQL}' AND '${untilDateSQL}'
          ${sport_id ? `AND sport_id = ${sport_id}` : ''}
          ${timeFilterSQL}
      `;

      // Get courts with their availability
      const courtsSql = `
        SELECT 
          cc.id,
          cc.name,
          cc.type,
          cc.sub_type as subtype,
          cc.sport_id,
          cc.surface_id,
          cc.availability,
          cs.name as sport_name,
          csr.name as surface_name
        FROM 
          courtmatchup_club_court cc
        LEFT JOIN
          courtmatchup_sports cs ON cc.sport_id = cs.id
        LEFT JOIN
          courtmatchup_surface csr ON cc.surface_id = csr.id
        WHERE 
          cc.club_id = ${club_id}
          ${sport_id ? `AND cc.sport_id = ${sport_id}` : ''}
        GROUP BY 
          cc.id
      `;

      // Get bookings for coaches
      const coachBookingsSql = `
        SELECT 
          coach_id, 
          date, 
          start_time, 
          end_time 
        FROM 
          courtmatchup_booking 
        LEFT JOIN
          courtmatchup_reservation r ON courtmatchup_booking.id = r.booking_id
        WHERE 
          coach_id IS NOT NULL
          AND date BETWEEN '${fromDateSQL}' AND '${untilDateSQL}'
          AND r.club_id = ${club_id}
          ${sport_id ? `AND sport_id = ${sport_id}` : ''}
          ${timeFilterSQL}
      `;

      // Get coaches with their availability and sports
      const coachesSql = `
        SELECT 
          c.id as coach_id,
          c.user_id,
          c.bio,
          c.hourly_rate,
          c.availability,
          u.first_name,
          u.last_name,
          u.email,
          u.photo,
          GROUP_CONCAT(
            DISTINCT CONCAT(
              cs.sport_id, '=',
              cs.type, '=',
              cs.sub_type, '=',
              cs.price, '=',
              s.name
            )
          ) as coach_sports
        FROM 
          courtmatchup_coach c
        JOIN 
          courtmatchup_user u ON c.user_id = u.id
        LEFT JOIN
          courtmatchup_coach_sports cs ON c.id = cs.coach_id
        LEFT JOIN
          courtmatchup_sports s ON cs.sport_id = s.id
        WHERE 
          c.club_id = ${club_id}
          AND u.role = 'coach'
          ${sport_id ? `AND cs.sport_id = ${sport_id}` : ''}
        GROUP BY 
          c.id
      `;


      // Get available staff with their availability
      const staffSql = `
        SELECT 
          s.id as staff_id,
          s.user_id,
          s.availability,
          u.role as staff_role,
          u.first_name,
          u.last_name,
          u.email,
          u.photo
        FROM 
          courtmatchup_staff s
        JOIN 
          courtmatchup_user u ON s.user_id = u.id
        WHERE 
          s.club_id = ${club_id}
          AND u.role = 'staff'
      `;

      // Sports query
      const sportsSql = `
        SELECT 
          s.id,
          s.name,
          s.type,
          s.sub_type,
          COALESCE(
            GROUP_CONCAT(
              DISTINCT b.date
            ),
            ';;'
          ) as booked_dates
        FROM 
          courtmatchup_sports s
        LEFT JOIN
          courtmatchup_booking b ON s.id = b.sport_id
        WHERE
          s.id IN (
            SELECT 
              sport_id 
            FROM 
              courtmatchup_booking b
            JOIN
              courtmatchup_reservation r ON b.id = r.booking_id
            WHERE
              r.club_id = ${club_id}
              AND b.date >= '${fromDateSQL}'
              ${sport_id ? `AND b.sport_id = ${sport_id}` : ''}
          )
          GROUP BY
            s.id
      `;

      // Execute all queries in parallel
      const [
        courts, 
        courtBookings, 
        coaches, 
        coachBookings, 
        staff, 
        sports
      ] = await Promise.all([
        sdk.rawQuery(courtsSql),
        sdk.rawQuery(courtBookingsSql),
        sdk.rawQuery(coachesSql),
        sdk.rawQuery(coachBookingsSql),
        sdk.rawQuery(staffSql),
        sdk.rawQuery(sportsSql)
      ]);

      // Process data using generateAvailabilityData function
      const { generateAvailabilityData } = require("../utils/util");

      // Process courts data
      const processedCourts = courts.map(court => {
        // Group bookings by court
        const courtSpecificBookings = courtBookings.filter(booking => booking.court_id === court.id);
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'club_court',
          entity: court,
          bookings: courtSpecificBookings,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...court,
          availabilityData
        };
      });

      // Process coaches data
      const processedCoaches = coaches.map(coach => {
        // Parse coach sports
        const coach_sports = coach.coach_sports ? 
          coach.coach_sports.split(',').map(sport => {
            const [sport_id, type, sub_type, price, sport_name] = sport.split('=');
            return {
              sport_id: parseInt(sport_id),
              type,
              sub_type,
              price: parseInt(price),
              sport_name
            };
          }) : [];
        
        // Group bookings by coach
        const coachSpecificBookings = coachBookings.filter(booking => booking.coach_id === coach.coach_id);
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'coach',
          entity: coach,
          bookings: coachSpecificBookings,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...coach,
          sports: coach_sports,
          availabilityData
        };
      });

      // Process staff data
      const processedStaff = staff.map(staffMember => {
        
        // Generate availability data
        const availabilityData = generateAvailabilityData({
          type: 'staff',
          entity: staffMember,
          from_date: from_date,
          until_date: until_date,
          from_time: from_time,
          until_time: until_time
        });
        
        return {
          ...staffMember,
          availabilityData
        };
      });

      // Process sports data
      const processedSports = sports.map(sport => {
        return {
          ...sport,
          booked_dates: sport.booked_dates.split(';;').filter(date => date)
        };
      });

      // Replace the generateTimeRanges function with this improved version
      const generateTimeRanges = (courts) => {
        // Collect all available time slots grouped by date
        const timeSlotsByDate = new Map();
        
        courts.forEach(court => {
          if (court.availabilityData && court.availabilityData.availability) {
            court.availabilityData.availability.forEach(slot => {
              if (slot.date && slot.time) {
                if (!timeSlotsByDate.has(slot.date)) {
                  timeSlotsByDate.set(slot.date, new Set());
                }
                timeSlotsByDate.get(slot.date).add(slot.time);
              }
            });
          }
        });
        
        // Convert the map to an array of date-based time ranges
        const dateTimeRanges = [];
        
        for (const [date, timeSlots] of timeSlotsByDate.entries()) {
          // Sort the time slots for this date
          const sortedTimeSlots = Array.from(timeSlots).sort((a, b) => {
            const [aHour, aMin] = a.split(':').map(Number);
            const [bHour, bMin] = b.split(':').map(Number);
            return aHour === bHour ? aMin - bMin : aHour - bHour;
          });
          
          if (sortedTimeSlots.length > 0) {
            dateTimeRanges.push({
              date,
              slots: sortedTimeSlots,
              from: sortedTimeSlots[0],
              until: sortedTimeSlots[sortedTimeSlots.length - 1]
            });
          }
        }
        
        // If no dates with time slots are found, create a default range
        if (dateTimeRanges.length === 0) {
          const startHour = from_time ? parseInt(from_time.split(':')[0]) : 6;
          const endHour = until_time ? parseInt(until_time.split(':')[0]) : 22;
          
          const defaultTimeSlots = [];
          for (let i = startHour; i <= endHour; i++) {
            defaultTimeSlots.push(`${i.toString().padStart(2, '0')}:00:00`);
          }
          
          // Use the from_date or current date for the default
          const defaultDate = from_date ? from_date : sqlDateFormat(new Date());
          
          dateTimeRanges.push({
            date: defaultDate,
            slots: defaultTimeSlots,
            from: defaultTimeSlots[0],
            until: defaultTimeSlots[defaultTimeSlots.length - 1]
          });
        }
        
        // Also create an aggregate view of all time slots across all dates
        const allTimeSlots = new Set();
        dateTimeRanges.forEach(dateRange => {
          dateRange.slots.forEach(slot => allTimeSlots.add(slot));
        });
        
        const sortedAllTimeSlots = Array.from(allTimeSlots).sort((a, b) => {
          const [aHour, aMin] = a.split(':').map(Number);
          const [bHour, bMin] = b.split(':').map(Number);
          return aHour === bHour ? aMin - bMin : aHour - bHour;
        });
        
        return {
          byDate: dateTimeRanges,
          aggregate: {
            slots: sortedAllTimeSlots,
            from: sortedAllTimeSlots[0] || (from_time || "06:00:00"),
            until: sortedAllTimeSlots[sortedAllTimeSlots.length - 1] || (until_time || "22:00:00")
          }
        };
      };

      // Get time ranges based on available courts
      const timeRanges = generateTimeRanges(processedCourts);

      // Return formatted response
      return res.status(200).json({
        error: false,
        courts: {
          available: processedCourts.filter(court => court.availabilityData.availability.length > 0),
          unavailable: (processedCourts.filter(court => court.availabilityData.unavailability.length > 0)).length > 0 ? processedCourts.filter(court => court.availabilityData.unavailability.length > 0) : null,
          total: courts.length
        },
        hours: {
          available: timeRanges.aggregate.slots,
          total: timeRanges.aggregate.slots.length,
          range: {
            from: timeRanges.aggregate.from,
            until: timeRanges.aggregate.until
          },
          byDate: timeRanges.byDate
        },
        coaches: {
          available: processedCoaches.filter(coach => coach.availabilityData.availability.length > 0),
          unavailable: (processedCoaches.filter(coach => coach.availabilityData.unavailability.length > 0)).length > 0 ? processedCoaches.filter(coach => coach.availabilityData.unavailability.length > 0) : null,
          total: coaches.length
        },
        staff: {
          available: processedStaff.filter(staff => staff.availabilityData.availability.length > 0),
          unavailable: (processedStaff.filter(staff => staff.availabilityData.unavailability.length > 0)).length > 0 ? processedStaff.filter(staff => staff.availabilityData.unavailability.length > 0) : null,
          total: staff.length
        },
        sports: processedSports,
        date_range: {
          from: fromDateSQL,
          until: untilDateSQL
        },
        time_range: {
          from: from_time || "00:00:00",
          until: until_time || "23:59:59"
        }
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


    // Create payment intent
    app.post(`/v3/api/custom/courtmatchup/reservations/payment-intent/create`, combinedMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        const { amount, user_id } = req.body;

        if(!amount || !user_id){
          throw new Error("Amount and user_id are required");
        }
  
        sdk.setTable("user");
        const user = (await sdk.get({ id: user_id }))[0];
  
        let stripe_customer = user.stripe_uid;
        if (!stripe_customer) {
          stripe_customer = (await stripe.createStripeCustomer({ email: user.email })).id;
        }
  
        const paymentIntent = await stripe.createPaymentIntentAutomatic({
          amount: amount * 100,
          currency: "usd",
          customer: stripe_customer,
        });
  
        return res.status(200).json({
          error: false,
          client_secret: paymentIntent.client_secret,
          payment_intent: paymentIntent.id,
        });
      } catch (error) {
        console.log(error);
        return res.status(400).json({ error: true, message: "Something went wrong" });
      }
    });
      // Verify payment status
    app.post(`/v3/api/custom/courtmatchup/reservations/payment/verify`, combinedMiddlewares, async function (req, res) {
      try {
        let sdk = req.sdk;
        sdk.getDatabase();
        sdk.setProjectId(req.projectId);
        const { status, booking_id, payment_intent=null, last_4=0 } = req.body;

        if(payment_intent){

          const paymentStatus = await stripe.retrievePaymentIntent(payment_intent);
          console.log('paymentStatus :>> ', paymentStatus);
    
          if (paymentStatus.status === "succeeded") {
            sdk.setTable("booking");
            await sdk.update({ status: BOOKING_STATUSES.SUCCESS, payment_intent: payment_intent, last_4: last_4 }, booking_id); // Update booking status to success

            // sdk.setTable("payment_history");
            // await sdk.insert({
            //   booking_id,
            //   amount: paymentStatus.amount / 100,
            //   status: paymentStatus.status,
            //   payment_intent: payment_intent,
            //   user_id: req.user_id,
            // });
    
            return res.status(200).json({ error: false, message: "Payment verified successfully" });
          } else {
            sdk.setTable("booking");
            await sdk.update({ status: BOOKING_STATUSES.FAIL, payment_intent: payment_intent, last_4: last_4 }, booking_id); // Update booking status to failed
            // sdk.setTable("payment_history");
            // await sdk.insert({
            //   booking_id,
            //   amount: paymentStatus.amount / 100,
            //   status: paymentStatus.status,
            //   payment_intent: payment_intent,
            //   user_id: req.user_id,
            // });
            return res.status(400).json({ error: true, message: "Payment verification failed" });
          }
          
        }else{

          sdk.setTable("booking");
          await sdk.update({ status }, booking_id); // Update booking status to success
          // sdk.setTable("payment_history");
          // await sdk.insert({
          //   booking_id,
          //   amount: 0,
          //   status: "succeeded",
          //   payment_intent: null,
          //   user_id: req.user_id,
          // });

          return res.status(200).json({ error: false, message: "Payment verified successfully" });
        }
      } catch (error) {
        console.log(error);
        return res.status(400).json({ error: true, message: "Unable to verify payment" });
      }
    });
  


  return [
    {
      method: "GET",
      name: "Category API",
      url: "/v3/api/custom/categories",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
    {
      method: "GET",
      name: "Island API",
      url: "/v3/api/custom/islands",
      successBody: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false, "verify": true }',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [],
      needToken: true,
    },
  ];
};
